import { supabase } from './client';
import type { Prompt, PromptGroup, PromptFile } from '../../types/prompts';
import { logger } from '../../utils/logger';

export const promptsApi = {
  // Prompt groups
  async getPromptGroups() {
    try {
      const { data, error } = await supabase
        .from('prompt_categories')
        .select('*')
        .order('position');
        
      if (error) throw error;
      
      // Transform to match our frontend types
      return data.map(group => ({
        id: group.id,
        title: group.title,
        description: group.description,
        color: group.color,
        prompts: []
      }));
    } catch (error) {
      logger.error('Failed to fetch prompt groups:', error);
      throw error;
    }
  },
  
  async createPromptGroup(group: Omit<PromptGroup, 'id' | 'prompts'>) {
    try {
      const { data, error } = await supabase
        .from('prompt_categories')
        .insert([{
          title: group.title,
          description: group.description,
          color: group.color,
          position: 999 // Will be updated later
        }])
        .select()
        .single();
        
      if (error) throw error;
      
      // Update position based on returned ID
      const { error: updateError } = await supabase
        .from('prompt_categories')
        .update({ position: parseInt(data.id.slice(-4), 16) % 1000 })
        .eq('id', data.id);
        
      if (updateError) logger.warn('Failed to update prompt group position:', updateError);
      
      return {
        id: data.id,
        title: data.title,
        description: data.description,
        color: data.color,
        prompts: []
      };
    } catch (error) {
      logger.error('Failed to create prompt group:', error);
      throw error;
    }
  },
  
  async updatePromptGroup(id: string, updates: Partial<PromptGroup>) {
    try {
      const { data, error } = await supabase
        .from('prompt_categories')
        .update({
          title: updates.title,
          description: updates.description,
          color: updates.color
        })
        .eq('id', id)
        .select()
        .single();
        
      if (error) throw error;
      
      return {
        id: data.id,
        title: data.title,
        description: data.description,
        color: data.color,
        prompts: []
      };
    } catch (error) {
      logger.error('Failed to update prompt group:', error);
      throw error;
    }
  },
  
  async deletePromptGroup(id: string) {
    try {
      const { error } = await supabase
        .from('prompt_categories')
        .delete()
        .eq('id', id);
        
      if (error) throw error;
    } catch (error) {
      logger.error('Failed to delete prompt group:', error);
      throw error;
    }
  },
  
  // Prompts
  async getPrompts(groupId: string) {
    try {
      const { data, error } = await supabase
        .from('prompts')
        .select(`
          *,
          files:prompt_files(*),
          tags:prompt_tags(name)
        `)
        .eq('category_id', groupId)
        .order('position');
        
      if (error) throw error;
      
      // Transform to match our frontend types
      return data.map(prompt => ({
        id: prompt.id,
        title: prompt.title,
        details: prompt.content,
        action: prompt.action_steps ? JSON.stringify(prompt.action_steps) : '',
        files: prompt.files || [],
        tags: prompt.tags ? prompt.tags.map((t: any) => t.name) : [],
        createdAt: new Date(prompt.created_at),
        updatedAt: new Date(prompt.updated_at)
      }));
    } catch (error) {
      logger.error('Failed to fetch prompts:', error);
      throw error;
    }
  },
  
  async createPrompt(groupId: string, prompt: Omit<Prompt, 'id' | 'files' | 'createdAt' | 'updatedAt'>) {
    try {
      // Get count of existing prompts in this group for position
      const { count, error: countError } = await supabase
        .from('prompts')
        .select('*', { count: 'exact', head: true })
        .eq('category_id', groupId);
        
      if (countError) throw countError;
      
      const { data, error } = await supabase
        .from('prompts')
        .insert([{
          category_id: groupId,
          title: prompt.title,
          content: prompt.details,
          action_steps: prompt.action ? JSON.parse(prompt.action) : [],
          tags: prompt.tags,
          position: count || 0
        }])
        .select()
        .single();
        
      if (error) throw error;
      
      return {
        id: data.id,
        title: data.title,
        details: data.content,
        action: JSON.stringify(data.action_steps),
        files: [],
        tags: data.tags || [],
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at)
      };
    } catch (error) {
      logger.error('Failed to create prompt:', error);
      throw error;
    }
  },
  
  async updatePrompt(id: string, updates: Partial<Prompt>) {
    try {
      const { data, error } = await supabase
        .from('prompts')
        .update({
          title: updates.title,
          content: updates.details,
          action_steps: updates.action ? JSON.parse(updates.action) : undefined,
          tags: updates.tags
        })
        .eq('id', id)
        .select()
        .single();
        
      if (error) throw error;
      
      return {
        id: data.id,
        title: data.title,
        details: data.content,
        action: JSON.stringify(data.action_steps),
        files: [], // Files would need to be fetched separately
        tags: data.tags || [],
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at)
      };
    } catch (error) {
      logger.error('Failed to update prompt:', error);
      throw error;
    }
  },
  
  async deletePrompt(id: string) {
    try {
      const { error } = await supabase
        .from('prompts')
        .delete()
        .eq('id', id);
        
      if (error) throw error;
    } catch (error) {
      logger.error('Failed to delete prompt:', error);
      throw error;
    }
  },
  
  // Files
  async addFile(promptId: string, file: Omit<PromptFile, 'id' | 'createdAt'>) {
    try {
      const { data, error } = await supabase
        .from('prompt_files')
        .insert([{
          prompt_id: promptId,
          name: file.name,
          type: file.type,
          content: file.content
        }])
        .select()
        .single();
        
      if (error) throw error;
      
      return {
        id: data.id,
        name: data.name,
        type: data.type as 'pdf' | 'text',
        content: data.content,
        createdAt: new Date(data.created_at)
      };
    } catch (error) {
      logger.error('Failed to add file:', error);
      throw error;
    }
  },
  
  async deleteFile(id: string) {
    try {
      const { error } = await supabase
        .from('prompt_files')
        .delete()
        .eq('id', id);
        
      if (error) throw error;
    } catch (error) {
      logger.error('Failed to delete file:', error);
      throw error;
    }
  }
};