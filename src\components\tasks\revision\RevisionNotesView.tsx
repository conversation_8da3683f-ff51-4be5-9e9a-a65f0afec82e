import React, { useState } from 'react';
import { RevisionNoteEditor } from './RevisionNoteEditor';
import { RevisionItem } from './RevisionItem';
import { Button } from '../../ui/button';
import { Plus, Info, UserPlus, X, Video } from 'lucide-react';
import { useProject } from '../../../contexts/ProjectContext';
import { useRevision } from '../../../contexts/RevisionContext';
import { useCollaboration } from '../../../contexts/CollaborationContext';

export const RevisionNotesView: React.FC = () => {
  const [isCreating, setIsCreating] = useState(false);
  const [isCollaboratorOpen, setIsCollaboratorOpen] = useState(false);
  const [email, setEmail] = useState('');
  const { currentProject } = useProject();
  const { 
    meetLink, 
    generateMeetLink, 
    revisions,
    addNote,
    removeNote,
    markNoteAsProcessed 
  } = useRevision();
  const { 
    collaborators, 
    addCollaborator, 
    startCollaboration 
  } = useCollaboration();
  
  // We need to implement this function since it's missing in the context
  const removeCollaborator = (emailToRemove: string) => {
    // This is a placeholder since the actual function is missing in the context
    console.log('Removing collaborator:', emailToRemove);
    // Normally we would call a function from the context
  };
  
  const handleAddNote = (content: string) => {
    addNote(content);
    setIsCreating(false);
  };
  
  const handleAddCollaborator = () => {
    if (email.trim()) {
      addCollaborator(email.trim());
      setEmail('');
    }
  };

  const handleRemoveCollaborator = (emailToRemove: string) => {
    removeCollaborator(emailToRemove);
  };
  
  const processedNotes = revisions.filter(note => note.isProcessed);
  const unprocessedNotes = revisions.filter(note => !note.isProcessed);
  
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold">Revision Notes</h2>
          <p className="text-gray-500 mt-1">Copy and paste feedback to track revisions</p>
        </div>
        <div className="flex gap-2">
          <Button 
            onClick={() => setIsCreating(true)}
            disabled={isCreating}
            className="bg-black text-white hover:bg-gray-800"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Revision Notes
          </Button>
          <Button
            onClick={() => setIsCollaboratorOpen(!isCollaboratorOpen)}
            className="bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300"
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Collaborators
          </Button>
          <Button
            onClick={generateMeetLink}
            className="bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300"
          >
            <Video className="h-4 w-4 mr-2" />
            Start Meeting
          </Button>
        </div>
      </div>
      
      {/* Collaborators UI */}
      {isCollaboratorOpen && (
        <div className="mb-6 border-2 border-gray-300 p-4 rounded bg-gray-50">
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-bold">Project Collaborators</h3>
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={() => setIsCollaboratorOpen(false)}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex flex-wrap gap-1 mb-3">
            {collaborators.length === 0 ? (
              <p className="text-sm text-gray-500 italic">No collaborators added yet</p>
            ) : (
              collaborators.map(collab => (
                <div 
                  key={collab} 
                  className="inline-flex items-center px-3 py-1 bg-gray-100 rounded text-sm"
                >
                  {collab}
                  <button 
                    onClick={() => handleRemoveCollaborator(collab)}
                    className="ml-2 text-gray-500 hover:text-red-500"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))
            )}
          </div>
          
          <div className="flex gap-2">
            <input
              type="email"
              placeholder="Email address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-l"
            />
            <Button
              onClick={handleAddCollaborator}
              disabled={!email.trim()}
              className="rounded-l-none bg-black text-white hover:bg-gray-800"
            >
              Add Collaborator
            </Button>
          </div>
        </div>
      )}
      
      {isCreating ? (
        <div className="mb-8">
          <RevisionNoteEditor 
            onSave={handleAddNote}
            onCancel={() => setIsCreating(false)}
          />
        </div>
      ) : null}
      
      {revisions.length === 0 && !isCreating ? (
        <div className="bg-blue-50 border-l-4 border-blue-500 p-4 text-blue-700 mb-8">
          <div className="flex">
            <Info className="h-5 w-5 mr-2" />
            <div>
              <p className="font-bold">Getting Started</p>
              <p>Paste revision notes here and send them to specific scenes to track your revisions.</p>
            </div>
          </div>
        </div>
      ) : null}
      
      {unprocessedNotes.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-4 border-b-2 border-black pb-2">
            Pending Notes ({unprocessedNotes.length})
          </h3>
          <div className="space-y-4">
            {unprocessedNotes.map(note => (
              <RevisionItem key={note.id} note={note} />
            ))}
          </div>
        </div>
      )}
      
      {processedNotes.length > 0 && (
        <div>
          <h3 className="text-lg font-bold mb-4 border-b-2 border-gray-300 pb-2">
            Processed Notes ({processedNotes.length})
          </h3>
          <div className="space-y-4">
            {processedNotes.map(note => (
              <RevisionItem key={note.id} note={note} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}; 