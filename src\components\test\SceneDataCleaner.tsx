import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader } from '../ui/card';
import { Trash2, RefreshCw, AlertTriangle, Check } from 'lucide-react';
import { useProject } from '../../contexts/ProjectContext';
import { 
  findAllSceneKeys, 
  purgeAllSceneData, 
  stripScenesFromEpisodeData,
  resetProjectToBlankState,
  getSceneDataStats
} from '../../utils/sceneDataCleaner';

export const SceneDataCleaner: React.FC = () => {
  const { currentProject } = useProject();
  const [sceneKeys, setSceneKeys] = useState<string[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [useEmptyTemplates, setUseEmptyTemplates] = useState<boolean>(() => 
    localStorage.getItem('use_empty_templates') === 'true'
  );
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    count?: number;
  } | null>(null);

  // Load initial data
  useEffect(() => {
    refreshData();
  }, [currentProject?.id]);

  const refreshData = () => {
    setIsLoading(true);
    try {
      // Find scene keys
      const keys = findAllSceneKeys(currentProject?.id);
      setSceneKeys(keys);
      
      // Get scene data stats
      const statsData = getSceneDataStats(currentProject?.id);
      setStats(statsData);
      
      // Check empty templates mode
      setUseEmptyTemplates(localStorage.getItem('use_empty_templates') === 'true');
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePurgeSceneData = () => {
    if (!currentProject) {
      setResult({
        success: false,
        message: 'No project is currently open'
      });
      return;
    }

    if (confirm('⚠️ WARNING: This will permanently delete ALL scene data. This action CANNOT be undone. Continue?')) {
      setIsLoading(true);
      try {
        const count = purgeAllSceneData(currentProject.id);
        setResult({
          success: true,
          message: 'All scene data has been completely purged',
          count
        });
        refreshData();
      } catch (error) {
        setResult({
          success: false,
          message: `Error purging scene data: ${error instanceof Error ? error.message : 'Unknown error'}`
        });
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleStripScenes = () => {
    if (!currentProject) {
      setResult({
        success: false,
        message: 'No project is currently open'
      });
      return;
    }

    setIsLoading(true);
    try {
      const success = stripScenesFromEpisodeData(currentProject.id);
      setResult({
        success,
        message: success 
          ? 'Successfully removed all scenes while preserving episode structure' 
          : 'Failed to strip scenes from episode data'
      });
      refreshData();
    } catch (error) {
      setResult({
        success: false,
        message: `Error stripping scenes: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetToBlank = () => {
    if (!currentProject) {
      setResult({
        success: false,
        message: 'No project is currently open'
      });
      return;
    }

    if (confirm('This will reset your project to a completely blank state. Continue?')) {
      setIsLoading(true);
      try {
        const success = resetProjectToBlankState(currentProject.id);
        setResult({
          success,
          message: success 
            ? 'Project has been reset to a completely blank state' 
            : 'Failed to reset project'
        });
        refreshData();
      } catch (error) {
        setResult({
          success: false,
          message: `Error resetting project: ${error instanceof Error ? error.message : 'Unknown error'}`
        });
      } finally {
        setIsLoading(false);
      }
    }
  };
  
  // Toggle empty templates mode
  const toggleEmptyTemplates = () => {
    const newValue = !useEmptyTemplates;
    setUseEmptyTemplates(newValue);
    localStorage.setItem('use_empty_templates', newValue.toString());
    
    setResult({
      success: true,
      message: newValue 
        ? 'Empty templates enabled. New episodes will have empty scene structures.' 
        : 'Empty templates disabled. New episodes will use the selected template structure.'
    });
  };

  const formatBytes = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' bytes';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + ' KB';
    else return (bytes / 1048576).toFixed(2) + ' MB';
  };

  return (
    <Card className="border-4 border-black">
      <CardHeader className="border-b-4 border-black bg-red-50">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <h2 className="text-xl font-bold text-red-800">Scene Data Cleaner</h2>
          </div>
          <Button 
            variant="ghost" 
            onClick={refreshData}
            disabled={isLoading}
            className="border-2 border-black"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <div className="space-y-6">
          {/* Warning */}
          <div className="border-2 border-red-500 p-4 rounded bg-red-50">
            <h3 className="font-bold text-red-800 mb-2">⚠️ Advanced Tools - Use with Caution</h3>
            <div className="text-sm text-red-700 space-y-2">
              <p>
                These tools can permanently delete scene data. Make sure you understand what each option does before using it.
              </p>
              <div className="flex items-center gap-2 pt-2 border-t border-red-300">
                <input
                  type="checkbox"
                  id="empty-templates-toggle"
                  checked={useEmptyTemplates}
                  onChange={toggleEmptyTemplates}
                  className="form-checkbox h-4 w-4 text-red-600 border-2 border-red-400"
                />
                <label htmlFor="empty-templates-toggle" className="font-medium">
                  Use empty scene structures (maintain template framework)
                </label>
              </div>
              <p className="text-xs text-red-600 italic">
                When enabled, new episodes will be created with empty scene arrays but maintain the template structure.
              </p>
            </div>
          </div>

          {/* Project Info */}
          <div className="border-2 border-black p-4 rounded">
            <h3 className="font-bold mb-2">Project Information</h3>
            {currentProject ? (
              <div className="space-y-1">
                <p><span className="font-medium">ID:</span> {currentProject.id}</p>
                <p><span className="font-medium">Name:</span> {currentProject.name}</p>
                <p><span className="font-medium">Type:</span> {currentProject.type}</p>
              </div>
            ) : (
              <p className="text-gray-500 italic">No project currently open</p>
            )}
          </div>

          {/* Storage Analysis */}
          <div className="border-2 border-black p-4 rounded">
            <h3 className="font-bold mb-2">Scene Data Analysis</h3>
            {stats ? (
              <div className="space-y-1">
                <p><span className="font-medium">Total Keys:</span> {stats.totalKeys}</p>
                <p><span className="font-medium">Total Size:</span> {formatBytes(stats.totalSize)}</p>
                <p><span className="font-medium">Episode Data Size:</span> {formatBytes(stats.episodeDataSize)}</p>
                <p><span className="font-medium">Scene Data Size:</span> {formatBytes(stats.sceneDataSize)}</p>
              </div>
            ) : (
              <p className="text-gray-500 italic">No analysis available</p>
            )}
          </div>

          {/* Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="border-2 border-black p-4 rounded bg-gray-50">
              <h3 className="font-bold mb-2">Option 1: Strip Scenes</h3>
              <p className="text-sm mb-4">
                Removes all scenes from episode data while preserving the episode and act structure.
                This is the safest option.
              </p>
              <Button
                onClick={handleStripScenes}
                disabled={isLoading || !currentProject}
                className="bg-yellow-600 hover:bg-yellow-700 text-white w-full"
              >
                Strip All Scenes
              </Button>
            </div>
            
            <div className="border-2 border-black p-4 rounded bg-gray-50">
              <h3 className="font-bold mb-2">Option 2: Reset to Blank</h3>
              <p className="text-sm mb-4">
                Completely resets the project to a blank state and enables blank templates mode.
                This is recommended for a fresh start.
              </p>
              <Button
                onClick={handleResetToBlank}
                disabled={isLoading || !currentProject}
                className="bg-orange-600 hover:bg-orange-700 text-white w-full"
              >
                Reset to Blank State
              </Button>
            </div>
            
            <div className="border-2 border-black p-4 rounded bg-gray-50">
              <h3 className="font-bold mb-2">Option 3: Purge All Data</h3>
              <p className="text-sm mb-4">
                Completely removes ALL scene-related data from localStorage.
                Most aggressive option - use with extreme caution.
              </p>
              <Button
                onClick={handlePurgeSceneData}
                disabled={isLoading || !currentProject}
                className="bg-red-600 hover:bg-red-700 text-white w-full"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Purge All Scene Data
              </Button>
            </div>
          </div>

          {/* Results */}
          {result && (
            <div className={`border-2 p-4 rounded ${result.success ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}`}>
              <div className="flex items-center gap-2">
                {result.success ? (
                  <Check className="h-5 w-5 text-green-600" />
                ) : (
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                )}
                <div>
                  <p className={`font-bold ${result.success ? 'text-green-800' : 'text-red-800'}`}>
                    {result.success ? 'Success' : 'Error'}
                  </p>
                  <p className="text-sm">
                    {result.message}
                    {result.count !== undefined && ` (${result.count} items affected)`}
                  </p>
                </div>
              </div>
              {result.success && (
                <div className="mt-2 p-2 bg-yellow-50 border-l-4 border-yellow-500 text-sm">
                  <p className="font-bold text-yellow-800">Important:</p>
                  <p className="text-yellow-700">Refresh the page to see the changes take effect.</p>
                </div>
              )}
            </div>
          )}

          {/* Key List */}
          <div className="border-2 border-black p-4 rounded">
            <h3 className="font-bold mb-2">Scene-Related Keys ({sceneKeys.length})</h3>
            {sceneKeys.length > 0 ? (
              <div className="max-h-40 overflow-y-auto">
                <ul className="text-xs font-mono space-y-1">
                  {sceneKeys.map(key => (
                    <li key={key} className="truncate hover:text-blue-600">
                      {key}
                    </li>
                  ))}
                </ul>
              </div>
            ) : (
              <p className="text-gray-500 italic">No scene-related keys found</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};