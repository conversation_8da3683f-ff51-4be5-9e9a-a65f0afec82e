import React, { useState } from 'react';
import { Hash, Plus, Edit, Check } from 'lucide-react';
import { Button } from '../../ui/button';

interface SceneHeaderProps {
  sceneId: string;
  title: string;
  tags: string[];
  onAddTag: () => void;
  onClose: () => void;
  onTitleChange: (title: string) => void;
  collapsed?: boolean;
  sceneOrder?: number;
}

export const SceneHeader: React.FC<SceneHeaderProps> = ({
  sceneId,
  title,
  tags = [],
  onAddTag,
  onClose,
  onTitleChange,
  collapsed = false,
  sceneOrder
}) => {
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [editTitle, setEditTitle] = useState(title);

  const handleTitleSave = () => {
    if (editTitle.trim() && editTitle.trim() !== title) {
      onTitleChange(editTitle.trim());
    }
    setIsEditingTitle(false);
  };

  return (
    <div className="flex-1">
      <div className="flex items-center gap-2">
        {isEditingTitle ? (
          <div className="flex items-center gap-2">
            <input
              type="text"
              value={editTitle}
              onChange={(e) => setEditTitle(e.target.value)}
              className="font-mono text-xl font-bold border-2 border-black px-2 py-1"
              autoFocus
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleTitleSave();
                if (e.key === 'Escape') {
                  setEditTitle(title);
                  setIsEditingTitle(false);
                }
              }}
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={handleTitleSave}
              className="h-8 w-8 p-1 hover:bg-gray-200"
            >
              <Check className="h-4 w-4" />
            </Button>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <h2 className="font-mono text-xl font-bold">{sceneOrder ? `Scene ${sceneOrder}` : title}</h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditingTitle(true)}
              className="h-8 w-8 p-1 hover:bg-gray-200"
            >
              <Edit className="h-4 w-4" />
            </Button>
          </div>
        )}
        <span className="px-2 py-1 bg-gray-200 text-sm font-mono rounded">Fountain</span>
      </div>
      {!collapsed && (
        <div className="flex gap-4 items-center flex-wrap mb-2">
          <span className="font-mono text-sm text-gray-600">Scene {sceneId}</span>
          <div className="flex gap-2 flex-wrap w-full min-w-0" style={{ position: 'relative', zIndex: 30 }}>
            {tags.map((tag, index) => (
              <span 
                key={index} 
                className="px-3 py-1 bg-gray-100 border-2 border-black font-mono text-sm flex items-center gap-1 max-w-xs truncate"
                style={{ display: 'flex', position: 'relative' }}
              >
                <Hash className="h-3 w-3" />
                <span className="truncate">{tag}</span>
              </span>
            ))}
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={onAddTag}
              className="border-2 border-black"
            >
              <Plus className="h-3 w-3" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};