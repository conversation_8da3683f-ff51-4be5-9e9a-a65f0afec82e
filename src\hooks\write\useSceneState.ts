import { useState, useCallback, useEffect } from 'react';
import { logger } from '../../utils/logger';
import { getProjectScopedKey } from '../../utils/storageUtils';
import type { Scene, SceneNote, NoteType, RevisionState } from '../../types/write';
import { useProject } from '../../contexts/ProjectContext';

const DEFAULT_BEATS = [
  { id: '1', title: 'SETUP', notes: '', color: 'bg-blue-500' },
  { id: '2', title: 'CONFLICT', notes: '', color: 'bg-red-500' },
  { id: '3', title: 'ESCALATION', notes: '', color: 'bg-yellow-500' },
  { id: '4', title: 'CLIMAX', notes: '', color: 'bg-purple-500' },
  { id: '5', title: 'RESOLUTION', notes: '', color: 'bg-green-500' }
];

const DEFAULT_CHARACTERS = [
  { id: '1', name: 'PROTAGONIST', isActive: true },
  { id: '2', name: 'ANTAGONIST', isActive: false },
  { id: '3', name: 'SUPPORTING', isActive: false }
];

export function useSceneState(sceneId: string) {
  const { currentProject } = useProject();
  const projectId = currentProject?.id;
  
  const [scene, setScene] = useState<Scene | null>(() => {
    try {
      // Get scene data from localStorage
      // Always use project-scoped key to prevent data leakage between projects
      const savedData = localStorage.getItem(getProjectScopedKey(projectId, `scene_${sceneId}`));
      return savedData ? JSON.parse(savedData) : null;
    } catch {
      return null;
    }
  });

  const [tags, setTags] = useState<string[]>(['confrontation', 'revelation', 'mystery']);
  const [isLoading, setIsLoading] = useState(false);

  // Load scene data when sceneId changes
  useEffect(() => {
    setIsLoading(true);
    logger.debug('Loading scene data for:', { sceneId, projectId });
    
    try {
      // Try exact project-scoped key first
      const key = getProjectScopedKey(projectId, `scene_${sceneId}`);
      let savedData = localStorage.getItem(key);
      
      // If not found and we have a project ID, try to find legacy data
      if (!savedData && projectId && projectId !== 'default-project') {
        const allKeys = Object.keys(localStorage).filter(k => 
          k.startsWith(getProjectScopedKey(projectId, 'scene_'))
        );
        logger.debug('Available scene keys:', { allKeys: allKeys.slice(0, 20) });
        
        // Parse the scene ID to get the components
        const parts = sceneId.split('-');
        if (parts.length >= 3) {
          const episodeId = parts[0];
          const actId = parts[1];
          const sceneNumber = parts[2];
          
          // Try alternative formats that might have been stored
          logger.debug('Attempting to find scene by components:', { episodeId, actId, sceneNumber });
          
          // Try legacy key format
          const legacyKey = `write_scene_${sceneId}`;
          const legacyData = localStorage.getItem(legacyKey);
          
          if (legacyData) {
            logger.debug('Found legacy scene data:', { legacyKey });
            savedData = legacyData;
            
            // Migrate to project-scoped key
            localStorage.setItem(key, legacyData);
            logger.debug('Migrated scene data to project-scoped key:', { from: legacyKey, to: key });
            
            // IMPORTANT: Verify the loaded data has the correct sceneId
            try {
              const parsedData = JSON.parse(savedData || '{}');
              if (parsedData.id && parsedData.id !== parseInt(sceneNumber) && parsedData.id !== sceneNumber) {
                logger.warn('Found scene data with mismatched ID:', { 
                  requestedId: sceneNumber,
                  foundId: parsedData.id
                });
                
                // Fix the ID to match the requested scene
                parsedData.id = parseInt(sceneNumber);
                savedData = JSON.stringify(parsedData);
                
                // Update storage with corrected ID
                localStorage.setItem(key, savedData);
                logger.debug('Updated scene data with correct ID');
              }
            } catch (e) {
              logger.error('Error verifying scene data ID:', e);
            }
          }
        }
      }
      
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        
        // Handle legacy notes if they don't have the new type field
        if (parsedData.notes && Array.isArray(parsedData.notes)) {
          parsedData.notes = parsedData.notes.map((note: any) => {
            if (!note.type) {
              return { ...note, type: 'general', isResolved: false };
            }
            return note;
          });
        }
        
        // Handle legacy isComplete property if revisionState is not set
        if (parsedData.isComplete !== undefined && parsedData.revisionState === undefined) {
          parsedData.revisionState = parsedData.isComplete ? 'complete' : 'not-started';
        }
        
        // Ensure the ID matches the requested scene ID
        const idParts = sceneId.split('-');
        if (idParts.length >= 3) {
          const requestedSceneNumber = idParts[2];
          if (parsedData.id !== parseInt(requestedSceneNumber) && parsedData.id !== requestedSceneNumber) {
            // This is the wrong scene data - the IDs don't match
            logger.warn('Scene data ID mismatch - discarding:', { 
              requestedId: requestedSceneNumber,
              foundId: parsedData.id
            });
            savedData = null;
            setScene(null);
          } else {
            setScene(parsedData);
            logger.debug('Loaded existing scene data:', { 
              sceneId, 
              hasContent: Boolean(parsedData.content), 
              contentPreview: parsedData.content?.substring(0, 50) 
            });
          }
        } else {
          setScene(parsedData);
          logger.debug('Loaded existing scene data (ID format unusual):', { 
            sceneId, 
            hasContent: Boolean(parsedData.content), 
            contentPreview: parsedData.content?.substring(0, 50) 
          });
        }
      } else {
        setScene(null);
        logger.debug('No existing scene data found:', { sceneId });
      }
    } catch (error) {
      logger.error('Failed to load scene data:', error);
      setScene(null);
    } finally {
      setIsLoading(false);
    }
  }, [sceneId, projectId]);

  // Save scene whenever it changes
  useEffect(() => {
    if (scene) {
      const storageKey = getProjectScopedKey(projectId, `scene_${sceneId}`);
      try {
        localStorage.setItem(storageKey, JSON.stringify({
          ...scene,
          lastEdited: new Date().toISOString()
        }));
        logger.debug('Scene saved:', { sceneId, projectId, storageKey });
        
        // Sync with episode data
        syncWithEpisodeData(scene);
      } catch (error) {
        logger.error('Failed to save scene:', error);
      }
    }
  }, [scene, sceneId, projectId]);

  // Function to sync scene data with episode data
  const syncWithEpisodeData = useCallback((sceneData: Scene) => {
    try {
      // Always use a project ID (default or provided)
      const effectiveProjectId = projectId || 'default-project';
      if (effectiveProjectId === 'default-project') {
        logger.warn('Using default project ID for scene sync - data may not persist correctly');
        return;
      }
      
      // Parse the scene ID to get episode, act, and scene IDs
      const parts = sceneId.split('-');
      if (parts.length < 3) return;
      
      const episodeId = parseInt(parts[0]);
      const actId = parts[1];
      const sceneIdNum = parseInt(parts[2]);
      
      // Get episode data from localStorage
      const episodeDataStr = localStorage.getItem(getProjectScopedKey(effectiveProjectId, 'episode_data'));
      if (!episodeDataStr) return;
      
      const episodeData = JSON.parse(episodeDataStr);
      if (!episodeData[episodeId]) return;
      
      // Find the scene in episode data
      const act = episodeData[episodeId].acts.find((a: any) => a.id === actId);
      if (!act) return;
      
      const sceneInEpisode = act.scenes.find((s: any) => s.id === sceneIdNum);
      if (!sceneInEpisode) return;
      
      // Update the scene content and title in episode data
      sceneInEpisode.content = sceneData.content;
      sceneInEpisode.title = sceneData.title;
      
      // Also sync the revision state if it's available
      if (sceneData.revisionState !== undefined) {
        sceneInEpisode.revisionState = sceneData.revisionState;
        // For backward compatibility
        sceneInEpisode.isComplete = sceneData.revisionState === 'complete';
      }
      
      // Save updated episode data
      localStorage.setItem(getProjectScopedKey(effectiveProjectId, 'episode_data'), JSON.stringify(episodeData));
      
      // Dispatch a storage event to notify other components
      window.dispatchEvent(new StorageEvent('storage', {
        key: getProjectScopedKey(effectiveProjectId, 'episode_data'),
        newValue: JSON.stringify(episodeData),
      }));
    } catch (error) {
      logger.error('Failed to sync with episode data:', error);
    }
  }, [sceneId, projectId]);

  const initializeScene = useCallback((newScene: Partial<Scene>) => {
    logger.debug('Initializing scene', { sceneId, scene: newScene });
    
    const storageKey = getProjectScopedKey(projectId, `scene_${sceneId}`);
    
    // Check if scene already exists to prevent duplication
    const existingData = localStorage.getItem(storageKey);
    if (existingData) {
      try {
        const parsedData = JSON.parse(existingData);
        logger.debug('Scene already exists, not initializing again:', { sceneId });
        setScene(parsedData);
        return;
      } catch (error) {
        logger.error('Failed to parse existing scene data:', error);
      }
    }
    
    const initialScene: Scene = {
      id: sceneId,
      title: newScene.title || 'New Scene',
      content: newScene.content || '',
      notes: newScene.notes || [],
      characters: newScene.characters || DEFAULT_CHARACTERS,
      emotionalStart: 'Neutral',
      emotionalEnd: 'Neutral',
      emotionalProgress: 50,
      isPositiveArc: true,
      beats: DEFAULT_BEATS,
      isComplete: false,
      revisionState: 'not-started',
      lastEdited: new Date().toISOString()
    };

    setScene(initialScene);
    localStorage.setItem(storageKey, JSON.stringify(initialScene));
    
    // Sync with episode data
    syncWithEpisodeData(initialScene);
  }, [sceneId, syncWithEpisodeData, projectId]);

  const updateScene = useCallback((updates: Partial<Scene>) => {
    logger.debug('Updating scene', { sceneId, updates });
    setScene(prev => {
      if (!prev) {
        logger.warn('Attempted to update non-existent scene:', { sceneId, projectId });
        return null;
      }
      
      const updated = { 
        ...prev, 
        ...updates,
        lastEdited: new Date().toISOString()
      };
      
      // If updating revision state, keep isComplete in sync for backward compatibility
      if (updates.revisionState !== undefined) {
        updated.isComplete = updates.revisionState === 'complete';
      }
      
      // Save to localStorage
      const storageKey = getProjectScopedKey(projectId, `scene_${sceneId}`);
      localStorage.setItem(storageKey, JSON.stringify(updated));
      logger.debug('Scene updated in storage:', { storageKey });
      
      // Sync with episode data if content, title, or revision state changed
      if (updates.content !== undefined || updates.title !== undefined || updates.revisionState !== undefined) {
        syncWithEpisodeData(updated);
      }
      
      return updated;
    });
  }, [sceneId, syncWithEpisodeData, projectId]);

  const toggleSceneComplete = useCallback(() => {
    updateScene({ 
      isComplete: scene ? !scene.isComplete : false,
      revisionState: scene?.isComplete ? 'not-started' : 'complete'
    });
  }, [scene, updateScene]);

  const setRevisionState = useCallback((state: RevisionState) => {
    updateScene({ revisionState: state, isComplete: state === 'complete' });
  }, [updateScene]);

  const addTag = useCallback(() => {
    const tag = prompt('Enter new tag:');
    if (tag) {
      setTags(prev => [...prev, tag]);
    }
  }, []);

  const addNote = useCallback((content: string, type: NoteType = 'general') => {
    const newNote: SceneNote = {
      id: Date.now().toString(),
      user: 'User',
      content,
      time: new Date().toLocaleString(),
      type,
      isResolved: false
    };
    updateScene({
      notes: scene ? [...scene.notes, newNote] : [newNote]
    });
  }, [scene, updateScene]);

  const deleteNote = useCallback((noteId: string) => {
    if (!scene) return;
    
    updateScene({
      notes: scene.notes.filter(note => note.id !== noteId)
    });
  }, [scene, updateScene]);

  const resolveNote = useCallback((noteId: string, isResolved: boolean) => {
    if (!scene) return;
    
    updateScene({
      notes: scene.notes.map(note => 
        note.id === noteId ? { ...note, isResolved } : note
      )
    });
  }, [scene, updateScene]);

  const addCollaborator = useCallback((noteId: string, email: string) => {
    if (!scene) return;
    
    updateScene({
      notes: scene.notes.map(note => 
        note.id === noteId 
          ? { 
              ...note, 
              collaborators: [...(note.collaborators || []), email]
            } 
          : note
      )
    });
  }, [scene, updateScene]);
  
  const removeCollaborator = useCallback((noteId: string, email: string) => {
    if (!scene) return;
    
    updateScene({
      notes: scene.notes.map(note => 
        note.id === noteId 
          ? { 
              ...note, 
              collaborators: (note.collaborators || []).filter(e => e !== email)
            } 
          : note
      )
    });
  }, [scene, updateScene]);

  const deleteScene = useCallback(() => {
    const storageKey = getProjectScopedKey(projectId, `scene_${sceneId}`);
    localStorage.removeItem(storageKey);
    setScene(null);
    logger.debug('Scene deleted:', { sceneId, storageKey });
  }, [sceneId, projectId]);

  return {
    scene,
    tags,
    notes: scene?.notes || [],
    isLoading,
    initializeScene,
    updateScene,
    deleteScene,
    toggleSceneComplete,
    setRevisionState,
    addTag,
    addNote,
    deleteNote,
    resolveNote,
    addCollaborator,
    removeCollaborator
  };
}