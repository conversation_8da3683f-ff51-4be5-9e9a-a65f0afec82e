import React, { useState } from 'react';
import { filmTemplate } from '../../constants/templates';
import { useEpisodeState } from '../../hooks/useEpisodeState';
import { cn } from '../../lib/utils';
import { AlertTriangle, Plus, ChevronRight, Pencil } from 'lucide-react';
import { Button } from '../ui/button';
import { useNavigation } from '../../contexts/NavigationContext';
import { useEpisodeTrackStore } from '../../store/episodeTrackStore';
import { SceneCard } from './SceneCard';

export const FilmEpisodesView: React.FC = () => {
  const {
    episodeData,
    deletedActs,
    hiddenActs,
    updateEpisodeData,
    deleteAct,
    undoDeleteAct,
    toggleActVisibility,
    updateSceneStoryline,
    getActColor
  } = useEpisodeState();
  
  const { setCurrentView } = useNavigation();
  const [draggedScene, setDraggedScene] = useState<{ id: number, actId: string } | null>(null);

  // Get storylines from the store
  const storylines = useEpisodeTrackStore(state => state.storylines);

  // Film projects only have one episode (episode 1)
  const episodeId = 1;
  const episode = episodeData[episodeId];
  const [errorMessage, setErrorMessage] = React.useState<string | null>(null);

  if (!episode) {
    return (
      <div className="flex justify-center items-center h-full">
        <p>Film sections are not initialized. Please open the film project first.</p>
      </div>
    );
  }

  const handleDragStart = (e: React.DragEvent, scene: any, actId: string, index: number) => {
    setDraggedScene({ id: scene.id, actId });
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, targetActId: string, targetIndex: number) => {
    e.preventDefault();
    
    if (!draggedScene) return;
    
    // Get source and target acts
    const sourceAct = episode.acts.find(a => a.id === draggedScene.actId);
    const targetAct = episode.acts.find(a => a.id === targetActId);
    
    if (!sourceAct || !targetAct) return;
    
    // Find the scene to move
    const sceneToMove = sourceAct.scenes.find(s => s.id === draggedScene.id);
    
    if (!sceneToMove) return;
    
    // Update episode data by removing scene from source act and adding to target act
    updateEpisodeData(episodeId, data => {
      const updatedActs = data.acts.map(act => {
        if (act.id === draggedScene.actId) {
          // Remove scene from source act
          return {
            ...act,
            scenes: act.scenes.filter(s => s.id !== draggedScene.id)
          };
        } else if (act.id === targetActId) {
          // Add scene to target act at the correct position
          const newScenes = [...act.scenes];
          newScenes.splice(targetIndex, 0, sceneToMove);
          return {
            ...act,
            scenes: newScenes
          };
        }
        return act;
      });
      
      return {
        ...data,
        acts: updatedActs
      };
    });
    
    setDraggedScene(null);
  };

  return (
    <div className="h-[calc(100vh-4rem)] overflow-hidden flex flex-col w-full">
      {errorMessage && (
        <div className="sticky top-0 z-50 bg-red-100 border-b-4 border-black p-2 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4 text-red-700" />
            <span className="text-sm text-red-800">{errorMessage}</span>
          </div>
          <Button
            onClick={() => setErrorMessage(null)}
            className="text-sm bg-red-200 hover:bg-red-300 text-red-900"
          >
            Dismiss
          </Button>
        </div>
      )}

      {deletedActs && (
        <div className="sticky top-0 z-50 bg-yellow-100 border-b-4 border-black p-2 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4 text-yellow-700" />
            <span className="text-sm text-yellow-800">
              Act "{deletedActs.acts[0].title}" was deleted
            </span>
          </div>
          <Button
            onClick={undoDeleteAct}
            className="text-sm bg-yellow-200 hover:bg-yellow-300 text-yellow-900"
          >
            Undo Delete
          </Button>
        </div>
      )}

      {/* Top navigation bar with write icon */}
      <div className="bg-gray-100 border-b border-gray-300 py-1 px-2 flex items-center justify-between">
        <div className="flex items-center">
          <ChevronRight className="h-4 w-4 mr-1" />
          <span className="text-sm font-medium">Episode Track</span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setCurrentView('write')}
          className="flex items-center gap-1 text-xs"
          title="Go to Write View"
        >
          <Pencil className="h-4 w-4" />
          <span>Write</span>
        </Button>
      </div>

      {/* Section Headers - keeping only this one row */}
      <div className="flex border-b border-gray-300 w-full sticky top-0 z-10 bg-white">
        {filmTemplate.map((section) => (
          <div 
            key={`header-${section.id}`}
            className={cn(
              "flex-shrink-0 px-2 py-1 flex items-center justify-between",
              section.color, 
              "text-white font-bold text-md",
              "w-1/4" // 4 equal columns for film template
            )}
          >
            <span className="truncate">{section.title}</span>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 hover:bg-white/10 text-white"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        ))}
      </div>

      {/* Scrollable content */}
      <div className="flex-1 overflow-x-auto overflow-y-auto w-full pb-12">
        <div className="flex h-full w-full" style={{ minWidth: '100%', display: 'grid', gridTemplateColumns: 'repeat(4, minmax(0, 1fr))' }}>
          {filmTemplate.map((sectionTemplate) => {
            // Find the corresponding act in the episode data
            const act = episode.acts.find(a => a.id === sectionTemplate.id);
            
            if (!act) return null;
            
            const actColor = getActColor(act.id);
            
            return (
              <div 
                key={act.id} 
                className="px-1 pt-1 pb-1 border-r border-gray-200 last:border-r-0 w-full"
                onDragOver={handleDragOver}
              >
                <div className="space-y-2 bg-white">
                  {Array.isArray(act.scenes) && act.scenes.length > 0 ? (
                    <div className="space-y-2">
                      {/* Display each scene in a row */}
                      {act.scenes.map((scene, index) => (
                        <SceneCard
                          key={scene.id}
                          scene={scene}
                          index={index}
                          groupId={act.id}
                          color={actColor}
                          storylineId={scene.storylineId}
                          onUpdate={updates => updateEpisodeData(episodeId, data => ({
                            ...data,
                            acts: data.acts.map(a =>
                              a.id === act.id
                                ? {
                                    ...a,
                                    scenes: a.scenes.map(s =>
                                      s.id === scene.id ? { ...s, ...updates } : s
                                    )
                                  }
                                : a
                            )
                          }))}
                          onDelete={() => updateEpisodeData(episodeId, data => ({
                            ...data,
                            acts: data.acts.map(a =>
                              a.id === act.id
                                ? {
                                    ...a,
                                    scenes: a.scenes.filter(s => s.id !== scene.id)
                                  }
                                : a
                            )
                          }))}
                          onDragStart={(e) => handleDragStart(e, scene, act.id, index)}
                          onDragOver={(e) => handleDragOver(e)}
                          onDrop={(e) => handleDrop(e, act.id, index)}
                          onStorylineChange={storylineId => updateEpisodeData(episodeId, data => ({
                            ...data,
                            acts: data.acts.map(a =>
                              a.id === act.id
                                ? {
                                    ...a,
                                    scenes: a.scenes.map(s =>
                                      s.id === scene.id ? { ...s, storylineId } : s
                                    )
                                  }
                                : a
                            )
                          }))}
                          isEpisodeExpanded={false}
                        />
                      ))}
                    </div>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        updateEpisodeData(episodeId, data => ({
                          ...data,
                          acts: data.acts.map(a => 
                            a.id === act.id 
                              ? {
                                  ...a,
                                  scenes: [
                                    ...a.scenes,
                                    {
                                      id: Date.now(),
                                      title: `Scene 1`,
                                      content: '',
                                      storylineId: null
                                    }
                                  ]
                                }
                              : a
                          )
                        }));
                      }}
                      className="w-full border-dashed text-xs text-gray-500 hover:bg-gray-50 py-1"
                    >
                      <Plus className="h-3 w-3 mr-1" /> Add Scene
                    </Button>
                  )}
                  {/* Always show the Add Scene button after existing scenes */}
                  {Array.isArray(act.scenes) && act.scenes.length > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        updateEpisodeData(episodeId, data => ({
                          ...data,
                          acts: data.acts.map(a => 
                            a.id === act.id 
                              ? {
                                  ...a,
                                  scenes: [
                                    ...a.scenes,
                                    {
                                      id: Date.now() + a.scenes.length,
                                      title: `Scene ${a.scenes.length + 1}`,
                                      content: '',
                                      storylineId: null
                                    }
                                  ]
                                }
                              : a
                          )
                        }));
                      }}
                      className="w-full border-dashed text-xs text-gray-500 hover:bg-gray-50 py-1"
                    >
                      <Plus className="h-3 w-3 mr-1" /> Add Scene
                    </Button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
      
      {/* Color Legend at bottom */}
      <div className="border-t border-gray-300 py-1 px-4 bg-gray-100 fixed bottom-0 left-0 right-0">
        <div className="flex items-center">
          <span className="text-xs font-medium mr-2">Color Legend</span>
          <div className="flex items-center gap-2">
            {storylines.map(storyline => (
              <div key={storyline.id} className="flex items-center">
                <div 
                  className="w-3 h-3 rounded-full mr-1" 
                  style={{ backgroundColor: storyline.color }}
                ></div>
                <span className="text-xs">{storyline.title}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Temporary mock data for storylines
const storylines = [
  { id: 'storyline1', title: 'Main Plot', color: '#3b82f6' },
  { id: 'storyline2', title: 'Subplot A', color: '#ef4444' },
  { id: 'storyline3', title: 'Subplot B', color: '#10b981' },
];