import React from 'react';
import { cn } from '../../../lib/utils';
import { useProject } from '../../../contexts/ProjectContext';

interface EpisodeSpineButtonProps {
  episodeNumber: number;
  isOpen: boolean;
  onToggle: (episodeNumber: number) => void;
  totalEpisodes?: number;
}

export const EpisodeSpineButton: React.FC<EpisodeSpineButtonProps> = ({
  episodeNumber,
  isOpen,
  onToggle,
  totalEpisodes = 1,
}) => {
  // Get project type to determine label
  const { currentProject } = useProject();
  const projectType = currentProject?.type || 'tv-30';
  
  // Determine the label based on project type
  const label = projectType === 'novel' 
    ? `CH_${String(episodeNumber).padStart(2, '0')}` 
    : `EP_${String(episodeNumber).padStart(2, '0')}`;
  
  return (
    <button
      onClick={() => onToggle(episodeNumber)}
      className={cn(
        'episode-spine-button',
        'transition-colors duration-200',
        isOpen ? 'bg-[#00FFF0]' : 'bg-white hover:bg-gray-100'
      )}
    >
      {label}
    </button>
  );
};