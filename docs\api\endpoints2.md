```typescript
GET /api/projects/:projectId/ideas
Params: { projectId: string }
Query: PaginationParams & {
  category?: string;
  status?: string;
  tags?: string[];
  search?: string;
}
Response: ApiResponse<PaginatedResponse<IdeaItem>>
```

### Create Idea
```typescript
POST /api/projects/:projectId/ideas
Params: { projectId: string }
Body: {
  title: string;
  content: string;
  type: 'note' | 'image' | 'voice' | 'link' | 'reference';
  category: string;
  tags?: string[];
  source?: string;
  metadata?: Record<string, any>;
}
Response: ApiResponse<IdeaItem>
```

### Update Idea
```typescript
PUT /api/projects/:projectId/ideas/:id
Params: {
  projectId: string;
  id: string;
}
Body: Partial<IdeaItem>
Response: ApiResponse<IdeaItem>
```

### Delete Idea
```typescript
DELETE /api/projects/:projectId/ideas/:id
Params: {
  projectId: string;
  id: string;
}
Response: ApiResponse<void>
```

### Add Tag to Idea
```typescript
POST /api/projects/:projectId/ideas/:id/tags
Params: {
  projectId: string;
  id: string;
}
Body: {
  tag: string;
}
Response: ApiResponse<{
  ideaId: string;
  tag: string;
}>
```

### Remove Tag from Idea
```typescript
DELETE /api/projects/:projectId/ideas/:id/tags/:tag
Params: {
  projectId: string;
  id: string;
  tag: string;
}
Response: ApiResponse<void>
```

### Add Project Reference
```typescript
POST /api/projects/:projectId/ideas/:id/references
Params: {
  projectId: string;
  id: string;
}
Body: {
  projectId: string;
  projectName: string;
  usage: string;
}
Response: ApiResponse<{
  ideaId: string;
  projectId: string;
  usage: string;
}>
```

### Remove Project Reference
```typescript
DELETE /api/projects/:projectId/ideas/:id/references/:referenceId
Params: {
  projectId: string;
  id: string;
  referenceId: string;
}
Response: ApiResponse<void>
```

## Tasks API

### Get Task Columns
```typescript
GET /api/projects/:projectId/tasks/columns
Params: { projectId: string }
Response: ApiResponse<{
  id: string;
  title: string;
  color: string;
  tasks: Task[];
}[]>
```

### Create Task
```typescript
POST /api/projects/:projectId/tasks
Params: { projectId: string }
Body: {
  columnId: string;
  title: string;
  content: string;
  labels?: string[];
}
Response: ApiResponse<Task>
```

### Update Task
```typescript
PUT /api/projects/:projectId/tasks/:id
Params: {
  projectId: string;
  id: string;
}
Body: Partial<Task>
Response: ApiResponse<Task>
```

### Move Task
```typescript
POST /api/projects/:projectId/tasks/:id/move
Params: {
  projectId: string;
  id: string;
}
Body: {
  targetColumnId: string;
  position: number;
}
Response: ApiResponse<Task>
```

### Add Comment to Task
```typescript
POST /api/projects/:projectId/tasks/:id/comments
Params: {
  projectId: string;
  id: string;
}
Body: {
  content: string;
}
Response: ApiResponse<{
  id: string;
  content: string;
  author: string;
  timestamp: number;
}>
```

### Delete Comment from Task
```typescript
DELETE /api/projects/:projectId/tasks/:id/comments/:commentId
Params: {
  projectId: string;
  id: string;
  commentId: string;
}
Response: ApiResponse<void>
```

## Write API

### Get Scene Content
```typescript
GET /api/projects/:projectId/scenes/:id
Params: {
  projectId: string;
  id: string;
}
Response: ApiResponse<{
  content: string;
  characters: Character[];
  emotionalArc: {
    start: string;
    end: string;
    progress: number;
    isPositive: boolean;
  };
  beats: SceneBeat[];
}>
```

### Update Scene Content
```typescript
PUT /api/projects/:projectId/scenes/:id
Params: {
  projectId: string;
  id: string;
}
Body: {
  content?: string;
  characters?: Character[];
  emotionalArc?: {
    start: string;
    end: string;
    progress: number;
    isPositive: boolean;
  };
  beats?: SceneBeat[];
}
Response: ApiResponse<Scene>
```

### Add Scene Note
```typescript
POST /api/projects/:projectId/scenes/:id/notes
Params: {
  projectId: string;
  id: string;
}
Body: {
  content: string;
}
Response: ApiResponse<Note>
```

### Delete Scene Note
```typescript
DELETE /api/projects/:projectId/scenes/:id/notes/:noteId
Params: {
  projectId: string;
  id: string;
  noteId: string;
}
Response: ApiResponse<void>
```

### Get Scene History
```typescript
GET /api/projects/:projectId/scenes/:id/history
Params: {
  projectId: string;
  id: string;
}
Response: ApiResponse<{
  versions: Array<{
    id: string;
    content: string;
    createdAt: Date;
    createdBy: string;
  }>;
}>
```

### Restore Scene Version
```typescript
POST /api/projects/:projectId/scenes/:id/restore/:versionId
Params: {
  projectId: string;
  id: string;
  versionId: string;
}
Response: ApiResponse<Scene>
```