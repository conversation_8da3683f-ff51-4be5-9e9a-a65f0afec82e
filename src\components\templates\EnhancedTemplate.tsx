import React from 'react';
import { MainNavigation } from '../layout/MainNavigation';
import { TimelineSection } from '../timeline/TimelineSection';
import { WorkshopView } from '../workshop/WorkshopView';
import { EpisodesView } from '../episodes/EpisodesView';
import { WriteView } from '../views/WriteView';
import { PromptsView } from '../views/PromptsView';
import { TaskView } from '../views/TaskView';
import { TestView } from '../views/TestView';
import { TimelineView } from '../views/TimelineView';
import { ChatWindow } from '../chat/ChatWindow';
import { useEpisodeTrackStore } from '../../store/episodeTrackStore';
import { ColorLegend } from '../timeline/ColorLegend';
import { ColorLegendToggle } from '../ui/ColorLegendToggle';
import { ChatWindowContainer } from '../chat/ChatWindowContainer';
import type { ViewType } from '../../constants/navigation';
import { EpisodeSpineProvider } from '../../contexts/EpisodeSpineContext';

const TIMELINE_VIEWS = ['workshop', 'episodes'] as const;

interface EnhancedTemplateProps {
  activeView: ViewType;
  onViewChange: (view: ViewType) => void;
  onHomeClick: () => void;
  onTemplateChange: () => void;
  template: 'classic' | 'enhanced';
}

export const EnhancedTemplate: React.FC<EnhancedTemplateProps> = ({
  activeView,
  onViewChange,
  onHomeClick,
  onTemplateChange,
  template
}) => {
  const showTimeline = TIMELINE_VIEWS.includes(activeView as any);
  const { episodeTrack } = useEpisodeTrackStore();

  const showColorLegend = ['workshop', 'timeline'].includes(activeView);
  const showCollapsed = !(activeView === 'workshop');

  const renderView = () => {
    switch (activeView) {
      case 'workshop':
        return <WorkshopView />;
      case 'episodes':
        return <EpisodesView />;
      case 'timeline':
        return <TimelineView />;
      case 'write':
        return <WriteView />;
      case 'prompts':
        return <PromptsView />;
      case 'tasks':
        return <TaskView />;
      case 'test':
        return <TestView />;
      default:
        return <WorkshopView />;
    }
  };

  return (
    <EpisodeSpineProvider>
      {/* Navigation bar outside the scale container to ensure it's always visible */}
      <MainNavigation
        activeView={activeView}
        onViewChange={onViewChange}
        onHomeClick={onHomeClick}
        onTemplateChange={onTemplateChange}
        template={template}
      />

      {/* Content inside scale container for proper zooming */}
      <div className="scale-container">
        <div className="min-h-screen bg-gray-100 flex flex-col w-full">
          <div className="flex flex-1 relative overflow-hidden w-full">
            {showTimeline && activeView !== 'timeline' && <TimelineSection />}

            <main
              className="flex-1 transition-all duration-300 w-full"
              style={{
                paddingTop: showTimeline && activeView !== 'timeline'
                  ? '1rem'
                  : '1rem'
              }}
            >
              {renderView()}
            </main>
          </div>
          {showColorLegend && <>
            <ColorLegend />
            <ColorLegendToggle collapsed={showCollapsed} />
          </>}
        </div>
      </div>
    </EpisodeSpineProvider>
  );
}