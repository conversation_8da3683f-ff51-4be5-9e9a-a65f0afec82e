# Story Unstuck - Local Development Version

A powerful story development application that combines visual organization and structured writing tools to help writers plan, organize, and write their stories effectively. This is a **local-only version** that runs entirely in your browser without requiring any external services or databases.

## Core Features

### 1. Workshop View
- Quadrant-based story development
- Drag-and-drop story cards
- Color-coded plot sections
- Expandable/collapsible sections
- AI-assisted brainstorming

### 2. Episodes View
- Visual episode planning
- Act structure templates
- Scene management
- Drag-and-drop scene organization
- Direct text editing

### 3. Timeline View
- Visual storyline tracking
- Color-coded story arcs
- Episode-by-episode planning
- Collapsible timeline interface
- Storyline management

### 4. Prompts View
- Story overview blocks
- Customizable prompt sections
- Drag-and-drop block organization
- Expandable/collapsible blocks
- Rich text editing

### 5. Tasks View
- Kanban board organization
- Drag-and-drop task management
- Comments and discussions
- Progress tracking
- Task categorization

### 6. Idea Pile
- Column-based organization
- Multiple content types (notes, images, links)
- Tag management
- Quick filters
- Source tracking

### 7. Local Data Storage
- All data stored in browser localStorage
- No external dependencies or accounts required
- Data persists between sessions
- Export/import functionality for data portability

## Project Structure

```
src/
├── components/          # UI components
│   ├── chat/           # AI chat interface
│   ├── episodes/       # Episode management
│   ├── ideapile/       # Idea organization
│   ├── layout/         # Common layouts
│   ├── prompts/        # Writing prompts
│   ├── tasks/          # Task management
│   ├── timeline/       # Story timeline
│   ├── ui/             # Shared UI components
│   ├── views/          # Main view components
│   └── workshop/       # Story workshop
├── contexts/           # React contexts
├── hooks/              # Custom React hooks
├── lib/               # Utility functions
├── types/             # TypeScript types
└── utils/             # Helper utilities
```

## Development Workflow

1. **Navigation**
   - Home view for project management
   - Seamless navigation between views
   - Persistent state across views

2. **Data Management**
   - Local storage persistence
   - Instant data updates
   - No network dependencies
   - Data export/import capabilities

3. **UI/UX**
   - Consistent design system
   - Responsive layouts
   - Drag-and-drop interactions
   - Keyboard shortcuts

4. **State Management**
   - React Context for global state
   - Custom hooks for feature logic
   - TypeScript for type safety
   - Modular state organization

## Getting Started

This application runs entirely locally - no external services, databases, or API keys required!

### Prerequisites
- Node.js (version 16 or higher)
- npm or yarn package manager

### Installation & Setup

1. **Clone the repository:**
```bash
git clone <repository-url>
cd SUSCleanup
```

2. **Install dependencies:**
```bash
npm install
```

3. **Start development server:**
```bash
npm run dev
```

4. **Open your browser:**
   - Navigate to `http://localhost:5174`
   - The application will automatically create a local user account
   - All data is stored in your browser's localStorage

5. **Build for production:**
```bash
npm run build
```

### Authentication
- **No signup required** - any email/password combination works
- User data is stored locally in your browser
- No external authentication service needed

## Best Practices

1. **Code Organization**
   - Small, focused components
   - Custom hooks for logic
   - TypeScript for type safety
   - Consistent file structure

2. **State Management**
   - Context for global state
   - Local state when possible
   - Optimistic updates
   - Error handling

3. **UI Components**
   - Reusable components
   - Consistent styling
   - Accessibility
   - Responsive design

4. **Performance**
   - Code splitting
   - Lazy loading
   - Memoization
   - Virtual scrolling

## Contributing

1. Fork the repository
2. Create feature branch
3. Commit changes
4. Push to branch
5. Open pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.