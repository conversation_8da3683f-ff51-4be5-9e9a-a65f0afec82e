import { useState, useCallback } from 'react';
import { logger } from '../../utils/logger';
import { ACT_COLORS } from '../../lib/theme';
import type { Prompt, PromptGroup, PromptFile } from '../../types/prompts';

const STORAGE_KEY = 'prompts_data';

// Default prompt groups with different colors
const DEFAULT_PROMPT_GROUPS: PromptGroup[] = [
  {
    id: 'genre-theme',
    title: 'GENRE & THEME',
    color: ACT_COLORS.act1,
    prompts: []
  },
  {
    id: 'character-conflict',
    title: 'CHARACTER & CONFLICT',
    color: ACT_COLORS.act2a,
    prompts: []
  },
  {
    id: 'story-structure',
    title: 'STORY STRUCTURE',
    color: ACT_COLORS.act3,
    prompts: []
  },
  {
    id: 'plot-framework',
    title: 'PLOT FRAMEWORK',
    color: ACT_COLORS.act4,
    prompts: []
  }
];

export function usePromptsState() {
  const [promptGroups, setPromptGroups] = useState<PromptGroup[]>(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      return saved ? JSON.parse(saved) : DEFAULT_PROMPT_GROUPS;
    } catch {
      return DEFAULT_PROMPT_GROUPS;
    }
  });

  const saveToStorage = useCallback((groups: PromptGroup[]) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(groups));
    } catch (error) {
      logger.error('Failed to save prompts:', error);
    }
  }, []);

  const addPromptGroup = useCallback((data: { title: string; color?: string }) => {
    const colors = Object.values(ACT_COLORS);
    const newGroup: PromptGroup = {
      id: Date.now().toString(),
      title: data.title,
      color: data.color || colors[promptGroups.length % colors.length],
      prompts: []
    };

    setPromptGroups(prev => {
      const next = [...prev, newGroup];
      saveToStorage(next);
      return next;
    });
    logger.debug('Added new prompt group:', newGroup);
  }, [promptGroups.length, saveToStorage]);

  const updatePromptGroup = useCallback((groupId: string, updates: Partial<PromptGroup>) => {
    logger.debug('Updating prompt group:', { groupId, updates });
    setPromptGroups(prev => {
      const next = prev.map(group =>
        group.id === groupId ? { ...group, ...updates } : group
      );
      saveToStorage(next);
      return next;
    });
  }, [saveToStorage]);

  const deletePromptGroup = useCallback((groupId: string) => {
    setPromptGroups(prev => {
      const next = prev.filter(group => group.id !== groupId);
      saveToStorage(next);
      return next;
    });
    logger.debug('Deleted prompt group:', groupId);
  }, [saveToStorage]);

  const addPrompt = useCallback((groupId: string) => {
    const newPrompt: Prompt = {
      id: Date.now().toString(),
      title: 'New Prompt',
      details: '',
      action: '',
      files: [],
      tags: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    setPromptGroups(prev => {
      const next = prev.map(group => {
        if (group.id === groupId) {
          return {
            ...group,
            prompts: [...group.prompts, newPrompt]
          };
        }
        return group;
      });
      saveToStorage(next);
      return next;
    });
    logger.debug('Added new prompt:', { groupId, prompt: newPrompt });
  }, [saveToStorage]);

  const updatePrompt = useCallback((groupId: string, promptId: string, updates: Partial<Prompt>) => {
    setPromptGroups(prev => {
      const next = prev.map(group => {
        if (group.id === groupId) {
          return {
            ...group,
            prompts: group.prompts.map(prompt =>
              prompt.id === promptId
                ? { ...prompt, ...updates, updatedAt: new Date() }
                : prompt
            )
          };
        }
        return group;
      });
      saveToStorage(next);
      return next;
    });
    logger.debug('Updated prompt:', { groupId, promptId, updates });
  }, [saveToStorage]);

  const addFile = useCallback((groupId: string, promptId: string, file: Omit<PromptFile, 'id' | 'createdAt'>) => {
    const newFile: PromptFile = {
      ...file,
      id: Date.now().toString(),
      createdAt: new Date()
    };

    setPromptGroups(prev => {
      const next = prev.map(group => {
        if (group.id === groupId) {
          return {
            ...group,
            prompts: group.prompts.map(prompt =>
              prompt.id === promptId
                ? { 
                    ...prompt, 
                    files: [...prompt.files, newFile],
                    updatedAt: new Date()
                  }
                : prompt
            )
          };
        }
        return group;
      });
      saveToStorage(next);
      return next;
    });
    logger.debug('Added file to prompt:', { groupId, promptId, file: newFile });
  }, [saveToStorage]);

  const deleteFile = useCallback((groupId: string, promptId: string, fileId: string) => {
    setPromptGroups(prev => {
      const next = prev.map(group => {
        if (group.id === groupId) {
          return {
            ...group,
            prompts: group.prompts.map(prompt =>
              prompt.id === promptId
                ? {
                    ...prompt,
                    files: prompt.files.filter(f => f.id !== fileId),
                    updatedAt: new Date()
                  }
                : prompt
            )
          };
        }
        return group;
      });
      saveToStorage(next);
      return next;
    });
    logger.debug('Deleted file from prompt:', { groupId, promptId, fileId });
  }, [saveToStorage]);

  return {
    promptGroups,
    addPromptGroup,
    updatePromptGroup,
    deletePromptGroup,
    addPrompt,
    updatePrompt,
    addFile,
    deleteFile
  };
}