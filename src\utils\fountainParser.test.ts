import { parseScenes, convertToAppScenes, extractCharactersFromContent } from './fountainParser';

// Sample Fountain screenplay
const sampleScreenplay = `Title: Test Screenplay
Author: Test Author

INT. LIVING ROOM - DAY

<PERSON><PERSON><PERSON> sits on the couch, reading a newspaper.

<PERSON><PERSON><PERSON>
(looking up)
Did you hear that?

<PERSON><PERSON><PERSON> enters from the kitchen.

MARY
Hear what?

JOHN
I thought I heard something outside.

EXT. HOUSE - CONTINUOUS

A shadowy figure moves across the yard.

CUT TO:

INT. LIVING ROOM - DAY

JOHN
I'm going to check it out.

MARY
Be careful.
`;

// Sample with multiple scenes
const multiSceneScreenplay = `
INT. LIVING ROOM - DAY

<PERSON> sits on the couch, reading a newspaper.

JOHN
I'm bored.

EXT. PARK - DAY

<PERSON> is jogging.

MARY
This is refreshing!

INT. OFFICE - NIGHT

<PERSON> works late.

BOB
I need to finish this report.
`;

describe('fountainParser', () => {
  describe('parseScenes', () => {
    test('should parse basic scenes', () => {
      const text = `
INT. LIVING ROOM - <PERSON><PERSON><PERSON> sits on the couch.

EXT. BACKYARD - EVENING

<PERSON> waters the plants.
`;
      const scenes = parseScenes(text);
      expect(scenes).toHaveLength(2);
      expect(scenes[0].heading).toContain('INT. LIVING ROOM');
      expect(scenes[1].heading).toContain('EXT. BACKYARD');
    });

    test('should include characters in parsed scenes', () => {
      const text = `
INT. LIVING ROOM - DAY

JOHN
Hello there!

MARY
Hi John, how are you?

JOHN
I'm doing great, thanks.
`;
      const scenes = parseScenes(text);
      expect(scenes).toHaveLength(1);
      expect(scenes[0].characters).toContain('JOHN');
      expect(scenes[0].characters).toContain('MARY');
    });
  });

  describe('extractCharactersFromContent', () => {
    test('should extract character names from speaking lines', () => {
      const content = `
INT. LIVING ROOM - DAY

JOHN
Hello there!

MARY
Hi John, how are you?

JOHN (CONT'D)
I'm doing great, thanks.
`;
      const characters = extractCharactersFromContent(content);
      expect(characters).toEqual(['JOHN', 'MARY']);
    });

    test('should extract character names from parenthetical cues', () => {
      const content = `
INT. LIVING ROOM - DAY

John enters the room and sees Mary.

John smiles at Mary.

Mary waves back at him.

JOHN
(to Mary)
Have you seen the new movie?

MARY
(shaking her head)
Not yet. Have you?

John looks around, checking if anyone else is listening.

JOHN
(whispering to Mary)
I saw it last night. It was amazing!
`;
      const characters = extractCharactersFromContent(content);
      expect(characters).toContain('Mary');
      expect(characters).toContain('John');
    });

    test('should ignore common false positives', () => {
      const content = `
FADE IN:

INT. LIVING ROOM - DAY

CUT TO:

EXT. BACKYARD - EVENING

DISSOLVE TO:

INT. KITCHEN - NIGHT
`;
      const characters = extractCharactersFromContent(content);
      expect(characters).toHaveLength(0);
    });

    test('should handle empty or invalid input', () => {
      expect(extractCharactersFromContent('')).toEqual([]);
      expect(extractCharactersFromContent(null)).toEqual([]);
      expect(extractCharactersFromContent(undefined)).toEqual([]);
    });
  });

  describe('convertToAppScenes', () => {
    test('should convert parsed scenes with characters to app scenes', () => {
      const parsedScenes = [
        {
          heading: 'INT. LIVING ROOM - DAY',
          content: 'JOHN\nHello there!\n\nMARY\nHi John!',
          order: 0,
          characters: ['JOHN', 'MARY']
        }
      ];
      
      const appScenes = convertToAppScenes(parsedScenes, 'act-1', 1);
      
      expect(appScenes).toHaveLength(1);
      expect(appScenes[0].title).toBe('Living Room');
      expect(appScenes[0].characters).toHaveLength(2);
      expect(appScenes[0].characters[0].name).toBe('JOHN');
      expect(appScenes[0].characters[1].name).toBe('MARY');
      expect(appScenes[0].characters[0].isActive).toBe(true);
    });
  });

  test('parseScenes should extract scenes from Fountain text', () => {
    const scenes = parseScenes(sampleScreenplay);
    
    // Should find 2 scenes
    expect(scenes.length).toBe(2);
    
    // First scene should be the living room
    expect(scenes[0].heading).toContain('LIVING ROOM');
    expect(scenes[0].content).toContain('JOHN sits on the couch');
    
    // Second scene should be outside
    expect(scenes[1].heading).toContain('HOUSE');
    expect(scenes[1].content).toContain('shadowy figure');
  });
  
  test('convertToAppScenes should convert parsed scenes to app format', () => {
    const parsedScenes = parseScenes(sampleScreenplay);
    const appScenes = convertToAppScenes(parsedScenes, 'test-act-id', 1);
    
    // Should have the same number of scenes
    expect(appScenes.length).toBe(parsedScenes.length);
    
    // App scenes should have the required properties
    expect(appScenes[0].title).toBeTruthy();
    expect(appScenes[0].content).toContain(parsedScenes[0].heading);
    expect(appScenes[0].content).toContain(parsedScenes[0].content);
    
    // Should have default values for other properties
    expect(appScenes[0].characters).toEqual([]);
    expect(appScenes[0].emotionalStart).toBe('Neutral');
    expect(appScenes[0].isComplete).toBe(false);
  });
  
  test('parseScenes should handle multiple scenes correctly', () => {
    const scenes = parseScenes(multiSceneScreenplay);
    
    // Should find 3 scenes
    expect(scenes.length).toBe(3);
    
    // Check scene headings
    expect(scenes[0].heading).toContain('LIVING ROOM');
    expect(scenes[1].heading).toContain('PARK');
    expect(scenes[2].heading).toContain('OFFICE');
    
    // Check scene content
    expect(scenes[0].content).toContain('John sits on the couch');
    expect(scenes[1].content).toContain('Mary is jogging');
    expect(scenes[2].content).toContain('Bob works late');
  });
  
  test('parseScenes should handle scene content correctly', () => {
    const scenes = parseScenes(multiSceneScreenplay);
    
    // Check that content doesn't include the heading
    expect(scenes[0].content).not.toContain('INT. LIVING ROOM - DAY');
    expect(scenes[1].content).not.toContain('EXT. PARK - DAY');
    
    // Check that content includes dialogue
    expect(scenes[0].content).toContain('JOHN');
    expect(scenes[1].content).toContain('MARY');
  });
});