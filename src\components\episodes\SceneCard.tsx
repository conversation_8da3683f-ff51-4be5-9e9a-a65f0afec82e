import React, { useState, useRef, useEffect } from 'react';
import { Grip, Edit, X, ChevronDown, ChevronUp, Palette, CheckSquare, Square } from 'lucide-react';
import { Button } from '../ui/button';
import { cn } from '../../lib/utils';
import { useNavigation } from '../../contexts/NavigationContext';
import { useEpisodeTrackStore } from '../../store/episodeTrackStore';
import { safelySetJsonForDrag } from '../../utils/dragUtils';
import type { Scene } from '../../types/episode';
import { StorylineColorPicker } from './StorylineColorPicker';

interface SceneCardProps {
  scene: Scene;
  index: number;
  groupId: string;
  color: string;
  storylineId?: string | null;
  onUpdate: (updates: Partial<Scene>) => void;
  onDelete: () => void;
  onDragStart: (e: React.DragEvent, scene: Scene, index: number) => void;
  onDragOver: (e: React.DragEvent, index: number) => void;
  onDrop: (e: React.DragEvent, index: number) => void;
  onStorylineChange?: (storylineId: string | null) => void;
  isEpisodeExpanded?: boolean;
  debug?: boolean;
  episodeActs?: any[];
}

export const SceneCard: React.FC<SceneCardProps> = ({
  scene,
  index,
  groupId,
  color,
  storylineId,
  onUpdate,
  onDelete,
  onDragStart,
  onDragOver,
  onDrop,
  onStorylineChange,
  isEpisodeExpanded = false,
  debug = false,
  episodeActs
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(scene.content);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [revisionState, setRevisionState] = useState<'complete' | 'in-revision' | 'needs-review' | 'not-started'>('not-started');
  const [showRevisionMenu, setShowRevisionMenu] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const [colorPickerError, setColorPickerError] = useState<string | null>(null);
  const { setCurrentView } = useNavigation();

  // Get storylines from the store
  const storylines = useEpisodeTrackStore(state => state.storylines);

  // Get the storyline color if it exists
  const storylineColor = storylineId ?
    storylines.find(s => s.id === storylineId)?.color :
    null;

  if (debug) {
    console.log('SceneCard render:', {
      id: scene.id,
      storylineId,
      storylineColor,
      storylines: storylines.map(s => ({ id: s.id, title: s.title, color: s.color }))
    });
  }

  // Calculate if content needs truncation
  const needsTruncation = scene.content.split('\n').length > 3 || scene.content.length > 120;

  // Debug log the scene data
  if (debug) {
    console.log('Scene data:', {
      id: scene.id,
      title: scene.title,
      content: scene.content?.substring(0, 30) + '...',
      storylineId,
      storylineColor,
      revisionState,
      episodeId: scene.episodeId,
      actId: scene.actId,
      sceneId: `${scene.episodeId}-${scene.actId}-${scene.id}`
    });
  }

  // Check if scene has write data and completion status
  useEffect(() => {
    try {
      // Create a unique ID for the scene that includes episode and act info
      const sceneId = `${scene.episodeId}-${scene.actId}-${scene.id}`;
      const savedData = localStorage.getItem(`write_scene_${sceneId}`);
      if (savedData) {
        const parsedData = JSON.parse(savedData);

        // Handle both legacy isComplete and new revisionState
        if (parsedData.revisionState) {
          setRevisionState(parsedData.revisionState);
          setIsComplete(parsedData.revisionState === 'complete');

          if (debug) {
            console.log('Loaded revision state:', {
              sceneId,
              revisionState: parsedData.revisionState
            });
          }
        } else {
          setIsComplete(Boolean(parsedData.isComplete));
          setRevisionState(parsedData.isComplete ? 'complete' : 'not-started');

          if (debug) {
            console.log('Loaded legacy completion state:', {
              sceneId,
              isComplete: Boolean(parsedData.isComplete)
            });
          }
        }

        // Also update the content if it's different
        if (parsedData.content !== scene.content) {
          onUpdate({ content: parsedData.content });
        }
      } else if (debug) {
        console.log('No saved data found for scene:', sceneId);
      }
    } catch (error) {
      console.error('Failed to check scene completion status:', error);
    }
  }, [scene, onUpdate, debug]);

  // Listen for changes to the scene content from the write module
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent | CustomEvent) => {
      // Handle both StorageEvent and CustomEvent
      const isStorageEvent = e instanceof StorageEvent;
      const key = isStorageEvent ? e.key : (e as any).detail?.key;
      const newValue = isStorageEvent ? e.newValue : (e as any).detail?.newValue;

      if (!key || !newValue) return;

      // Check if this is a write_scene event for this scene
      const sceneId = `${scene.episodeId}-${scene.actId}-${scene.id}`;
      if (key === `write_scene_${sceneId}`) {
        try {
          const data = JSON.parse(newValue);
          if (data.content !== scene.content) {
            setEditContent(data.content);
            onUpdate({ content: data.content });
          }

          // Handle both legacy isComplete and new revisionState
          if (data.revisionState) {
            setRevisionState(data.revisionState);
            setIsComplete(data.revisionState === 'complete');

            if (debug) {
              console.log('Updated revision state from storage event:', {
                sceneId,
                revisionState: data.revisionState
              });
            }
          } else {
            setIsComplete(Boolean(data.isComplete));
            setRevisionState(data.isComplete ? 'complete' : 'not-started');

            if (debug) {
              console.log('Updated legacy completion state from storage event:', {
                sceneId,
                isComplete: Boolean(data.isComplete)
              });
            }
          }
        } catch (error) {
          console.error('Failed to process scene update:', error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('sceneContentChanged', handleStorageChange as EventListener);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('sceneContentChanged', handleStorageChange as EventListener);
    };
  }, [scene, onUpdate, debug]);

  // Reset expansion state when episode expands/collapses
  useEffect(() => {
    if (!isEpisodeExpanded && isExpanded) {
      setIsExpanded(false);
    }
  }, [isEpisodeExpanded]);

  // Handle content height synchronization
  useEffect(() => {
    if (!isExpanded || !contentRef.current) return;

    const cards = document.querySelectorAll(`[data-group-id="${groupId}"]`);
    let maxHeight = 0;

    cards.forEach(card => {
      const content = card.querySelector('.scene-content');
      if (content) {
        const height = content.scrollHeight;
        maxHeight = Math.max(maxHeight, height);
      }
    });

    cards.forEach(card => {
      const content = card.querySelector('.scene-content');
      if (content && card.getAttribute('data-expanded') === 'true') {
        (content as HTMLElement).style.height = `${maxHeight}px`;
      }
    });
  }, [isExpanded, groupId, scene.content]);

  useEffect(() => {
    if (!isExpanded && contentRef.current) {
      contentRef.current.style.height = '';
    }
  }, [isExpanded]);

  const handleSave = () => {
    if (editContent.trim() !== scene.content) {
      onUpdate({ content: editContent.trim() });

      // Also update the write module data if it exists
      try {
        const sceneId = `${scene.episodeId}-${scene.actId}-${scene.id}`;
        const savedData = localStorage.getItem(`write_scene_${sceneId}`);
        if (savedData) {
          const parsedData = JSON.parse(savedData);
          parsedData.content = editContent.trim();
          parsedData.lastEdited = new Date().toISOString();
          localStorage.setItem(`write_scene_${sceneId}`, JSON.stringify(parsedData));

          // Dispatch a custom event to notify other components
          window.dispatchEvent(new CustomEvent('sceneContentChanged', {
            detail: {
              key: `write_scene_${sceneId}`,
              newValue: JSON.stringify(parsedData)
            }
          }));
        }
      } catch (error) {
        console.error('Failed to update write module data:', error);
      }
    }
    setIsEditing(false);
  };

  // Define mapping for revision state styles
  const revisionStateStyles: Record<string, { icon: React.ReactNode, label: string, color: string }> = {
    'complete': {
      icon: <CheckSquare className="h-3 w-3 text-green-600" />,
      label: 'Complete',
      color: 'bg-green-50 border-green-600'
    },
    'in-revision': {
      icon: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-3 w-3 text-orange-600"><path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/><path d="M3 3v5h5"/><path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16"/><path d="M16 16h5v5"/></svg>,
      label: 'In Revision',
      color: 'bg-orange-50 border-orange-600'
    },
    'needs-review': {
      icon: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-3 w-3 text-red-600"><circle cx="12" cy="12" r="10"/><line x1="12" x2="12" y1="8" y2="12"/><line x1="12" x2="12.01" y1="16" y2="16"/></svg>,
      label: 'Needs Review',
      color: 'bg-red-50 border-red-600'
    },
    'not-started': {
      icon: <Square className="h-3 w-3" />,
      label: 'Not Started',
      color: 'border-black/20'
    }
  };

  // Get current revision state configuration
  const stateConfig = revisionStateStyles[revisionState];

  // Debug log the current revision state
  useEffect(() => {
    if (debug) {
      console.log('Current revision state:', {
        sceneId: `${scene.episodeId}-${scene.actId}-${scene.id}`,
        revisionState,
        stateConfig
      });
    }
  }, [revisionState, debug, scene.episodeId, scene.actId, scene.id, stateConfig]);

  const handleDragStart = (e: React.DragEvent) => {
    if (isEditing) {
      e.preventDefault();
      return;
    }

    setIsDragging(true);
    e.dataTransfer.effectAllowed = 'move';

    if (cardRef.current) {
      const rect = cardRef.current.getBoundingClientRect();
      e.dataTransfer.setDragImage(cardRef.current, rect.width / 2, rect.height / 2);
    }

    safelySetJsonForDrag(e, {
      type: 'scene',
      id: scene.id,
      index,
      storylineId
    });

    onDragStart(e, scene, index);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const toggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isEpisodeExpanded) {
      setIsExpanded(!isExpanded);
      if (cardRef.current) {
        cardRef.current.setAttribute('data-expanded', (!isExpanded).toString());
      }
    }
  };

  const handleStorylineSelect = (newStorylineId: string | null) => {
    try {
      if (debug) {
        console.log('handleStorylineSelect:', { newStorylineId });
      }

      // Call the parent handler to update the storyline
      onStorylineChange?.(newStorylineId);

      // Close the color picker
      setShowColorPicker(false);
      setColorPickerError(null);
    } catch (error) {
      console.error('Error selecting storyline:', error);
      setColorPickerError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  const updateRevisionState = (newState: 'complete' | 'in-revision' | 'needs-review' | 'not-started') => {
    setRevisionState(newState);
    setShowRevisionMenu(false);

    // Update revision status in write module data
    try {
      const sceneId = `${scene.episodeId}-${scene.actId}-${scene.id}`;
      const savedData = localStorage.getItem(`write_scene_${sceneId}`);
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        parsedData.revisionState = newState;

        // For backward compatibility
        parsedData.isComplete = newState === 'complete';

        parsedData.lastEdited = new Date().toISOString();
        localStorage.setItem(`write_scene_${sceneId}`, JSON.stringify(parsedData));

        // Dispatch a custom event to notify other components
        window.dispatchEvent(new CustomEvent('sceneContentChanged', {
          detail: {
            key: `write_scene_${sceneId}`,
            newValue: JSON.stringify(parsedData)
          }
        }));
      } else {
        // If no write data exists yet, create it
        const newData = {
          id: sceneId,
          title: scene.title,
          content: scene.content,
          notes: [],
          characters: [],
          emotionalStart: 'Neutral',
          emotionalEnd: 'Neutral',
          emotionalProgress: 50,
          isPositiveArc: true,
          beats: [],
          revisionState: newState,
          isComplete: newState === 'complete',
          lastEdited: new Date().toISOString()
        };
        localStorage.setItem(`write_scene_${sceneId}`, JSON.stringify(newData));

        // Dispatch a custom event to notify other components
        window.dispatchEvent(new CustomEvent('sceneContentChanged', {
          detail: {
            key: `write_scene_${sceneId}`,
            newValue: JSON.stringify(newData)
          }
        }));
      }
    } catch (error) {
      console.error('Failed to update scene revision status:', error);
    }
  };

  // Navigate to write view for this scene
  const navigateToWriteView = (e: React.MouseEvent) => {
    e.stopPropagation();

    // Store the scene ID in session storage for the write view to use
    const sceneId = `${scene.episodeId}-${scene.actId}-${scene.id}`;
    sessionStorage.setItem('activeSceneId', sceneId);

    // Navigate to write view
    setCurrentView('write');
  };

  const cardHeight = isEpisodeExpanded ? 'auto' : '80px';
  const contentHeight = isEpisodeExpanded ? 'auto' : isExpanded ? '144px' : '56px';

  // Calculate global scene number if episodeActs is provided
  let globalSceneNumber = index + 1;
  if (episodeActs) {
    const allScenes = episodeActs.flatMap((a: any) => a.scenes.map((s: any) => ({ ...s, actId: a.id })));
    const idx = allScenes.findIndex((s: any) => s.id === scene.id && s.actId === scene.actId);
    if (idx !== -1) globalSceneNumber = idx + 1;
  }

  return (
    <div
      ref={cardRef}
      data-scene-id={scene.id}
      data-group-id={groupId}
      className={cn(
        "scene-card relative rounded mb-2 border-4 bg-white",
        isDragging ? "opacity-50" : "opacity-100"
      )}
      style={{
        borderColor: storylineColor || color || '#000',
        boxSizing: 'border-box',
        padding: 0,
      }}
      draggable={!isEditing}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="w-full bg-gray-100 py-0.5 px-1 border-b border-gray-300 flex items-center justify-between text-black" style={{borderTopLeftRadius: 6, borderTopRightRadius: 6}}>
        <div className="flex items-center gap-2 z-[9999] ml-auto" style={{marginRight: 4}}>
          <Button
            variant="ghost"
            size="sm"
            className="h-5 w-5 p-1 cursor-grab"
            style={{ minWidth: 0 }}
            tabIndex={-1}
            aria-label="Drag to reorder"
            draggable={!isEditing}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
          >
            <Grip className="h-3 w-3 text-gray-500" />
          </Button>
          <StorylineColorPicker
            currentStorylineId={storylineId}
            onStorylineChange={handleStorylineSelect}
            buttonSize="sm"
            position="top-right"
            debug={debug}
          />
          <Button
            variant="ghost"
            size="sm"
            onClick={e => { e.stopPropagation(); setIsEditing(true); }}
            className="h-7 w-7 p-1"
            title="Edit Content"
          >
            <Edit className="h-6 w-6 text-black" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={e => { e.stopPropagation(); if (window.confirm('Are you sure you want to delete this scene?')) { onDelete(); } }}
            className="h-7 w-7 p-1 hover:text-red-600"
            title="Delete Scene"
          >
            <X className="h-6 w-6 text-black" />
          </Button>
        </div>
      </div>
      <div className="p-2">
        {isEditing ? (
          <input
            className="font-mono text-xs font-bold truncate flex-1 cursor-text border-b border-blue-300 focus:outline-none focus:border-blue-500 bg-white"
            value={scene.title || ''}
            onChange={e => onUpdate({ title: e.target.value })}
            onBlur={() => setIsEditing(false)}
            autoFocus
            onKeyDown={e => { if (e.key === 'Enter') setIsEditing(false); }}
          />
        ) : (
          <h4
            className="font-mono text-xs font-bold truncate flex-1 cursor-text"
            title={scene.title}
            onClick={e => { e.stopPropagation(); setIsEditing(true); }}
          >
            {scene.title || <span className="text-gray-400">Untitled Scene</span>}
          </h4>
        )}
        <h4 className="font-mono text-sm font-bold mb-1">
          Scene {globalSceneNumber}
        </h4>
        <div className="scene-content p-1 text-xs cursor-text font-mono min-h-[30px]" onClick={e => { e.stopPropagation(); setIsEditing(true); }}>
          {scene.content || <span className="text-gray-400">Empty scene...</span>}
        </div>
      </div>
    </div>
  );
};