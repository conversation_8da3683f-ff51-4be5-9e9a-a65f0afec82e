import React from 'react';
import { HomeView } from './views/HomeView';
import { ClassicTemplate } from './templates/ClassicTemplate';
import { EnhancedTemplate } from './templates/EnhancedTemplate';
import { useNavigation } from '../contexts/NavigationContext';
import { useProject } from '../contexts/ProjectContext';
import { useTemplate } from '../hooks/useTemplate'; // ✅ use the hook

export function AppContent() {
  const { currentView, setCurrentView } = useNavigation();
  const { currentProject } = useProject();
  const { template, toggleTemplate } = useTemplate(); // ✅ global state

  // Always show home view if no project is selected
  if (!currentProject) {
    return <HomeView onNavigate={setCurrentView} />;
  }

  const commonProps = {
    activeView: currentView,
    onViewChange: setCurrentView,
    onHomeClick: () => setCurrentView('home'),
    onTemplateChange: toggleTemplate, // ✅ from context
    template
  };

  return template === 'enhanced' ? (
    <EnhancedTemplate {...commonProps} />
  ) : (
    <ClassicTemplate {...commonProps} />
  );
}
