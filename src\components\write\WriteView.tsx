import React, { useEffect } from 'react';
import { WriteLayout } from './layout/WriteLayout';
import { SceneContent } from './scene';
import { ErrorBoundary } from '../ErrorBoundary';
import { useWriteNavigation } from '../../hooks/write/useWriteNavigation';
import { useProject } from '../../contexts/ProjectContext';
import { useEpisodeState } from '../../hooks/useEpisodeState';

export const WriteView: React.FC = () => {
  const { currentProject } = useProject();
  const {
    activeSceneId,
    isNavCollapsed,
    toggleNavigation,
    selectScene,
    toggleEpisode,
    expandedEpisodes,
    expandedActs,
    toggleAct
  } = useWriteNavigation();
  
  const { episodes, episodeData } = useEpisodeState();

  // Auto-expand episodes and relevant acts when view loads
  useEffect(() => {
    // We no longer need to check for expandAll since we're using saved state
    // Just handle the active scene case
    if (activeSceneId) {
      // The scene is already selected, make sure its episode is expanded
      const parts = activeSceneId.split('-');
      if (parts.length >= 2) {
        const episodeId = parseInt(parts[0]);
        const actId = parts[1];
        
        // Make sure episode is expanded
        if (!expandedEpisodes.has(episodeId)) {
          toggleEpisode(episodeId);
        }
        
        // Make sure act is expanded 
        if (!expandedActs.has(actId)) {
          toggleAct(actId);
        }
      }
    } 
    // No active scene, try to expand first episode if nothing else is expanded
    else if (expandedEpisodes.size === 0 && episodes.length > 0) {
      // Try to expand first episode and its first act
      const firstEpisodeId = episodes[0];
      
      // Expand the first episode
      toggleEpisode(firstEpisodeId);
      
      // Try to expand the first act of this episode
      if (episodeData[firstEpisodeId] && 
          episodeData[firstEpisodeId].acts.length > 0) {
        const firstActId = episodeData[firstEpisodeId].acts[0].id;
        toggleAct(firstActId);
      }
    }
  }, [activeSceneId, expandedEpisodes, expandedActs, episodes, episodeData, toggleEpisode, toggleAct]);

  // Check if we have an active episode ID from the episodes view
  useEffect(() => {
    const storedEpisodeId = sessionStorage.getItem('activeEpisodeId');
    // Only use the stored episode ID if we have a current project
    if (storedEpisodeId && currentProject) {
      // Clear it so it's only used once
      sessionStorage.removeItem('activeEpisodeId');
      
      const episodeId = parseInt(storedEpisodeId);
      if (!isNaN(episodeId)) {
        toggleEpisode(episodeId);
      }
    }
  }, [toggleEpisode, currentProject]);

  return (
    <WriteLayout
      isNavCollapsed={isNavCollapsed}
      onToggleNav={toggleNavigation}
      activeSceneId={activeSceneId}
      onSceneSelect={selectScene}
    >
      {activeSceneId ? (
        <ErrorBoundary>
          <SceneContent
            sceneId={activeSceneId}
            onClose={() => selectScene(undefined)}
          />
        </ErrorBoundary>
      ) : (
        <div className="flex-1 flex flex-col items-center justify-center text-gray-500">
          <div className="max-w-md text-center p-8">
            <h2 className="text-xl font-bold mb-4">Select a scene to begin writing</h2>
            <p className="mb-4">
              Choose a scene from the navigation panel to start writing. Your content will be saved automatically.
            </p>
            <p className="text-sm text-gray-400">
              Scenes are only created when you select them, saving storage space for your project.
            </p>
          </div>
        </div>
      )}
    </WriteLayout>
  );
};