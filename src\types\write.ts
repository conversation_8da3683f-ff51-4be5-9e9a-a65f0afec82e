import type { Note } from './shared';

export interface Character {
  id: string;
  name: string;
  isActive: boolean;
}

export interface SceneBeat {
  id: string;
  title: string;
  notes: string;
  color: string;
}

// Define revision states for scenes
export type RevisionState = 'complete' | 'in-revision' | 'needs-review' | 'not-started';

// Define note types for color-coding
export type NoteType = 'rewrite' | 'review' | 'discuss' | 'research' | 'general';

// Extend the Note interface with type information
export interface SceneNote extends Note {
  type: NoteType;
  isResolved?: boolean;
  collaborators?: string[]; // Array of collaborator emails
  sceneId?: string; // ID of the scene this note belongs to
}

export interface Scene {
  id: string;
  title: string;
  content: string;
  notes: SceneNote[];
  characters: Character[];
  emotionalStart: string;
  emotionalEnd: string;
  emotionalProgress: number;
  isPositiveArc: boolean;
  beats: SceneBeat[];
  isComplete?: boolean;
  revisionState?: RevisionState;
  lastEdited?: string;
}

export interface Note {
  id: string;
  user: string;
  content: string;
  time: string;
}