import type { IdeaItem, IdeaTag, IdeaReference } from '../types/ideaPile';
import { logger } from '../utils/logger';

// Local storage keys
const IDEAS_KEY = 'story_unstuck_ideas';
const TAGS_KEY = 'story_unstuck_idea_tags';
const REFERENCES_KEY = 'story_unstuck_idea_references';

// Helper functions for localStorage operations
const getStoredData = <T>(key: string): T[] => {
  try {
    const data = localStorage.getItem(key);
    return data ? JSON.parse(data) : [];
  } catch (error) {
    logger.error(`Failed to parse ${key} from localStorage:`, error);
    return [];
  }
};

const setStoredData = <T>(key: string, data: T[]): void => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    logger.error(`Failed to save ${key} to localStorage:`, error);
  }
};

export const ideaPileApi = {
  // Core idea management
  async createIdea(idea: Omit<IdeaItem, 'id' | 'created_at' | 'updated_at'>): Promise<IdeaItem> {
    const newIdea: IdeaItem = {
      ...idea,
      id: `idea-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const ideas = getStoredData<IdeaItem>(IDEAS_KEY);
    ideas.push(newIdea);
    setStoredData(IDEAS_KEY, ideas);

    logger.debug('Idea created locally:', newIdea.id);
    return newIdea;
  },

  async updateIdea(id: string, updates: Partial<IdeaItem>): Promise<IdeaItem> {
    const ideas = getStoredData<IdeaItem>(IDEAS_KEY);
    const index = ideas.findIndex(idea => idea.id === id);

    if (index === -1) {
      throw new Error(`Idea with id ${id} not found`);
    }

    const updatedIdea = {
      ...ideas[index],
      ...updates,
      updated_at: new Date().toISOString()
    };

    ideas[index] = updatedIdea;
    setStoredData(IDEAS_KEY, ideas);

    logger.debug('Idea updated locally:', id);
    return updatedIdea;
  },

  async deleteIdea(id: string): Promise<void> {
    const ideas = getStoredData<IdeaItem>(IDEAS_KEY);
    const filteredIdeas = ideas.filter(idea => idea.id !== id);
    setStoredData(IDEAS_KEY, filteredIdeas);

    // Also remove associated tags and references
    const tags = getStoredData<IdeaTag>(TAGS_KEY);
    const filteredTags = tags.filter(tag => tag.idea_id !== id);
    setStoredData(TAGS_KEY, filteredTags);

    const references = getStoredData<IdeaReference>(REFERENCES_KEY);
    const filteredReferences = references.filter(ref => ref.idea_id !== id);
    setStoredData(REFERENCES_KEY, filteredReferences);

    logger.debug('Idea deleted locally:', id);
  },

  async getIdeas(filters?: {
    category?: string;
    status?: string;
    tags?: string[];
    search?: string;
  }): Promise<IdeaItem[]> {
    let ideas = getStoredData<IdeaItem>(IDEAS_KEY);
    const tags = getStoredData<IdeaTag>(TAGS_KEY);
    const references = getStoredData<IdeaReference>(REFERENCES_KEY);

    // Apply filters
    if (filters?.category) {
      ideas = ideas.filter(idea => idea.category === filters.category);
    }
    if (filters?.status) {
      ideas = ideas.filter(idea => idea.status === filters.status);
    }
    if (filters?.search) {
      const searchLower = filters.search.toLowerCase();
      ideas = ideas.filter(idea =>
        idea.title?.toLowerCase().includes(searchLower) ||
        idea.content?.toLowerCase().includes(searchLower)
      );
    }
    if (filters?.tags?.length) {
      ideas = ideas.filter(idea => {
        const ideaTags = tags.filter(tag => tag.idea_id === idea.id).map(tag => tag.tag);
        return filters.tags!.some(filterTag => ideaTags.includes(filterTag));
      });
    }

    // Attach tags and references to each idea
    const ideasWithRelations = ideas.map(idea => ({
      ...idea,
      tags: tags.filter(tag => tag.idea_id === idea.id),
      references: references.filter(ref => ref.idea_id === idea.id)
    }));

    logger.debug('Ideas loaded from localStorage:', ideasWithRelations.length);
    return ideasWithRelations;
  },

  // Tag management
  async addTag(ideaId: string, tag: string): Promise<IdeaTag> {
    const newTag: IdeaTag = {
      id: `tag-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      idea_id: ideaId,
      tag,
      created_at: new Date().toISOString()
    };

    const tags = getStoredData<IdeaTag>(TAGS_KEY);

    // Check if tag already exists for this idea
    const existingTag = tags.find(t => t.idea_id === ideaId && t.tag === tag);
    if (existingTag) {
      return existingTag;
    }

    tags.push(newTag);
    setStoredData(TAGS_KEY, tags);

    logger.debug('Tag added locally:', { ideaId, tag });
    return newTag;
  },

  async removeTag(ideaId: string, tag: string): Promise<void> {
    const tags = getStoredData<IdeaTag>(TAGS_KEY);
    const filteredTags = tags.filter(t => !(t.idea_id === ideaId && t.tag === tag));
    setStoredData(TAGS_KEY, filteredTags);

    logger.debug('Tag removed locally:', { ideaId, tag });
  },

  // Project references
  async addReference(reference: Omit<IdeaReference, 'id' | 'created_at'>): Promise<IdeaReference> {
    const newReference: IdeaReference = {
      ...reference,
      id: `ref-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      created_at: new Date().toISOString()
    };

    const references = getStoredData<IdeaReference>(REFERENCES_KEY);
    references.push(newReference);
    setStoredData(REFERENCES_KEY, references);

    logger.debug('Reference added locally:', newReference.id);
    return newReference;
  },

  async removeReference(id: string): Promise<void> {
    const references = getStoredData<IdeaReference>(REFERENCES_KEY);
    const filteredReferences = references.filter(ref => ref.id !== id);
    setStoredData(REFERENCES_KEY, filteredReferences);

    logger.debug('Reference removed locally:', id);
  }
};