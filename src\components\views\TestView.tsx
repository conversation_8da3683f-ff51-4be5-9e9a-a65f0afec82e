import React from 'react';
import { ProjectStorageTest } from '../test/ProjectStorageTest';
import { StorageDebugger } from '../test/StorageDebugger';
import { DEFAULT_THEME_SETTINGS } from '../../lib/theme';

export const TestView: React.FC = () => {
  // Function to reset theme/zoom settings
  const handleResetZoom = () => {
    localStorage.setItem('story_unstuck_theme', JSON.stringify(DEFAULT_THEME_SETTINGS));
    window.location.reload();
  };

  return (
    <div className="min-h-screen p-6 bg-gray-100">
      <h1 className="text-2xl font-bold mb-6">Test View</h1>
      {/* Reset Zoom/Theme Button - prominent and in flow */}
      <button
        onClick={handleResetZoom}
        className="block w-full max-w-md mx-auto my-8 px-6 py-4 text-2xl bg-yellow-300 border-4 border-black rounded font-bold hover:bg-yellow-400 transition shadow-lg"
      >
        Reset Zoom/Theme to Default
      </button>
      {/* Storage Debugger */}
      <div className="mb-8">
        <StorageDebugger />
      </div>
      <ProjectStorageTest />
      <div className="mt-8 p-6 border-4 border-black bg-white">
        <h2 className="text-xl font-bold mb-4">Troubleshooting Guide</h2>
        <div className="space-y-4">
          <div>
            <h3 className="font-bold">Authentication Issues</h3>
            <p className="mb-2">If you're having trouble with authentication:</p>
            <ul className="list-disc pl-6 space-y-1">
              <li>Use the "Skip Authentication" button on the login page</li>
              <li>This will create a mock user session that works offline</li>
              <li>All data will be stored in localStorage instead of Supabase</li>
            </ul>
          </div>
          <div>
            <h3 className="font-bold">Project Creation Issues</h3>
            <p className="mb-2">If projects aren't being created or saved:</p>
            <ul className="list-disc pl-6 space-y-1">
              <li>Check if you're in local mode (mock authentication)</li>
              <li>Use the "Create Test Project" button above to test project creation</li>
              <li>Check the "LocalStorage Projects" section to verify local storage</li>
            </ul>
          </div>
          <div>
            <h3 className="font-bold">Data Storage</h3>
            <p className="mb-2">The application now uses a hybrid storage approach:</p>
            <ul className="list-disc pl-6 space-y-1">
              <li>Tries Supabase first for authenticated users</li>
              <li>Falls back to localStorage if Supabase operations fail</li>
              <li>Mock authentication mode uses localStorage exclusively</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}