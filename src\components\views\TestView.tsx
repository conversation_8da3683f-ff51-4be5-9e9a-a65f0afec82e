import React from 'react';
import { StorageDebugger } from '../test/StorageDebugger';
import { DEFAULT_THEME_SETTINGS } from '../../lib/theme';

export const TestView: React.FC = () => {
  // Function to reset theme/zoom settings
  const handleResetZoom = () => {
    localStorage.setItem('story_unstuck_theme', JSON.stringify(DEFAULT_THEME_SETTINGS));
    window.location.reload();
  };

  return (
    <div className="min-h-screen p-6 bg-gray-100">
      <h1 className="text-2xl font-bold mb-6">Test View</h1>
      {/* Reset Zoom/Theme Button - prominent and in flow */}
      <button
        onClick={handleResetZoom}
        className="block w-full max-w-md mx-auto my-8 px-6 py-4 text-2xl bg-yellow-300 border-4 border-black rounded font-bold hover:bg-yellow-400 transition shadow-lg"
      >
        Reset Zoom/Theme to Default
      </button>
      {/* Storage Debugger */}
      <div className="mb-8">
        <StorageDebugger />
      </div>
      <div className="mt-8 p-6 border-4 border-black bg-white">
        <h2 className="text-xl font-bold mb-4">Troubleshooting Guide</h2>
        <div className="space-y-4">
          <div>
            <h3 className="font-bold">Authentication Issues</h3>
            <p className="mb-2">Authentication is handled locally:</p>
            <ul className="list-disc pl-6 space-y-1">
              <li>Any email/password combination will work for login</li>
              <li>User data is stored locally in your browser</li>
              <li>No external authentication service required</li>
            </ul>
          </div>
          <div>
            <h3 className="font-bold">Project Creation Issues</h3>
            <p className="mb-2">If projects aren't being created or saved:</p>
            <ul className="list-disc pl-6 space-y-1">
              <li>Check browser console for any JavaScript errors</li>
              <li>Ensure localStorage is enabled in your browser</li>
              <li>Try clearing browser cache and reloading the page</li>
              <li>Use the Storage Debugger above to inspect stored data</li>
            </ul>
          </div>
          <div>
            <h3 className="font-bold">Data Storage</h3>
            <p className="mb-2">The application uses local storage for all data:</p>
            <ul className="list-disc pl-6 space-y-1">
              <li>All data is stored locally in your browser</li>
              <li>No external services or databases required</li>
              <li>Data persists between sessions on the same device</li>
              <li>Use browser developer tools to inspect localStorage if needed</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}