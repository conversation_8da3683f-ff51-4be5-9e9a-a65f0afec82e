/**
 * Validates if text contains proper Fountain scene headings
 * @param text The text to validate
 * @returns True if valid scene headings are found
 */
export function validateFountainScenes(text: string): boolean {
  // Check for proper scene headings (INT./EXT.)
  const sceneHeadingRegex = /^(INT|EXT|INT\/EXT|I\/E)[.\s]+/im;
  return sceneHeadingRegex.test(text);
}

/**
 * Checks if text is likely in Fountain format
 * @param text The text to check
 * @returns True if the text appears to be in Fountain format
 */
export function isFountainFormat(text: string): boolean {
  // Check for scene headings (INT./EXT.)
  const hasSceneHeadings = /^(INT|EXT|INT\/EXT|I\/E)[.\s]+/im.test(text);
  
  // Check for character cues (ALL CAPS followed by dialogue)
  const hasCharacterCues = /^[A-Z\s]+$[\r\n]+/m.test(text);
  
  // Check for transitions
  const hasTransitions = /^(CUT TO|FADE TO|DISSOLVE TO|SMASH CUT|MATCH CUT):/im.test(text);
  
  // If it has at least two of these features, it's likely Fountain
  return [hasSceneHeadings, hasCharacterCues, hasTransitions]
    .filter(Boolean).length >= 2;
}

/**
 * Formats text according to Fountain screenplay format rules
 * @param text The text to format
 * @returns Formatted text following Fountain syntax
 */
export function formatFountainText(text: string): string {
  return text
    // Format scene headings
    .replace(/^(int|ext|int\/ext|i\/e)(\.|\s+)/gim, (match) => 
      match.toUpperCase())
    
    // Format character names (all caps on their own line)
    .replace(/^([A-Za-z\s]+)$/gm, (line) => {
      // Only if it's a reasonable length for a character name and not a transition
      if (line.trim().length > 0 && 
          line.trim().length < 30 && 
          !line.includes('TO:') &&
          !line.match(/^(FADE|CUT|DISSOLVE|SMASH|WIPE)/i)) {
        return line.trim().toUpperCase();
      }
      return line;
    })
    
    // Format transitions
    .replace(/^(fade|cut|dissolve|smash|wipe).*$/gim, (line) => 
      line.toUpperCase())
    
    // Format parentheticals
    .replace(/^\((.+)\)$/gm, (match, group) => 
      `(${group.toLowerCase()})`)
    
    // Add proper spacing between elements
    .replace(/\n{3,}/g, '\n\n')
    
    // Ensure scene headings have proper spacing
    .replace(/\n(INT|EXT|INT\/EXT|I\/E)/g, '\n\n$1');
}

/**
 * Highlights Fountain syntax with HTML spans for styling
 * @param text The text to highlight
 * @returns HTML string with syntax highlighting
 */
export function highlightFountainSyntax(text: string): string {
  const lines = text.split('\n');
  
  return lines.map(line => {
    // Scene headings
    if (/^(INT|EXT|INT\/EXT|I\/E)\./.test(line)) {
      return `<span style="color: #2563eb; font-weight: 700;">${line}</span>`;
    }
    
    // Character names
    if (/^[A-Z\s]+$/.test(line.trim()) && line.trim().length > 0) {
      return `<span style="color: #10b981; font-weight: 700;">${line}</span>`;
    }
    
    // Parentheticals
    if (/^\(.+\)$/.test(line.trim())) {
      return `<span style="color: #6b7280; font-style: italic;">${line}</span>`;
    }
    
    // Transitions
    if (/^(FADE|CUT|DISSOLVE|SMASH|WIPE)/.test(line) || /TO:$/.test(line)) {
      return `<span style="color: #8b5cf6; font-weight: 700;">${line}</span>`;
    }
    
    // Notes
    if (/^\[\[.+\]\]$/.test(line.trim())) {
      return `<span style="color: #d97706; background-color: #fffbeb;">${line}</span>`;
    }
    
    // Section headings
    if (/^#+\s+/.test(line)) {
      return `<span style="color: #4f46e5; font-weight: 700;">${line}</span>`;
    }
    
    // Default text
    return line;
  }).join('\n');
}

/**
 * Analyzes Fountain text and provides suggestions for improvement
 * @param text The text to analyze
 * @returns Array of suggestions
 */
export function analyzeFountainText(text: string): string[] {
  const suggestions: string[] = [];
  const lines = text.split('\n');
  
  // Check for missing scene headings
  if (!lines.some(line => /^(INT|EXT|INT\/EXT|I\/E)\./.test(line))) {
    suggestions.push('Consider adding a scene heading (INT./EXT.)');
  }
  
  // Check for very long action paragraphs
  const actionParagraphs = lines.filter(line => 
    line.trim().length > 0 && 
    !line.match(/^[A-Z\s]+$/) && 
    !line.match(/^\(.+\)$/) &&
    !line.match(/^(INT|EXT|INT\/EXT|I\/E)\./)
  );
  
  if (actionParagraphs.some(p => p.length > 200)) {
    suggestions.push('Consider breaking up long action paragraphs');
  }
  
  // Check for character names consistency
  const characterNames = lines
    .filter(line => /^[A-Z\s]+$/.test(line.trim()) && line.trim().length > 0)
    .map(line => line.trim());
  
  const uniqueNames = new Set(characterNames);
  if (uniqueNames.size < characterNames.length) {
    suggestions.push('Check for character name consistency');
  }
  
  return suggestions;
}

/**
 * Extracts characters from Fountain text
 * @param text The Fountain text
 * @returns Array of character names
 */
export function extractCharacters(text: string): string[] {
  const lines = text.split('\n');
  const characterLines = lines.filter(line => 
    /^[A-Z\s]+$/.test(line.trim()) && 
    line.trim().length > 0 &&
    !line.match(/^(INT|EXT|INT\/EXT|I\/E)\./) &&
    !line.match(/^(FADE|CUT|DISSOLVE|SMASH|WIPE)/) &&
    !line.includes('TO:')
  );
  
  return [...new Set(characterLines.map(line => line.trim()))];
}

/**
 * Converts Fountain text to HTML for preview
 * @param text The Fountain text
 * @returns HTML representation of the screenplay
 */
export function fountainToHtml(text: string): string {
  const lines = text.split('\n');
  let html = '';
  let inDialogue = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();
    
    // Scene headings
    if (/^(INT|EXT|INT\/EXT|I\/E)\./.test(line)) {
      html += `<div class="font-bold text-blue-600 my-4">${line}</div>`;
      inDialogue = false;
    }
    // Character names
    else if (/^[A-Z\s]+$/.test(trimmedLine) && trimmedLine.length > 0) {
      html += `<div class="font-bold text-green-600 mt-4">${line}</div>`;
      inDialogue = true;
    }
    // Parentheticals
    else if (/^\(.+\)$/.test(trimmedLine)) {
      html += `<div class="text-gray-500 italic ml-8">${line}</div>`;
    }
    // Dialogue
    else if (inDialogue && trimmedLine.length > 0) {
      html += `<div class="ml-8 mb-2">${line}</div>`;
    }
    // Transitions
    else if (/^(FADE|CUT|DISSOLVE|SMASH|WIPE)/.test(line) || /TO:$/.test(line)) {
      html += `<div class="font-bold text-purple-600 text-right my-4">${line}</div>`;
      inDialogue = false;
    }
    // Action
    else if (trimmedLine.length > 0) {
      html += `<div class="my-2">${line}</div>`;
      inDialogue = false;
    }
    // Empty line
    else {
      html += '<div>&nbsp;</div>';
      inDialogue = false;
    }
  }
  
  return html;
}

/**
 * Extracts scene metadata from Fountain text
 * @param text The Fountain text
 * @returns Object with metadata like character count, dialogue percentage, etc.
 */
export function extractSceneMetadata(text: string): {
  characterCount: number;
  dialoguePercentage: number;
  actionPercentage: number;
  approximateDuration: number;
} {
  const lines = text.split('\n');
  const characters = new Set<string>();
  let dialogueLines = 0;
  let actionLines = 0;
  
  let inDialogue = false;
  let currentCharacter = '';
  
  for (const line of lines) {
    const trimmedLine = line.trim();
    
    // Character cues
    if (/^[A-Z\s]+$/.test(trimmedLine) && trimmedLine.length > 0) {
      currentCharacter = trimmedLine;
      characters.add(currentCharacter);
      inDialogue = true;
    }
    // Dialogue
    else if (inDialogue && trimmedLine.length > 0 && !trimmedLine.startsWith('(')) {
      dialogueLines++;
    }
    // Action
    else if (trimmedLine.length > 0 && !inDialogue) {
      actionLines++;
    }
    // Empty line ends dialogue
    else if (trimmedLine.length === 0) {
      inDialogue = false;
    }
  }
  
  const totalLines = dialogueLines + actionLines;
  const dialoguePercentage = totalLines > 0 ? (dialogueLines / totalLines) * 100 : 0;
  const actionPercentage = totalLines > 0 ? (actionLines / totalLines) * 100 : 0;
  
  // Rough estimate: 1 page = 1 minute, 1 page ≈ 55 lines
  const approximateDuration = Math.max(1, Math.round(totalLines / 55));
  
  return {
    characterCount: characters.size,
    dialoguePercentage,
    actionPercentage,
    approximateDuration
  };
}
import { logger } from './logger';
import { nanoid } from 'nanoid';
import type { Scene } from '../types/scene';

/**
 * Converts parsed Fountain scenes to app-specific scene objects
 * @param scenes Array of parsed Fountain scenes
 * @returns Array of Scene objects ready for the app
 */
export function convertToAppScenes(scenes: any[]): Scene[] {
  logger.debug(`Converting ${scenes.length} Fountain scenes to app format`);
  
  return scenes.map((scene, index) => {
    // Extract scene number and title from the scene heading
    const heading = scene.scene_heading || '';
    const match = heading.match(/^(?:INT|EXT|INT\/EXT|EXT\/INT|INT\.|EXT\.|INT \/ EXT|EXT \/ INT|INT\.|EXT\.)?(?:[^\w]*?)([^-\n]*?)(?:\s*-\s*(.*?))?$/i);
    
    let location = '';
    let timeOfDay = '';
    
    if (match) {
      location = match[1]?.trim() || '';
      timeOfDay = match[2]?.trim() || '';
    }
    
    // Use nanoid for unique scene IDs
    const uniqueId = nanoid();
    
    return {
      id: uniqueId,
      title: heading,
      content: scene.scene_content || '',
      location,
      timeOfDay,
      number: index + 1,
      type: 'scene',
    };
  });
}
