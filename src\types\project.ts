export interface Project {
  id: string;
  name: string;
  type: 'tv-30' | 'tv-60' | 'film' | 'novel';
  status: 'active' | 'draft' | 'completed';
  template: 'classic' | 'enhanced';
  lastModified: Date;
  updated_at?: string;
  user_id?: string;
  created_at?: string;
  description?: string;
  description?: string;
  collaborators?: string[]; // Array of collaborator emails
}

export interface Episode {
  id: string;
  project_id: string;
  number: number;
  title: string;
  status: 'draft' | 'in_progress' | 'complete';
  created_at?: string;
  updated_at?: string;
}

export interface Act {
  id: string;
  episode_id: string;
  title: string;
  subtitle?: string;
  color: string;
  position: number;
  storyline_id?: string | null;
  created_at?: string;
  updated_at?: string;
  scenes?: Scene[];
}

export interface Scene {
  id: string;
  act_id: string;
  title: string;
  content?: string;
  position: number;
  storyline_id?: string | null;
  is_complete: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface Storyline {
  id: string;
  project_id: string;
  title: string;
  color: string;
  created_at?: string;
  updated_at?: string;
  episodes?: Record<string, boolean>;
}