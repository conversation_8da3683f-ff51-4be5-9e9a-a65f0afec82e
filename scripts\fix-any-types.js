#!/usr/bin/env node

/**
 * <PERSON>ript to help identify and suggest fixes for TypeScript 'any' types
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Common patterns and their suggested replacements
const anyTypeReplacements = {
  // Event handlers
  'e: any': 'e: React.MouseEvent | React.KeyboardEvent',
  'event: any': 'event: React.SyntheticEvent',
  'data: any': 'data: unknown',
  'props: any': 'props: Record<string, unknown>',
  'item: any': 'item: unknown',
  'value: any': 'value: unknown',
  'result: any': 'result: unknown',
  'response: any': 'response: unknown',
  
  // Function parameters
  '(data: any)': '(data: unknown)',
  '(item: any)': '(item: unknown)',
  '(value: any)': '(value: unknown)',
  '(props: any)': '(props: Record<string, unknown>)',
  
  // Array types
  'any[]': 'unknown[]',
  'Array<any>': 'Array<unknown>',
  
  // Object types
  ': any = {}': ': Record<string, unknown> = {}',
  ': any = []': ': unknown[] = []',
};

function findTsFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      files.push(...findTsFiles(fullPath));
    } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

function analyzeFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  const issues = [];
  
  lines.forEach((line, index) => {
    if (line.includes(': any') || line.includes('<any>') || line.includes('any[]')) {
      // Find potential replacements
      let suggestion = line;
      for (const [pattern, replacement] of Object.entries(anyTypeReplacements)) {
        if (line.includes(pattern)) {
          suggestion = line.replace(pattern, replacement);
          break;
        }
      }
      
      issues.push({
        line: index + 1,
        original: line.trim(),
        suggestion: suggestion !== line ? suggestion.trim() : 'Consider using: unknown, specific interface, or union type'
      });
    }
  });
  
  return issues;
}

function main() {
  console.log('🔍 Analyzing TypeScript files for "any" types...\n');
  
  const srcFiles = findTsFiles('./src');
  let totalIssues = 0;
  
  for (const file of srcFiles) {
    const issues = analyzeFile(file);
    if (issues.length > 0) {
      console.log(`📄 ${file}:`);
      issues.forEach(issue => {
        console.log(`  Line ${issue.line}: ${issue.original}`);
        console.log(`  💡 ${issue.suggestion}\n`);
      });
      totalIssues += issues.length;
    }
  }
  
  console.log(`\n📊 Found ${totalIssues} 'any' type issues across ${srcFiles.length} files`);
  console.log('\n🛠️  Recommended tools:');
  console.log('1. Use TypeScript strict mode (already enabled)');
  console.log('2. Install @typescript-eslint/no-explicit-any rule (already enabled)');
  console.log('3. Use "unknown" instead of "any" for safer typing');
  console.log('4. Create specific interfaces for complex objects');
  console.log('5. Use union types for known possibilities');
}

main();
