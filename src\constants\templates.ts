// Templates for project types with standardized structure definitions

// Default template colors for visual consistency
export const templateColors = {
  coldOpen: 'bg-blue-500',
  act1: 'bg-purple-500',
  act2a: 'bg-green-500',
  act2b: 'bg-emerald-500',
  act3: 'bg-yellow-500',
  act4: 'bg-orange-500',
  act5: 'bg-pink-500',
  cliffhanger: 'bg-red-500',
  // For film sections
  setup: 'bg-blue-500',
  risingAction: 'bg-purple-500',
  confrontation: 'bg-orange-500',
  resolution: 'bg-red-500',
  // For novel chapters
  chapter: 'bg-blue-500',
  scene: 'bg-purple-500'
};

// Default number of scenes per act/section
export const sceneCounts = {
  // TV-30 scene counts
  tv30: {
    coldopen: 1,
    act1: 2,
    act2a: 2,
    act2b: 2,
    act3: 2, 
    act4: 2,
    cliffhanger: 1
  },
  // TV-60 scene counts
  tv60: {
    coldopen: 1,
    act1: 2,
    act2a: 2,
    act2b: 2,
    act3: 2,
    act4: 2,
    act5: 2,
    cliffhanger: 1
  },
  // Film scene counts
  film: {
    setup: 4,
    risingAction: 4,
    confrontation: 4,
    resolution: 4
  },
  // Novel scene counts
  novel: {
    chapter: 8 // 8 scenes per chapter by default
  }
};

// Default episode count by project type
export const episodeCounts = {
  'tv-30': 10, // 30-minute shows have 10 episodes by default
  'tv-60': 8,  // 60-minute shows have 8 episodes by default
  'film': 1,   // Films have a single "episode"
  'novel': 12  // Novels have 12 chapters by default
};

// Template structure definitions
export type ProjectTemplateType = 'tv-30' | 'tv-60' | 'film' | 'novel';

// Define structure for TV-30 template (30-minute episodes)
export const tv30Template = [
  { id: 'coldopen', title: 'COLD OPEN', subtitle: '', color: templateColors.coldOpen, scenes: [], beats: [] },
  { id: 'act1', title: 'ACT 1', subtitle: '', color: templateColors.act1, scenes: [], beats: [] },
  { id: 'act2a', title: 'ACT 2A', subtitle: '', color: templateColors.act2a, scenes: [], beats: [] },
  { id: 'act2b', title: 'ACT 2B', subtitle: '', color: templateColors.act2b, scenes: [], beats: [] },
  { id: 'act3', title: 'ACT 3', subtitle: '', color: templateColors.act3, scenes: [], beats: [] },
  { id: 'act4', title: 'ACT 4', subtitle: '', color: templateColors.act4, scenes: [], beats: [] },
  { id: 'cliffhanger', title: 'CLIFFHANGER', subtitle: '', color: templateColors.cliffhanger, scenes: [], beats: [] }
];

// Define structure for TV-60 template (60-minute episodes)
export const tv60Template = [
  { id: 'coldopen', title: 'COLD OPEN', subtitle: '', color: templateColors.coldOpen, scenes: [], beats: [] },
  { id: 'act1', title: 'ACT 1', subtitle: '', color: templateColors.act1, scenes: [], beats: [] },
  { id: 'act2a', title: 'ACT 2A', subtitle: '', color: templateColors.act2a, scenes: [], beats: [] },
  { id: 'act2b', title: 'ACT 2B', subtitle: '', color: templateColors.act2b, scenes: [], beats: [] },
  { id: 'act3', title: 'ACT 3', subtitle: '', color: templateColors.act3, scenes: [], beats: [] },
  { id: 'act4', title: 'ACT 4', subtitle: '', color: templateColors.act4, scenes: [], beats: [] },
  { id: 'act5', title: 'ACT 5', subtitle: '', color: templateColors.act5, scenes: [], beats: [] },
  { id: 'cliffhanger', title: 'CLIFFHANGER', subtitle: '', color: templateColors.cliffhanger, scenes: [], beats: [] }
];

// Define structure for Film template (4 sections, now Q1-Q4)
export const filmTemplate = [
  { id: 'setup', title: 'Q1', subtitle: '', color: templateColors.setup, scenes: [], beats: [] },
  { id: 'risingAction', title: 'Q2', subtitle: '', color: templateColors.risingAction, scenes: [], beats: [] },
  { id: 'confrontation', title: 'Q3', subtitle: '', color: templateColors.confrontation, scenes: [], beats: [] },
  { id: 'resolution', title: 'Q4', subtitle: '', color: templateColors.resolution, scenes: [], beats: [] }
];

// Define structure for Novel template (chapters with scenes)
// Create a function that generates a template for a specific chapter
export const novelChapterTemplate = (chapterNumber: number) => ({
  id: `chapter${chapterNumber}`, 
  title: `Chapter ${chapterNumber}`, 
  subtitle: '',
  color: templateColors.chapter, 
  scenes: Array.from({ length: 8 }, (_, i) => ({
    id: Date.now() + (chapterNumber * 1000) + i, // Use chapter number in ID to prevent conflicts
    title: `Scene ${i + 1}`,
    content: '',
    storylineId: null
  })),
  beats: [] 
});

// Generate a template for all 12 chapters
export const novelTemplate = () => 
  Array.from({ length: 12 }, (_, i) => novelChapterTemplate(i + 1));

// Function to get the template structure based on project type
export function getTemplateStructure(projectType: ProjectTemplateType) {
  switch (projectType) {
    case 'tv-30':
      return tv30Template;
    case 'tv-60':
      return tv60Template;
    case 'film':
      return filmTemplate;
    case 'novel':
      return novelTemplate(); // Return all chapter templates
    default:
      return tv30Template; // Default to 30-minute template
  }
}

// Function to get scene counts based on project type
export function getSceneCounts(projectType: ProjectTemplateType): Record<string, number> {
  switch (projectType) {
    case 'tv-30':
      return sceneCounts.tv30;
    case 'tv-60':
      return sceneCounts.tv60;
    case 'film':
      return sceneCounts.film;
    case 'novel':
      return sceneCounts.novel;
    default:
      return sceneCounts.tv30;
  }
}