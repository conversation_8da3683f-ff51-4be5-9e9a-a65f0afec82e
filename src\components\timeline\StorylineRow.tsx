import React, { useState } from 'react';
import { useEpisodeTrackStore } from '../../store/episodeTrackStore';
import { cn } from '../../lib/utils';
import type { Storyline } from '../../types/timeline';

interface StorylineRowProps {
  storyline: Storyline;
  episodes: string[];
}

const colorOptions = [
  { name: 'Pink', value: '#FF3AF2' },
  { name: '<PERSON><PERSON>', value: '#00FFF0' },
  { name: 'Yellow', value: '#FFE600' },
  { name: 'Red', value: '#FF4D4D' },
  { name: 'Green', value: '#4DFF4D' },
  { name: 'Blue', value: '#4D4DFF' }
];

export const StorylineRow: React.FC<StorylineRowProps> = ({
  storyline,
  episodes
}) => {
  const { updateStoryline, removeStoryline, toggleEpisode, moveCard } = useEpisodeTrackStore();
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(storyline.title);
  const [showColorPicker, setShowColorPicker] = useState(false);

  const handleEditComplete = () => {
    if (editName.trim() && editName.trim() !== storyline.title) {
      updateStoryline(storyline.id, { title: editName.trim() });
    }
    setIsEditing(false);
  };

  const handleDragOver = (e: React.DragEvent) => {
    if (e.dataTransfer.types.includes('application/json')) {
      e.preventDefault();
      e.currentTarget.classList.add('bg-gray-100/50');
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.currentTarget.classList.remove('bg-gray-100/50');
  };

  const handleDrop = (e: React.DragEvent, episodeId: string) => {
    e.preventDefault();
    e.currentTarget.classList.remove('bg-gray-100/50');

    try {
      const data = JSON.parse(e.dataTransfer.getData('application/json'));
      if (data.type === 'storyline-card') {
        moveCard(data.cardId, {
          type: 'storyline',
          storylineId: storyline.id,
          episodeId
        });
      }
    } catch (error) {
      console.error('Failed to process drop:', error);
    }
  };

  return (
    <div className="grid grid-cols-[200px_1fr] gap-1">
      {/* Storyline Info */}
      <div className="group relative p-2 font-medium flex items-center gap-2 bg-white border-2 border-black">
        {/* Color Swatch */}
        <div
          className="w-4 h-4 rounded-full border border-black mr-2 cursor-pointer"
          style={{ backgroundColor: storyline.color }}
          title="Change storyline color"
          onClick={() => setShowColorPicker((v) => !v)}
        />
        {/* Color Picker Dropdown */}
        {showColorPicker && (
          <div className="absolute left-0 top-full mt-1 bg-white border-2 border-black rounded shadow-lg z-50 p-2 flex gap-1">
            {colorOptions.map(option => (
              <button
                key={option.value}
                className={cn(
                  'w-5 h-5 rounded-full border-2',
                  option.value === storyline.color ? 'border-black' : 'border-gray-300',
                  'focus:outline-none focus:ring-2 focus:ring-black'
                )}
                style={{ backgroundColor: option.value }}
                onClick={e => {
                  e.stopPropagation();
                  updateStoryline(storyline.id, { color: option.value });
                  setShowColorPicker(false);
                }}
                title={option.name}
              />
            ))}
          </div>
        )}
        {isEditing ? (
          <input
            type="text"
            value={editName}
            onChange={e => setEditName(e.target.value)}
            onBlur={handleEditComplete}
            onKeyDown={e => {
              if (e.key === 'Enter') handleEditComplete();
              if (e.key === 'Escape') setIsEditing(false);
            }}
            className="flex-1 text-sm bg-white border border-gray-300 rounded px-1 py-0.5 focus:outline-none"
            autoFocus
          />
        ) : (
          <span
            className="flex-1 truncate text-sm cursor-pointer hover:underline"
            onClick={() => setIsEditing(true)}
            title="Click to edit storyline name"
          >
            {storyline.title}
          </span>
        )}
      </div>

      {/* Episode Grid */}
      <div className="grid gap-1" style={{ gridTemplateColumns: `repeat(${episodes.length}, 1fr)` }}>
        {episodes.map(episode => {
          const cards = storyline.cards.filter(c => c.episodeId === episode);
          return (
            <div
              key={episode}
              className="relative h-12"
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, episode)}
            >
              <button
                onClick={() => toggleEpisode(storyline.id, episode)}
                className={cn(
                  'w-full h-full border-2 border-black transition-colors',
                  'hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-black'
                )}
                style={{ 
                  backgroundColor: storyline.episodes[episode] ? storyline.color : 'white',
                  opacity: storyline.episodes[episode] ? 1 : 0.5
                }}
              />
              <div className="absolute inset-0 pointer-events-none">
                {cards.map((card, index) => (
                  <div
                    key={card.id}
                    className={cn(
                      'absolute left-1 right-1 p-2',
                      'bg-white/90 border border-black/20',
                      'text-xs rounded shadow-sm',
                      'transform-gpu transition-all duration-300',
                      'timeline-slot-card'
                    )}
                    style={{
                      top: `${index * 2 + 2}px`,
                      zIndex: index,
                      backgroundColor: storyline.color + '20',
                      transform: 'scale(0.8)',
                      transformOrigin: 'top left'
                    }}
                  >
                    <div className="timeline-slot-content whitespace-nowrap overflow-hidden text-ellipsis">
                      {card.content}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};