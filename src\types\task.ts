export interface TaskComment {
  id: string;
  content: string;
  timestamp: number;
  author: string;
}

export interface RevisionNote {
  id: string;
  content: string;
  isProcessed: boolean;
  sceneId?: string;
  noteType?: 'rewrite' | 'review' | 'discuss' | 'research' | 'general';
  createdAt: number;
  collaborators?: string[];
}

export interface Task {
  id: string;
  title: string;
  content: string;
  comments: TaskComment[];
  labels: string[];
  columnId: string;
  revisionNotes?: RevisionNote[];
}

export interface TaskColumn {
  id: string;
  title: string;
  color: string;
}