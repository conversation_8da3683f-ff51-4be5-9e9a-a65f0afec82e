import React, { useState } from 'react';
import { Grip, Edit, X } from 'lucide-react';
import { Button } from '../ui/button';
import { cn } from '../../lib/utils';
import type { DragItem } from '../../types/workshop';

export interface PlotPointCardProps {
  id: string;
  content: string;
  source: 'manual' | 'chat' | 'workshop';
  sectionId?: string; 
  color?: string;
  position?: number;
  storylineId?: string;
  episodeId?: string;
  metadata?: Record<string, any>;
  onUpdate: (id: string, content: string) => void;
  onDelete: (id: string) => void;
}

export const PlotPointCard: React.FC<PlotPointCardProps> = ({
  id,
  content,
  sectionId,
  onUpdate,
  onDelete
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(content);
  const [isDragging, setIsDragging] = useState(false);

  const handleSave = () => {
    if (editContent.trim()) {
      onUpdate(id, editContent.trim());
    }
    setIsEditing(false);
  };

  const handleDragStart = (e: React.DragEvent) => {
    setIsDragging(true);
    e.currentTarget.classList.add('opacity-50');

    const dragData: DragItem = {
      id,
      type: 'plot-point',
      sectionId,
      content
    };

    e.dataTransfer.setData('application/json', JSON.stringify(dragData));
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  return (
    <div 
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      className={cn(
        "bg-white border-2 border-black p-0", 
        "hover:shadow-md hover:-translate-y-0.5",
        "transition-all duration-200 group",
        "h-full min-h-[50px] max-h-[70px]", // Reduced height
        "flex flex-col", 
        isDragging && "opacity-50"
      )}
    >
      {/* Grip handle as a header */}
      <div className="grip-container bg-gray-100 py-0.5 px-1 flex justify-between items-center border-b border-gray-300 cursor-move">
        <Grip className="h-3 w-3 text-gray-600 flex-shrink-0" />
        <Button
          variant="ghost"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            onDelete(id);
          }}
          className="opacity-0 group-hover:opacity-100 transition-opacity h-4 w-4 p-0"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
      
      <div className="flex-1 min-w-0 p-1">
        {isEditing ? (
          <textarea
            value={editContent}
            onChange={(e) => setEditContent(e.target.value)}
            onBlur={handleSave}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSave();
              }
              if (e.key === 'Escape') {
                setEditContent(content);
                setIsEditing(false);
              }
            }}
            className="w-full h-full text-xs border border-gray-300 resize-none focus:outline-none focus:ring-1 focus:ring-black p-1"
            autoFocus
          />
        ) : (
          <div 
            onClick={() => setIsEditing(true)}
            className="w-full h-full text-xs cursor-text font-bold line-clamp-2 overflow-hidden text-ellipsis px-1"
          >
            {content || <span className="text-gray-400">New plot point...</span>}
          </div>
        )}
      </div>
    </div>
  );
};