import React, { useState } from 'react';
import { Palette } from 'lucide-react';
import { Button } from '../ui/button';
import { useEpisodeTrackStore } from '../../store/episodeTrackStore';
import { cn } from '../../lib/utils';

interface StorylineColorPickerProps {
  currentStorylineId: string | null;
  onStorylineChange: (storylineId: string | null) => void;
  buttonSize?: 'sm' | 'md' | 'lg';
  position?: 'top-right' | 'bottom-right' | 'top-left' | 'bottom-left';
  showText?: boolean;
  debug?: boolean;
}

export const StorylineColorPicker: React.FC<StorylineColorPickerProps> = ({
  currentStorylineId,
  onStorylineChange,
  buttonSize = 'sm',
  position = 'top-right',
  showText = false,
  debug = false
}) => {
  const [showPicker, setShowPicker] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Get storylines from the store
  const storylines = useEpisodeTrackStore(state => state.storylines);
  
  if (debug) {
    console.log('StorylineColorPicker render:', { 
      currentStorylineId, 
      storylines: storylines.map(s => ({ id: s.id, title: s.title, color: s.color }))
    });
  }
  
  // Get the current storyline color if it exists
  const currentStoryline = currentStorylineId 
    ? storylines.find(s => s.id === currentStorylineId) 
    : null;
  
  if (debug && currentStorylineId && !currentStoryline) {
    console.warn('Storyline not found:', currentStorylineId);
  }
  
  // Calculate position classes
  const positionClasses = {
    'top-right': 'top-0 right-0',
    'bottom-right': 'bottom-0 right-0',
    'top-left': 'top-0 left-0',
    'bottom-left': 'bottom-0 left-0'
  };
  
  // Calculate dropdown position
  const dropdownPosition = {
    'top-right': 'right-0 top-6',
    'bottom-right': 'right-0 bottom-6',
    'top-left': 'left-0 top-6',
    'bottom-left': 'left-0 bottom-6'
  };
  
  // Button size variants
  const sizeClasses = {
    'sm': 'h-5 w-5 p-0',
    'md': 'h-7 w-7 p-0',
    'lg': 'h-9 w-9 p-0'
  };
  
  const iconSizes = {
    'sm': 'h-3 w-3',
    'md': 'h-4 w-4',
    'lg': 'h-5 w-5'
  };

  const handleStorylineChange = (storylineId: string | null) => {
    try {
      if (debug) {
        console.log('handleStorylineChange:', { storylineId });
      }
      
      // Call the parent handler
      onStorylineChange(storylineId);
      
      // Close the picker
      setShowPicker(false);
      setError(null);
    } catch (error) {
      console.error('Error changing storyline:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
    }
  };
  
  return (
    <div className="relative">
      <Button
        variant="ghost"
        onClick={(e) => {
          e.stopPropagation();
          setShowPicker(!showPicker);
          setError(null);
        }}
        className={cn(
          sizeClasses[buttonSize],
          "hover:bg-gray-100 rounded-full",
          "absolute",
          positionClasses[position],
          "z-10",
          "bg-white/80 shadow-sm border border-gray-200"
        )}
        title="Assign to Storyline"
      >
        <div 
          className={cn(
            "rounded-full flex items-center justify-center",
            iconSizes[buttonSize]
          )}
          style={currentStorylineId && currentStoryline 
            ? { 
                backgroundColor: currentStoryline.color,
                color: "#fff"
              } 
            : { 
                backgroundColor: "#e5e7eb", 
                color: "#9ca3af" 
              }
          }
        >
          <Palette className={iconSizes[buttonSize]} />
        </div>
        {showText && <span className="ml-1 text-xs">Storyline</span>}
      </Button>
      
      {/* Error message */}
      {error && (
        <div className="absolute right-0 top-full mt-1 bg-red-50 border-2 border-red-500 rounded shadow-lg z-50 p-2 text-xs text-red-600 max-w-[200px]">
          Error: {error}
        </div>
      )}
      
      {showPicker && (
        <div 
          className={cn(
            "absolute bg-white border-2 border-gray-300 shadow-lg rounded-md z-50 p-2 min-w-[150px]",
            dropdownPosition[position]
          )}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="font-bold text-xs mb-2 text-gray-600">Assign Storyline:</div>
          <div className="space-y-1">
            <div 
              className="flex items-center gap-2 p-1 hover:bg-gray-100 rounded cursor-pointer" 
              onClick={() => {
                handleStorylineChange(null);
              }}
            >
              <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
              <span className="text-xs">None</span>
            </div>
            
            {storylines.map(storyline => (
              <div 
                key={`storyline-option-${storyline.id}`}
                className={cn(
                  "flex items-center gap-2 p-1 hover:bg-gray-100 rounded cursor-pointer",
                  currentStorylineId === storyline.id && "bg-gray-100"
                )}
                onClick={() => {
                  handleStorylineChange(storyline.id);
                }}
              >
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: storyline.color }}
                ></div>
                <span className="text-xs">{storyline.title}</span>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* Debug info if enabled */}
      {debug && (
        <div className="absolute bottom-full right-0 bg-black text-white text-[8px] p-1 z-50">
          ID: {currentStorylineId || 'none'}
        </div>
      )}
    </div>
  );
};