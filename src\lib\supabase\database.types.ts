export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          display_name: string | null
          avatar_url: string | null
          created_at: string
          last_login: string | null
        }
        Insert: {
          id: string
          email: string
          display_name?: string | null
          avatar_url?: string | null
          created_at?: string
          last_login?: string | null
        }
        Update: {
          id?: string
          email?: string
          display_name?: string | null
          avatar_url?: string | null
          created_at?: string
          last_login?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "users_id_fkey"
            columns: ["id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      projects: {
        Row: {
          id: string
          name: string
          type: string
          status: string
          template: string
          user_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          type: string
          status?: string
          template?: string
          user_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          type?: string
          status?: string
          template?: string
          user_id?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "projects_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      episodes: {
        Row: {
          id: string
          project_id: string
          number: number
          title: string
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          number: number
          title: string
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          number?: number
          title?: string
          status?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "episodes_project_id_fkey"
            columns: ["project_id"]
            referencedRelation: "projects"
            referencedColumns: ["id"]
          }
        ]
      }
      acts: {
        Row: {
          id: string
          episode_id: string
          title: string
          subtitle: string | null
          color: string
          position: number
          storyline_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          episode_id: string
          title: string
          subtitle?: string | null
          color: string
          position: number
          storyline_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          episode_id?: string
          title?: string
          subtitle?: string | null
          color?: string
          position?: number
          storyline_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "acts_episode_id_fkey"
            columns: ["episode_id"]
            referencedRelation: "episodes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "acts_storyline_id_fkey"
            columns: ["storyline_id"]
            referencedRelation: "storylines"
            referencedColumns: ["id"]
          }
        ]
      }
      scenes: {
        Row: {
          id: string
          act_id: string
          title: string
          content: string | null
          position: number
          storyline_id: string | null
          is_complete: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          act_id: string
          title: string
          content?: string | null
          position: number
          storyline_id?: string | null
          is_complete?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          act_id?: string
          title?: string
          content?: string | null
          position?: number
          storyline_id?: string | null
          is_complete?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "scenes_act_id_fkey"
            columns: ["act_id"]
            referencedRelation: "acts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "scenes_storyline_id_fkey"
            columns: ["storyline_id"]
            referencedRelation: "storylines"
            referencedColumns: ["id"]
          }
        ]
      }
      storylines: {
        Row: {
          id: string
          project_id: string
          title: string
          color: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          title: string
          color: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          title?: string
          color?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "storylines_project_id_fkey"
            columns: ["project_id"]
            referencedRelation: "projects"
            referencedColumns: ["id"]
          }
        ]
      }
      storyline_episodes: {
        Row: {
          storyline_id: string
          episode_id: string
          is_active: boolean
        }
        Insert: {
          storyline_id: string
          episode_id: string
          is_active?: boolean
        }
        Update: {
          storyline_id?: string
          episode_id?: string
          is_active?: boolean
        }
        Relationships: [
          {
            foreignKeyName: "storyline_episodes_episode_id_fkey"
            columns: ["episode_id"]
            referencedRelation: "episodes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "storyline_episodes_storyline_id_fkey"
            columns: ["storyline_id"]
            referencedRelation: "storylines"
            referencedColumns: ["id"]
          }
        ]
      }
      // Add more tables as needed
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_character_appearances: {
        Args: {
          character_id_param: string
          project_id_param: string
        }
        Returns: {
          scene_id: string
          episode_number: number
          act_title: string
          scene_title: string
          is_active: boolean
          emotional_state: string | null
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}