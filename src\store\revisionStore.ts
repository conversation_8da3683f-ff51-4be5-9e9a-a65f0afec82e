import { create } from 'zustand';
import type { RevisionNote } from '../types/task';
import { getProjectScopedKey } from '../utils/storageUtils';
import { logger } from '../utils/logger';

interface RevisionState {
  notes: RevisionNote[];
  activeNote: string | null;
  addNote: (content: string) => string;
  updateNote: (id: string, updates: Partial<RevisionNote>) => void;
  deleteNote: (id: string) => void;
  markAsProcessed: (id: string, processed: boolean) => void;
  sendToScene: (id: string, sceneId: string, noteType: string, customContent?: string) => void;
  addCollaborator: (id: string, email: string) => void;
  removeCollaborator: (id: string, email: string) => void;
}

// Function to get current project ID from localStorage
const getCurrentProjectId = (): string | undefined => {
  try {
    // Get projects from storage
    const projectsStr = localStorage.getItem('story_unstuck_projects');
    if (!projectsStr) {
      logger.warn('No projects found in localStorage');
      return undefined;
    }
    
    const projects = JSON.parse(projectsStr);
    if (!Array.isArray(projects) || projects.length === 0) {
      logger.warn('No projects array or empty projects in localStorage');
      return undefined;
    }
    
    // The first project in the array is typically the current one
    // This is based on how the app manages projects
    const currentProject = projects[0];
    if (currentProject && currentProject.id) {
      logger.debug('Found current project ID:', currentProject.id);
      return currentProject.id;
    }
    
    logger.warn('No project ID found in first project');
    return undefined;
  } catch (error) {
    logger.error('Failed to get current project ID:', error);
    return undefined;
  }
};

export const useRevisionStore = create<RevisionState>((set, get) => ({
  notes: [],
  activeNote: null,
  
  addNote: (content) => {
    const id = `rev-${Date.now()}`;
    set((state) => ({
      notes: [...state.notes, {
        id,
        content,
        isProcessed: false,
        createdAt: Date.now()
      }]
    }));
    return id;
  },
  
  updateNote: (id, updates) => {
    set((state) => ({
      notes: state.notes.map(note => 
        note.id === id ? { ...note, ...updates } : note
      )
    }));
  },
  
  deleteNote: (id) => {
    set((state) => ({
      notes: state.notes.filter(note => note.id !== id)
    }));
  },
  
  markAsProcessed: (id, processed) => {
    set((state) => ({
      notes: state.notes.map(note => 
        note.id === id ? { ...note, isProcessed: processed } : note
      )
    }));
  },
  
  sendToScene: (id, sceneId, noteType, customContent) => {
    // Get the revision note
    const note = get().notes.find(n => n.id === id);
    if (!note) {
      logger.error('Note not found:', id);
      return;
    }
    
    // Use custom content if provided, otherwise use the full note content
    const contentToSend = customContent || note.content;
    
    // Update the revision note with scene info in our store
    get().updateNote(id, { 
      sceneId, 
      noteType: noteType as 'rewrite' | 'review' | 'discuss' | 'research' | 'general', 
      isProcessed: true 
    });
    
    // Get current project ID
    const projectId = getCurrentProjectId();
    if (!projectId) {
      logger.error('Could not determine current project ID');
      
      // FALLBACK: Try direct approach using the scene ID
      try {
        // Try using the legacy storage key as a fallback
        const legacyKey = `write_scene_${sceneId}`;
        
        // Just write directly to the legacy key without project scoping
        let sceneData = null;
        const legacyData = localStorage.getItem(legacyKey);
        
        if (legacyData) {
          sceneData = JSON.parse(legacyData);
        }
        
        // Create the new note
        const sceneNote = {
          id: `revision-${Date.now()}`,
          user: 'Revision Notes',
          content: contentToSend,
          time: new Date().toLocaleString(),
          type: noteType as 'rewrite' | 'review' | 'discuss' | 'research' | 'general',
          isResolved: false,
          source: 'revision'
        };
        
        if (sceneData) {
          // Add note to existing data
          sceneData.notes = sceneData.notes || [];
          sceneData.notes.push(sceneNote);
          
          // Update revision state if needed
          if (noteType === 'rewrite' && sceneData.revisionState !== 'in-revision') {
            sceneData.revisionState = 'needs-review';
          }
          
          // Save back to localStorage
          localStorage.setItem(legacyKey, JSON.stringify(sceneData));
          
          // Trigger update events
          window.dispatchEvent(new StorageEvent('storage', {
            key: legacyKey,
            newValue: localStorage.getItem(legacyKey)
          }));
          
          window.dispatchEvent(new CustomEvent('sceneContentChanged', {
            detail: {
              key: legacyKey,
              newValue: localStorage.getItem(legacyKey)
            }
          }));
          
          logger.debug('FALLBACK: Successfully sent note to scene using legacy key', { sceneId, legacyKey });
          return;
        } else {
          // Create new scene data
          const newSceneData = {
            id: sceneId.split('-').pop() || sceneId,
            title: 'Scene',
            content: '',
            notes: [sceneNote],
            characters: [],
            emotionalStart: 'Neutral',
            emotionalEnd: 'Neutral',
            emotionalProgress: 50,
            isPositiveArc: true,
            beats: [],
            revisionState: noteType === 'rewrite' ? 'needs-review' : 'not-started',
            isComplete: false,
            lastEdited: new Date().toISOString()
          };
          
          // Save to localStorage using legacy key
          localStorage.setItem(legacyKey, JSON.stringify(newSceneData));
          
          // Trigger update events
          window.dispatchEvent(new StorageEvent('storage', {
            key: legacyKey,
            newValue: localStorage.getItem(legacyKey)
          }));
          
          window.dispatchEvent(new CustomEvent('sceneContentChanged', {
            detail: {
              key: legacyKey,
              newValue: localStorage.getItem(legacyKey)
            }
          }));
          
          logger.debug('FALLBACK: Created new scene with note using legacy key', { sceneId, legacyKey });
          return;
        }
      } catch (fallbackError) {
        logger.error('Fallback approach also failed:', fallbackError);
        return;
      }
    }
    
    logger.debug('Sending note to scene', { projectId, sceneId, noteType });
    
    // Add the note to the scene in localStorage
    try {
      // First look for project-scoped scene data
      const sceneKey = getProjectScopedKey(projectId, `scene_${sceneId}`);
      
      // Look for the scene data
      let sceneData = null;
      let storageKey = sceneKey;
      
      // Try project-scoped key first
      const projectScopedData = localStorage.getItem(sceneKey);
      if (projectScopedData) {
        sceneData = JSON.parse(projectScopedData);
        logger.debug('Found scene data with project-scoped key', { sceneId });
      }
      
      // If not found, try the legacy key
      if (!sceneData) {
        const legacyKey = `write_scene_${sceneId}`;
        const legacyData = localStorage.getItem(legacyKey);
        if (legacyData) {
          sceneData = JSON.parse(legacyData);
          storageKey = legacyKey;
          logger.debug('Found scene data with legacy key', { sceneId });
        }
      }
      
      // Create the new note object
      const sceneNote = {
        id: `revision-${Date.now()}`,
        user: 'Revision Notes',
        content: contentToSend,
        time: new Date().toLocaleString(),
        type: noteType as 'rewrite' | 'review' | 'discuss' | 'research' | 'general',
        isResolved: false,
        source: 'revision'
      };
      
      if (sceneData) {
        // Add the note to existing scene data
        sceneData.notes = sceneData.notes || [];
        sceneData.notes.push(sceneNote);
        
        // Update revision state if needed
        if (noteType === 'rewrite' && sceneData.revisionState !== 'in-revision') {
          sceneData.revisionState = 'needs-review';
        }
        
        // Save back to localStorage
        localStorage.setItem(storageKey, JSON.stringify(sceneData));
        
        // If we found it in the legacy location but have a project ID, also save to project-scoped location
        if (storageKey !== sceneKey) {
          localStorage.setItem(sceneKey, JSON.stringify(sceneData));
          logger.debug('Migrated scene data to project-scoped key', { 
            from: storageKey, 
            to: sceneKey 
          });
        }
      } else {
        // Initialize new scene data
        const newSceneData = {
          id: sceneId.split('-').pop() || sceneId, // Extract just the scene number
          title: 'Scene',
          content: '',
          notes: [sceneNote],
          characters: [],
          emotionalStart: 'Neutral',
          emotionalEnd: 'Neutral',
          emotionalProgress: 50,
          isPositiveArc: true,
          beats: [],
          revisionState: noteType === 'rewrite' ? 'needs-review' : 'not-started',
          isComplete: false,
          lastEdited: new Date().toISOString()
        };
        
        // Save to localStorage using the project-scoped key
        localStorage.setItem(sceneKey, JSON.stringify(newSceneData));
        logger.debug('Created new scene data with note', { sceneKey });
      }
      
      // Dispatch a storage event to notify other components
      window.dispatchEvent(new StorageEvent('storage', {
        key: storageKey,
        newValue: localStorage.getItem(storageKey)
      }));
      
      // Also dispatch a custom event for components using the event listener
      window.dispatchEvent(new CustomEvent('sceneContentChanged', {
        detail: {
          key: storageKey,
          newValue: localStorage.getItem(storageKey)
        }
      }));
      
      logger.debug('Successfully sent note to scene', { 
        sceneId, 
        storageKey,
        contentLength: contentToSend.length,
        isCustomContent: Boolean(customContent)
      });
    } catch (error) {
      logger.error('Failed to add note to scene:', error);
    }
  },
  
  addCollaborator: (id, email) => {
    set((state) => ({
      notes: state.notes.map(note => 
        note.id === id 
          ? { 
              ...note, 
              collaborators: [...(note.collaborators || []), email]
            } 
          : note
      )
    }));
  },
  
  removeCollaborator: (id, email) => {
    set((state) => ({
      notes: state.notes.map(note => 
        note.id === id 
          ? { 
              ...note, 
              collaborators: (note.collaborators || []).filter(e => e !== email)
            } 
          : note
      )
    }));
  }
})); 