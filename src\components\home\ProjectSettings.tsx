import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from '../ui/dialog';
import { Button } from '../ui/button';
import { AlertTriangle, Trash2, Refresh<PERSON>w, ToggleLeft, ToggleRight } from 'lucide-react';
import { clearProjectSceneData, initializeBlankEpisode, saveBlankEpisodeData, createEmptyTemplate } from '../../utils/templateUtils';
import { getTemplateStructure } from '../../constants/templates';
import { useProject } from '../../contexts/ProjectContext';
import { logger } from '../../utils/logger';
import { stripScenesFromEpisodeData, resetProjectToBlankState } from '../utils/sceneDataCleaner';

interface ProjectSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ProjectSettings: React.FC<ProjectSettingsProps> = ({
  isOpen,
  onClose
}) => {
  const { currentProject } = useProject();
  const [isClearing, setIsClearing] = useState(false);
  const [useBlankTemplates, setUseBlankTemplates] = useState(() => 
    localStorage.getItem('use_blank_templates') === 'true'
  );
  const [useEmptyTemplates, setUseEmptyTemplates] = useState(() => 
    localStorage.getItem('use_empty_templates') === 'true'
  );
  const [clearResult, setClearResult] = useState<{
    success: boolean;
    message: string;
    count?: number;
  } | null>(null);

  // Toggle blank templates mode
  const toggleBlankTemplates = () => {
    const newValue = !useBlankTemplates;
    setUseBlankTemplates(newValue);
    localStorage.setItem('use_blank_templates', newValue.toString());
    
    setClearResult({
      success: true,
      message: newValue 
        ? 'Blank templates enabled. New episodes will have no pre-populated scenes.' 
        : 'Default templates enabled. New episodes will include default scenes.'
    });
  };
  
  // Toggle empty templates mode
  const toggleEmptyTemplates = () => {
    const newValue = !useEmptyTemplates;
    setUseEmptyTemplates(newValue);
    localStorage.setItem('use_empty_templates', newValue.toString());
    
    setClearResult({
      success: true,
      message: newValue 
        ? 'Empty templates enabled. New episodes will have empty scene structures.' 
        : 'Empty templates disabled. New episodes will use the selected template structure.'
    });
  };

  const handleClearSceneData = () => {
    if (!currentProject) {
      setClearResult({
        success: false,
        message: 'No project is currently open'
      });
      return;
    }

    if (confirm('Are you sure you want to clear all scene data? This action cannot be undone.')) {
      setIsClearing(true);
      try {
        // Clear all scene data for this project
        const clearedCount = clearProjectSceneData(currentProject.id);
        
        // Initialize with blank templates
        if (currentProject.type) {
          const templateStructure = getTemplateStructure(currentProject.type);
          const blankEpisodeData = initializeBlankEpisode(1, templateStructure);
          saveBlankEpisodeData(currentProject.id, blankEpisodeData);
        }
        
        setClearResult({
          success: true,
          message: 'Scene data cleared successfully',
          count: clearedCount
        });
        
        logger.debug('Scene data cleared successfully', { 
          projectId: currentProject.id, 
          count: clearedCount 
        });
      } catch (error) {
        setClearResult({
          success: false,
          message: `Error clearing scene data: ${error instanceof Error ? error.message : 'Unknown error'}`
        });
        logger.error('Failed to clear scene data', error);
      } finally {
        setIsClearing(false);
      }
    }
  };

  const handleStripScenes = () => {
    if (!currentProject) {
      setClearResult({
        success: false,
        message: 'No project is currently open'
      });
      return;
    }

    if (confirm('This will remove all scenes from the current project while preserving episode structure. Continue?')) {
      setIsClearing(true);
      try {
        // Strip scenes from episode data
        const success = stripScenesFromEpisodeData(currentProject.id);
        
        setClearResult({
          success,
          message: success 
            ? 'Successfully removed all scenes from this project while preserving episode structure' 
            : 'Failed to strip scenes from episode data'
        });
      } catch (error) {
        setClearResult({
          success: false,
          message: `Error stripping scenes: ${error instanceof Error ? error.message : 'Unknown error'}`
        });
      } finally {
        setIsClearing(false);
      }
    }
  };

  const handleReinitializeWithBlankTemplates = () => {
    if (!currentProject) {
      setClearResult({
        success: false,
        message: 'No project is currently open'
      });
      return;
    }

    if (confirm('This will replace all episodes with blank templates and clear all scene data. Continue?')) {
      setIsClearing(true);
      try {
        // Use the more comprehensive reset function
        const success = resetProjectToBlankState(currentProject.id);
        
        setClearResult({
          success,
          message: success 
            ? 'Project has been reset to a completely blank state. All scene data has been cleared.' 
            : 'Failed to reset project'
        });
        
        // Update the UI state for blank templates
        setUseBlankTemplates(true);
      } catch (error) {
        setClearResult({
          success: false,
          message: `Error reinitializing project: ${error instanceof Error ? error.message : 'Unknown error'}`
        });
      } finally {
        setIsClearing(false);
      }
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="border-4 border-black">
        <DialogHeader>
          <DialogTitle>Project Settings</DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="space-y-2">
            <h3 className="text-lg font-bold">Template Management</h3>
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm text-gray-600 mb-2">
                  Manage scene data and templates for your project.
                </p>
                <div className="space-y-2">
                  <div className="flex items-center justify-between border-b border-gray-200 pb-2">
                    <div>
                      <p className="font-medium">Blank Templates</p>
                      <p className="text-xs text-gray-500">No pre-populated scenes in new episodes</p>
                    </div>
                    <Button
                      variant="ghost"
                      onClick={toggleBlankTemplates}
                      className="flex items-center gap-2"
                    >
                      {useBlankTemplates ? (
                        <ToggleRight className="h-5 w-5 text-green-600" />
                      ) : (
                        <ToggleLeft className="h-5 w-5 text-gray-400" />
                      )}
                    </Button>
                  </div>
                  
                  <div className="flex items-center justify-between pt-2">
                    <div>
                      <p className="font-medium">Empty Scene Structures</p>
                      <p className="text-xs text-gray-500">Initialize with empty scene arrays but maintain structure</p>
                    </div>
                    <Button
                      variant="ghost"
                      onClick={toggleEmptyTemplates}
                      className="flex items-center gap-2"
                    >
                      {useEmptyTemplates ? (
                        <ToggleRight className="h-5 w-5 text-green-600" />
                      ) : (
                        <ToggleLeft className="h-5 w-5 text-gray-400" />
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="border-2 border-black p-4 rounded-md bg-gray-50">
              <h4 className="font-bold mb-2">Option 1: Strip Scenes</h4>
              <p className="text-sm mb-4">
                Removes all scenes while preserving episode and act structure.
                This is the safest option.
              </p>
              <Button 
                onClick={handleStripScenes}
                disabled={isClearing || !currentProject}
                className="bg-yellow-600 hover:bg-yellow-700 text-white w-full"
              >
                Strip All Scenes
              </Button>
            </div>

            <div className="border-2 border-black p-4 rounded-md bg-gray-50">
              <h4 className="font-bold mb-2">Option 2: Reset to Blank</h4>
              <p className="text-sm mb-4">
                Completely resets the project to a blank state and enables blank templates mode.
                This is recommended for a fresh start.
              </p>
              <Button 
                onClick={handleReinitializeWithBlankTemplates}
                disabled={isClearing || !currentProject}
                className="bg-blue-600 hover:bg-blue-700 text-white w-full"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Reset to Blank State
              </Button>
            </div>
            
            <div className="border-2 border-black p-4 rounded-md bg-gray-50">
              <h4 className="font-bold mb-2">Option 3: Clear All Data</h4>
              <p className="text-sm mb-4">
                Completely removes ALL scene content from your project.
                This action cannot be undone.
              </p>
              <Button 
                onClick={handleClearSceneData}
                disabled={isClearing || !currentProject}
                className="bg-red-600 hover:bg-red-700 text-white w-full"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Clear All Scene Data
              </Button>
            </div>
          </div>

          {clearResult && (
            <div className={`p-4 rounded-md ${clearResult.success ? 'bg-green-100 border-green-500' : 'bg-red-100 border-red-500'} border-2`}>
              <div className="flex items-start">
                {!clearResult.success && <AlertTriangle className="h-5 w-5 text-red-600 mr-2 flex-shrink-0 mt-0.5" />}
                <div>
                  <p className={`font-bold ${clearResult.success ? 'text-green-800' : 'text-red-800'}`}>
                    {clearResult.success ? 'Success' : 'Error'}
                  </p>
                  <p className="text-sm">
                    {clearResult.message}
                    {clearResult.count !== undefined && ` (${clearResult.count} items cleared)`}
                  </p>
                  {clearResult.success && (
                    <p className="text-sm mt-2 text-gray-600">
                      Refresh the page to see the changes take effect.
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="ghost" onClick={onClose} className="border-2 border-black">
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};