import { logger } from './logger';

/**
 * Utility to clear all scene data from local storage
 */

/**
 * Finds all scene-related keys in localStorage for a specific project
 * @param projectId Optional project ID to limit the search
 * @returns Array of localStorage keys
 */
export function findSceneKeys(projectId?: string): string[] {
  const keys = [];
  
  // Patterns to match
  const patterns = [
    // Project-specific scene data
    projectId ? `project_${projectId}_scene_` : 'project_',
    // Project-specific episode data
    projectId ? `project_${projectId}_episode_data` : 'episode_data',
    // Legacy keys
    'write_scene_',
    'episode_data'
  ];
  
  // Find all matching keys
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && patterns.some(pattern => key.includes(pattern))) {
      keys.push(key);
    }
  }
  
  return keys;
}

/**
 * Clears all scene data from localStorage
 * @param projectId Optional project ID to limit the clearing
 * @returns Number of items cleared
 */
export function clearAllSceneData(projectId?: string): number {
  const keys = findSceneKeys(projectId);
  
  // Remove all found keys
  keys.forEach(key => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      logger.error(`Failed to remove key ${key}:`, error);
    }
  });
  
  logger.debug(`Cleared ${keys.length} scene-related items from localStorage`);
  return keys.length;
}

/**
 * Enables blank templates mode
 * @param enable Whether to enable or disable blank templates
 */
export function setBlankTemplatesMode(enable: boolean): void {
  localStorage.setItem('use_blank_templates', enable.toString());
  logger.debug(`Set blank templates mode to: ${enable}`);
}

/**
 * Checks if blank templates mode is enabled
 * @returns True if blank templates mode is enabled
 */
export function isBlankTemplatesModeEnabled(): boolean {
  return localStorage.getItem('use_blank_templates') === 'true';
}

/**
 * Analyzes scene data in localStorage
 * @param projectId Optional project ID to limit the analysis
 * @returns Analysis results
 */
export function analyzeSceneData(projectId?: string): {
  totalKeys: number;
  sceneKeys: number;
  episodeDataKeys: number;
  totalSize: number;
  largestKey: { key: string; size: number } | null;
} {
  const keys = findSceneKeys(projectId);
  let totalSize = 0;
  let largestKey = { key: '', size: 0 };
  
  const sceneKeys = keys.filter(k => k.includes('scene_')).length;
  const episodeDataKeys = keys.filter(k => k.includes('episode_data')).length;
  
  // Calculate total size and find largest key
  keys.forEach(key => {
    try {
      const value = localStorage.getItem(key);
      if (value) {
        const size = value.length;
        totalSize += size;
        
        if (size > largestKey.size) {
          largestKey = { key, size };
        }
      }
    } catch (error) {
      logger.error(`Failed to analyze key ${key}:`, error);
    }
  });
  
  return {
    totalKeys: keys.length,
    sceneKeys,
    episodeDataKeys,
    totalSize,
    largestKey: largestKey.key ? largestKey : null
  };
}