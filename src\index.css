@tailwind base;
@tailwind components;
@tailwind utilities;

/* Theme variables */
:root {
  /* Base colors */
  --color-primary: #000000;
  --color-primary-hover: #1a1a1a;
  --color-primary-active: #333333;

  --color-accent: #00FFF0;
  --color-accent-hover: #00e6d9;
  --color-accent-active: #00ccc2;

  /* Secondary colors */
  --color-secondary-pink: #FF3AF2;
  --color-secondary-yellow: #FFE600;
  --color-secondary-green: #4DFF4D;

  /* Semantic colors */
  --color-success: #4DFF4D;
  --color-warning: #FFE600;
  --color-error: #FF4D4D;
  --color-info: #00FFF0;

  /* Neutral colors */
  --color-background: #ffffff;
  --color-foreground: #000000;
  --color-gray-50: #fafafa;
  --color-gray-100: #f5f5f5;
  --color-gray-200: #e5e5e5;
  --color-gray-300: #d4d4d4;
  --color-gray-400: #a3a3a3;
  --color-gray-500: #737373;
  --color-gray-600: #525252;
  --color-gray-700: #404040;
  --color-gray-800: #262626;
  --color-gray-900: #171717;
}

/* Dark mode variables */
.dark {
  --color-background: #171717;
  --color-foreground: #ffffff;
  --color-gray-50: #262626;
  --color-gray-100: #404040;
  --color-gray-200: #525252;
  --color-gray-300: #737373;
  --color-gray-400: #a3a3a3;
  --color-gray-500: #d4d4d4;
  --color-gray-600: #e5e5e5;
  --color-gray-700: #f5f5f5;
  --color-gray-800: #fafafa;
  --color-gray-900: #ffffff;
}

/* High contrast mode */
.high-contrast {
  --color-primary: #000000;
  --color-primary-hover: #333333;
  --color-primary-active: #666666;

  --color-accent: #00CCFF;
  --color-accent-hover: #00AADD;
  --color-accent-active: #0088BB;

  --color-secondary-pink: #FF00FF;
  --color-secondary-yellow: #FFFF00;
  --color-secondary-green: #00FF00;

  --color-success: #00FF00;
  --color-warning: #FFFF00;
  --color-error: #FF0000;
  --color-info: #00CCFF;
}

/* Font size adjustments for different zoom levels */
.text-small {
  font-size: 0.9rem;
}

.text-small .text-sm {
  font-size: 0.7rem !important;
}

.text-small button {
  padding: 0.3rem 0.6rem;
}

.text-small nav {
  padding-top: 0.3rem;
  padding-bottom: 0.3rem;
}

.text-small .adjustpadding {
  padding-top: 2rem;
}

/* Normal is default, no class needed */

/* Large text mode */
.text-large {
  font-size: 1.2rem;
}

/* Make navigation more compact on smaller screens */
@media (max-width: 1366px) {
  nav .text-sm {
    font-size: 0.7rem !important;
  }

  nav button {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .theme-toggle-buttons {
    margin-left: -1rem;
    position: relative;
    right: 0;
    z-index: 60;
  }
}

/* For very narrow screens, ensure theme toggle is visible */
@media (max-width: 1100px) {
  .theme-toggle-buttons {
    position: absolute;
    right: 0.5rem;
    top: 0.5rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 9999px;
    padding: 0.25rem;
    z-index: 60;
  }

  .dark .theme-toggle-buttons {
    background: rgba(23, 23, 23, 0.8);
  }
}

/* Zoom scale modes */
.scale-75 {
  font-size: 0.75rem;
}

.scale-90 {
  font-size: 0.9rem;
}

.scale-100 {
  font-size: 1rem;
}

.scale-110 {
  font-size: 1.1rem;
}

/* Ensure navigation bar is always visible when zooming */
nav.fixed.top-0.left-0.right-0.z-50,
nav[role="navigation"] {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 9999 !important;
  width: 100% !important;
  background: white !important;
  min-height: 4rem !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

/* Scale container for proper content scaling */
.scale-container {
  width: 100% !important;
  min-width: 0 !important;
  max-width: 100% !important;
  height: 100vh !important;
  min-height: 100vh !important;
  padding-top: 4rem !important;
  margin-top: 0 !important;
  box-sizing: border-box;
  overflow-x: hidden !important;
  display: flex;
  flex-direction: column;
}

/* Scene grid layout */
.scene-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 0.5rem;
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  box-sizing: border-box;
  overflow-x: hidden !important;
}

/* Scene slot styles */
.scene-slot {
  width: 100%;
  min-width: 0;
  flex: 1 1 0%;
  box-sizing: border-box;
  height: 72px; /* Fixed height for all slots */
  transition: all 0.2s ease-in-out;
}

/* Scene card styles */
.scene-card {
  width: 100%;
  min-width: 0;
  flex: 1 1 0%;
  box-sizing: border-box;
  height: 72px; /* Fixed height for all cards */
  display: flex;
  flex-direction: column;
  transition: all 0.2s ease-in-out;
}

/* Revision state styles - only affect background color */
.scene-card[data-revision-state="complete"] {
  background-color: #f0fdf4 !important;
}

.scene-card[data-revision-state="in-revision"] {
  background-color: #fff7ed !important;
}

.scene-card[data-revision-state="needs-review"] {
  background-color: #fef2f2 !important;
}

/* Scene content transition */
.scene-content {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  height: 48px; /* Fixed content height */
  line-height: 16px; /* Fixed line height */
  font-size: 12px; /* Fixed font size */
}

/* Empty scene content placeholder */
.scene-content:empty::before {
  content: 'Empty scene...';
  color: #a3a3a3;
  font-style: italic;
  line-height: 16px;
}

/* Ensure consistent line heights */
.scene-card .font-mono {
  line-height: 16px;
}

/* Truncation styles */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  /* Consider line-height and max-height for precise control if needed */
  /* Example:
  line-height: 1.5em; 
  max-height: 3em; 
  */
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  max-height: 48px; /* 3 lines * 16px line height */
}

/* Drag and drop animations */
@keyframes pulse-border {
  0% { border-color: rgba(0, 0, 0, 0.2); }
  50% { border-color: rgba(0, 0, 0, 0.5); }
  100% { border-color: rgba(0, 0, 0, 0.2); }
}

.drag-over {
  animation: pulse-border 1s infinite;
}

/* Scene card animations */
.scene-card-enter {
  opacity: 0;
  transform: translateY(10px);
}

.scene-card-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.scene-card-exit {
  opacity: 1;
}

.scene-card-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 300ms, transform 300ms;
}

/* Reordering animation */
.scene-reorder {
  transition: transform 0.2s ease-in-out;
}

/* Full width layout */
.full-width-layout {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

/* Episode columns layout */
.episode-columns-container {
  display: flex;
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  height: 100%;
  overflow-x: hidden !important;
}

.episode-column {
  flex: 1 1 0%;
  min-width: 200px;
  border-right: 4px solid black;
  height: 100%;
  overflow-y: auto;
  width: 100%;
  max-width: 100vw !important;
  box-sizing: border-box;
}

/* Compact episode spine */
.episode-spine-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 4rem);
  border-left: 4px solid black;
  background-color: white;
  position: sticky;
  right: 0;
  top: 0;
  z-index: 20;
}

/* Timeline slot card */
.timeline-slot-card {
  width: 100%;
  height: 100%;
  overflow: hidden;
  transition: all 0.3s ease;
  max-height: none !important;
  overflow: visible !important;
}

.timeline-slot-content {
  transform-origin: top left;
  text-overflow: ellipsis;
}

/* Scene card expanded state */
.scene-card[data-expanded="true"] {
  height: auto;
  min-height: 72px;
}

.scene-card[data-expanded="true"] .scene-content {
  min-height: 48px;
}

/* Scene card padding and spacing */
.scene-card {
  padding: 8px;
}

.scene-content {
  padding: 4px;
}

/* Scene card header */
.scene-card-header {
  height: 24px;
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

/* Scene card footer */
.scene-card-footer {
  height: 24px;
  display: flex;
  align-items: center;
  margin-top: 4px;
}

/* Episode expand/collapse transitions */
.episode-column {
  transition: width 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.episode-column-expanded {
  width: 100% !important;
}

.episode-column-collapsed {
  width: 24rem !important;
}

.shrinksize, .expandsize {
  height: calc(100vh - 4rem) !important;
  overflow: hidden !important;
  width: 100% !important;
  max-width: 100% !important;
}

.adjustsize{
  margin-left: 0;
  margin-top: 0;
}

.text-sm{
  font-size: 0.875rem !important;
}

.text-sm{
  @media only screen and (max-width: 600px) {
     font-size: 0.75rem !important;
  }
}

.adjustposition{
  top: 45px;
  left:-50px;
  width: 200px;
}

.bordertop{
  border-top-width:4px;
}

.adjustpadding{
  padding-top:0.5rem;
  height: calc(100vh - 4rem);
  width: 100%;
  max-width: 100vw;
}

.marginmove {
  margin-top:0;
}

.movedown{
  position: relative;
  top: 12px;
}

.movebutton{
  margin-top:20px;
}

.scenecontent {
  width: 100%;
  min-width: 0;
  flex: 1 1 0%;
  overflow-y: auto;
  box-sizing: border-box;
}

.adjusttextsize{
  width:305px;
  margin-top:-10px;
}

.adjustbuttonstop{
  margin-top:-12px;
}

.floatright{
  float:right;
}

.moveqtop{
  padding-top:1rem;
}

.max-height-important {
  height: var(--max-height) !important;
  overflow-y:hidden;
}

.max-height-important-cards {
  max-height: 57vh !important;
  overflow-y:auto !important;
}

/* Episode spine button improvements */
.episode-spine-button {
  writing-mode: vertical-rl;
  transform: rotate(180deg);
  min-height: 60px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px !important;
  border: 2px solid black;
  transition: background-color 0.2s ease;
  font-weight: bold;
  font-family: monospace;
  font-size: 0.75rem;
}

.episode-spine-container {
  width: 30px;
  flex-shrink: 0;
}

/* Compact workshop view */
.workshop-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
  padding: 0.5rem;
}

.workshop-card {
  aspect-ratio: 4/3;
  height: auto;
}

.workshop-section {
  margin-bottom: 0.5rem;
}

.prompts-layout {
  display: flex;
  height: calc(100vh - 4rem);
  overflow: hidden;
}

.prompts-layout main {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.prompts-layout aside {
  width: 400px;
  border-left: 4px solid #000;
  background-color: white;
  display: flex;
  flex-direction: column;
}

/* Chat window positioning styles */
.chat-window-left {
  left: 0;
  border-right: 4px solid black !important;
}

.chat-window-right {
  right: 0;
  border-left: 4px solid black !important;
}

.chat-window-full {
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  width: 100% !important;
  height: 100% !important;
  z-index: 100;
}

/* Chat window transition */
.chat-window-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Resizable chat window */
.chat-resizable {
  min-width: 320px;
  min-height: 400px;
  max-width: 80vw;
  max-height: 80vh;
}

/* Chat resize handles */
.chat-resize-handle {
  position: absolute;
  background-color: transparent;
  transition: background-color 0.2s;
}

.chat-resize-handle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.chat-resize-handle-n {
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  cursor: n-resize;
}

.chat-resize-handle-e {
  top: 0;
  right: 0;
  bottom: 0;
  width: 6px;
  cursor: e-resize;
}

.chat-resize-handle-s {
  bottom: 0;
  left: 0;
  right: 0;
  height: 6px;
  cursor: s-resize;
}

.chat-resize-handle-w {
  top: 0;
  left: 0;
  bottom: 0;
  width: 6px;
  cursor: w-resize;
}

.chat-resize-handle-ne {
  top: 0;
  right: 0;
  width: 10px;
  height: 10px;
  cursor: ne-resize;
}

.chat-resize-handle-se {
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  cursor: se-resize;
}

.chat-resize-handle-sw {
  bottom: 0;
  left: 0;
  width: 10px;
  height: 10px;
  cursor: sw-resize;
}

.chat-resize-handle-nw {
  top: 0;
  left: 0;
  width: 10px;
  height: 10px;
  cursor: nw-resize;
}

/* Chat snap guides */
.chat-snap-guide {
  position: fixed;
  background-color: rgba(59, 130, 246, 0.5);
  z-index: 9999;
  pointer-events: none;
}

.chat-snap-guide-x {
  width: 2px;
  height: 100vh;
}

.chat-snap-guide-y {
  height: 2px;
  width: 100vw;
}

/* Chat widget overlay */
.chat-widget-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.05);
  z-index: 40;
  pointer-events: none;
}

/* Chat position buttons */
.chat-position-buttons {
  position: absolute;
  top: -24px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 2px;
  background-color: white;
  border: 2px solid black;
  border-bottom: none;
  border-radius: 4px 4px 0 0;
  padding: 2px 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.chat-window:hover .chat-position-buttons {
  opacity: 1;
}

/* Screenplay preview styles */
.screenplay-preview {
  max-width: 40em;
  margin: 0 auto;
  font-family: 'Courier Prime', monospace;
}

.screenplay-preview div {
  margin-bottom: 0.5em;
}

/* Screenplay formatting */
.screenplay-preview .scene-heading {
  font-weight: bold;
  text-transform: uppercase;
}

.screenplay-preview .character {
  margin-top: 1em;
  margin-bottom: 0;
  text-transform: uppercase;
}

.screenplay-preview .dialogue {
  margin-left: 2em;
  margin-right: 2em;
}

.screenplay-preview .parenthetical {
  margin-left: 1.5em;
  margin-right: 1.5em;
  font-style: italic;
}

.screenplay-preview .transition {
  text-align: right;
  text-transform: uppercase;
}

/* Dictation indicator */
.dictation-indicator {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  background-color: rgba(239, 68, 68, 0.1);
  color: rgb(239, 68, 68);
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.dictation-indicator-dot {
  height: 0.5rem;
  width: 0.5rem;
  background-color: rgb(239, 68, 68);
  border-radius: 50%;
}

/* Color legend minimized state */
.color-legend-minimized {
  height: 40px !important;
  overflow: hidden;
}

/* Responsive grid for WorkshopView */
.workshop-grid-responsive {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
}
@media (min-width: 768px) {
  .workshop-grid-responsive {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 1024px) {
  .workshop-grid-responsive {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Prevent horizontal scroll on WorkshopView grid */
.workshop-grid-responsive, .workshop-grid-responsive > * {
  min-width: 0;
  max-width: 100%;
  box-sizing: border-box;
}

.quadrant-expanded {
  flex-grow: 100 !important;
  z-index: 50;
  position: relative;
  background: white;
  min-width: 0;
  width: 97vw !important;
  max-width: 97vw !important;
  margin-left: 0.5vw;
  margin-right: 0.5vw;
  box-shadow: 0 0 0 4px #000;
  transition: flex-grow 0.3s, box-shadow 0.3s, opacity 0.3s, width 0.3s, margin 0.3s;
}

.quadrant-collapsed {
  flex-grow: 1 !important;
  min-width: 16px;
  max-width: 24px;
  opacity: 0.7;
  pointer-events: none;
  filter: blur(0.5px);
  transition: flex-grow 0.3s, opacity 0.3s, filter 0.3s, max-width 0.3s;
  border-right: 4px solid #000;
  border-left: 4px solid #000;
}

.quadrant-expanded .overflow-y-auto {
  overflow-y: auto !important;
}

/* Write module adjustments for better visibility */
/* Move the SceneNotes component up in the write module */
.WriteLayout .SceneContent .flex-1.flex > div:last-child {
  margin-top: -20px;
}

/* Adjust the episodes view in the write module */
.WriteLayout .ArrowLeft + span,
button:has(.ArrowLeft) {
  position: relative;
  top: -20px;
}

/* Ensure comments box is fully visible */
.SceneNotes .flex-1.flex.flex-col.p-4 {
  max-height: 92vh;
  overflow-y: auto;
}

/* Adjust comment box textarea to fit in viewport */
.SceneNotes textarea {
  height: 60px;
}

/* Fit to screen adjustments for Write module */
@media (max-height: 900px) {
  /* Further adjustments for smaller screens */
  .WriteLayout .SceneContent .flex-1.flex > div:last-child {
    margin-top: -40px;
  }

  .SceneNotes .notes-list {
    max-height: 85vh;
  }
}

/* Episode Spine */
.sticky.top-0.right-0.z-20.h-full.flex.flex-col {
  height: calc(100vh - 4rem) !important;
  display: flex;
  flex-direction: column;
}

.episode-spine-button {
  flex: 1 1 0 !important;
  min-height: 80px !important;
}

/* Ensure episode spine is visible in all views */
.EpisodesView .sticky.top-0.right-0.z-20.h-full.flex.flex-col {
  position: sticky !important;
  height: calc(100vh - 4rem) !important;
  min-height: calc(100vh - 4rem) !important;
  display: flex !important;
  flex-direction: column !important;
  z-index: 20 !important;
}

/* Ensure enhanced template properly shows episode spine */
.min-h-screen.bg-gray-100.flex.flex-col .EpisodesView .sticky.top-0.right-0.z-20.h-full.flex.flex-col {
  position: fixed !important;
  right: 0 !important;
  top: 4rem !important;
}

/* UI Visibility Fixes - Added to ensure all UI elements remain visible */

/* Tag visibility */
.px-3.py-1.bg-gray-100.border-2.border-black.font-mono.text-sm.flex.items-center.gap-1 {
  display: flex !important;
  z-index: 30 !important;
  position: relative !important;
}

/* Toolbar & button visibility */
.sticky.top-0.z-10 {
  z-index: 35 !important;
  position: relative !important;
}

/* Dictation button */
button[title="Start dictation"],
button[title="Stop dictation"] {
  z-index: 40 !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Scene planning section */
.border-r-4.border-black.flex.flex-col {
  overflow: visible !important;
  z-index: 25 !important;
}

/* Fix for containers that might hide content */
.flex-1.flex {
  overflow: visible !important;
}

/* Prevent preview mode from hiding UI elements */
.flex-col.w-full > div:nth-child(1) {
  z-index: 20 !important;
}

/* Ensure editor content doesn't overlap UI */
textarea,
.codeMirror,
[contenteditable="true"] {
  z-index: 15 !important;
}

@media (max-width: 900px) {
  .scale-container,
  .scene-grid,
  .episode-columns-container {
    width: 100vw !important;
    max-width: 100vw !important;
    min-width: 0 !important;
  }
}

html, body {
  width: 100% !important;
  max-width: 100% !important;
  overflow-x: hidden !important;
}

/* Fix navigation bar max-width */
nav.fixed.top-0.left-0.right-0.z-50,
nav[role="navigation"] {
  max-width: 100% !important;
}