import React, { useEffect } from 'react';
import { Plus, X } from 'lucide-react';
import { Button } from '../../../ui/button';
import { cn } from '../../../../lib/utils';
import type { SceneBeat } from '../../../../types/write';

interface SceneBeatsProps {
  beats: SceneBeat[];
  onUpdate: (beats: SceneBeat[]) => void;
}

export const DEFAULT_BEATS: SceneBeat[] = [
  { id: '1', title: 'SETUP', notes: '', color: 'bg-blue-500' },
  { id: '2', title: 'CONFLICT', notes: '', color: 'bg-red-500' },
  { id: '3', title: 'ESCALATION', notes: '', color: 'bg-yellow-500' },
  { id: '4', title: 'CLIMAX', notes: '', color: 'bg-purple-500' },
  { id: '5', title: 'RESOLUTION', notes: '', color: 'bg-green-500' }
];

export const SceneBeats: React.FC<SceneBeatsProps> = ({
  beats,
  onUpdate
}) => {
  // Ensure default beats are initialized if none are provided
  useEffect(() => {
    if (!beats || beats.length === 0) {
      onUpdate(DEFAULT_BEATS);
    }
  }, [beats, onUpdate]);

  // Use a local copy of beats, falling back to DEFAULT_BEATS if empty
  const currentBeats = beats?.length > 0 ? beats : DEFAULT_BEATS;

  const handleBeatUpdate = (id: string, notes: string) => {
    onUpdate(
      currentBeats.map(beat =>
        beat.id === id ? { ...beat, notes } : beat
      )
    );
  };

  const addBeat = () => {
    const title = prompt('Enter beat title:');
    if (title) {
      onUpdate([
        ...currentBeats,
        {
          id: Date.now().toString(),
          title,
          notes: '',
          color: 'bg-gray-500'
        }
      ]);
    }
  };

  const removeBeat = (id: string) => {
    // Prevent removing all beats - keep at least one
    if (currentBeats.length <= 1) {
      return;
    }
    onUpdate(currentBeats.filter(beat => beat.id !== id));
  };

  return (
    <div className="flex-1 flex flex-col">
      {/* Header */}
      <div className="p-2 bg-gray-50 border-b-2 border-black flex justify-between items-center">
        <span className="font-mono text-sm font-bold">Scene Beats</span>
        <Button
          variant="ghost"
          size="sm"
          onClick={addBeat}
          className="h-8 w-8 p-1 rounded-full"
          title="Add beat"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>
      
      {/* Content */}
      <div className="p-4 flex-1 overflow-y-auto">
        {/* Beat cards */}
        <div className="flex w-full mb-4 gap-2">
          {currentBeats.map(beat => (
            <div key={beat.id} className="flex-1 min-w-0">
              <div className="flex items-center gap-1 mb-2">
                <div className={cn(
                  beat.color,
                  'w-full py-1 px-2 border-2 border-black font-mono text-xs font-bold text-center text-white truncate'
                )}>
                  {beat.title}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeBeat(beat.id)}
                  className="h-5 w-5 p-0"
                  disabled={currentBeats.length <= 1}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
              <textarea
                className="w-full p-1 border-2 border-black font-mono text-xs resize-none h-16"
                value={beat.notes || ''}
                onChange={(e) => handleBeatUpdate(beat.id, e.target.value)}
                placeholder="Beat notes..."
              />
            </div>
          ))}
        </div>

        {/* Beat grid for detailed notes */}
        <div className="grid grid-cols-1 gap-4">
          {currentBeats.map(beat => (
            <div key={`detail-${beat.id}`} className="border-2 border-t-4 border-black p-3" style={{ borderTopColor: getBorderColor(beat.color) }}>
              <div className="font-mono text-xs font-bold mb-2">{beat.title} NOTES</div>
              <textarea
                className="w-full p-2 border-2 border-black font-mono text-xs resize-none min-h-[60px]"
                value={beat.notes || ''}
                onChange={(e) => handleBeatUpdate(beat.id, e.target.value)}
                placeholder={`Add your ${beat.title.toLowerCase()} notes here...`}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Helper function to get CSS border color from the beat color class
function getBorderColor(colorClass: string): string {
  switch(colorClass) {
    case 'bg-blue-500': return '#3b82f6';
    case 'bg-red-500': return '#ef4444';
    case 'bg-yellow-500': return '#eab308';
    case 'bg-purple-500': return '#a855f7';
    case 'bg-green-500': return '#22c55e';
    case 'bg-gray-500': return '#6b7280';
    default: return '#000000';
  }
}