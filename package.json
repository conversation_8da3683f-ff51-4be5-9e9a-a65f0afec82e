{"name": "story-unstuck", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 5174", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-slot": "^1.0.2", "@supabase/supabase-js": "^2.39.3", "clsx": "^2.1.0", "immer": "^10.1.1", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.2.1", "zustand": "^4.5.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.19"}}