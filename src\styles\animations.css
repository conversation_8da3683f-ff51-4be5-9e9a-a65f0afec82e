/* Drag and drop animations */
@keyframes pulse-border {
  0% {
    border-color: rgba(0, 0, 0, 0.2);
  }
  50% {
    border-color: rgba(0, 0, 0, 0.5);
  }
  100% {
    border-color: rgba(0, 0, 0, 0.2);
  }
}

.drag-over {
  animation: pulse-border 1s infinite;
}

/* Scene card animations */
.scene-card-enter {
  opacity: 0;
  transform: translateY(10px);
}

.scene-card-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.scene-card-exit {
  opacity: 1;
}

.scene-card-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 300ms, transform 300ms;
}

/* Reordering animation */
.scene-reorder {
  transition: transform 0.2s ease-in-out;
}