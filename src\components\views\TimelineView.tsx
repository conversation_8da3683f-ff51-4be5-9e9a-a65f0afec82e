import React, { useEffect } from 'react';
import { Plus, ChevronDown, ChevronRight } from 'lucide-react';
import { Button } from '../ui/button';
import { TimelineGrid } from '../timeline/TimelineGrid';
import { ColorLegend } from '../timeline/ColorLegend';
import { ColorLegendToggle } from '../ui/ColorLegendToggle';
import { useEpisodeTrackStore } from '../../store/episodeTrackStore';

export const TimelineView: React.FC = () => {
  const { 
    addStoryline, 
    episodeTrack,
    toggleEpisodeTrackExpanded,
    setColorLegendVisible
  } = useEpisodeTrackStore();

  // Ensure color legend is visible when the timeline view is loaded
  useEffect(() => {
    setColorLegendVisible(true);
    return () => {
      // Keep the color legend visible when navigating away
    };
  }, [setColorLegendVisible]);

  return (
    <div className="h-full flex flex-col">
      {/* Episode Track Header - Similar to TimelineSection */}
      <div className="sticky top-0 z-10 bg-white flex items-center justify-between px-4 py-2 border-b-4 border-black">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleEpisodeTrackExpanded}
            className="hover:bg-gray-100"
          >
            {episodeTrack.isExpanded ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
          <h2 className="font-bold">Episode Track</h2>
        </div>

        <Button 
          variant="ghost" 
          onClick={() => addStoryline('New Storyline')}
          className="hover:bg-gray-100 border-2 border-black"
        >
          <Plus className="h-4 w-4 mr-1" />
          Add Storyline
        </Button>
      </div>

      {/* Timeline Grid - Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="min-w-max">
          <TimelineGrid />
        </div>
      </div>

      {/* Color Legend is added by the template components */}
    </div>
  );
};