import React from 'react';
import { MainNavigation } from '../layout/MainNavigation';
import { WorkshopView } from '../workshop/WorkshopView';
import { EpisodesView } from '../episodes/EpisodesView';
import { WriteView } from '../views/WriteView';
import { PromptsView } from '../views/PromptsView';
import { TaskView } from '../views/TaskView';
import { TestView } from '../views/TestView';
import { TimelineView } from '../views/TimelineView';
import { ColorLegend } from '../timeline/ColorLegend';
import { ColorLegendToggle } from '../ui/ColorLegendToggle';
import type { ViewType } from '../../constants/navigation';
import { EpisodeSpineProvider } from '../../contexts/EpisodeSpineContext';

interface ClassicTemplateProps {
  activeView: ViewType;
  onViewChange: (view: ViewType) => void;
  onHomeClick: () => void;
  onTemplateChange: () => void;
  template: 'classic' | 'enhanced';
}

export const ClassicTemplate: React.FC<ClassicTemplateProps> = ({
  activeView,
  onViewChange,
  onHomeClick,
  onTemplateChange,
  template
}) => {
  const renderView = () => {
    switch (activeView) {
      case 'workshop':
        return <WorkshopView />;
      case 'episodes':
        return <EpisodesView />;
      case 'timeline':
        return <TimelineView />;
      case 'write':
        return <WriteView />;
      case 'prompts':
        return <PromptsView />;
      case 'tasks':
        return <TaskView />;
      case 'test':
        return <TestView />;
      default:
        return <WorkshopView />;
    }
  };

  return (
    <EpisodeSpineProvider>
      {/* Navigation bar outside the scale container to ensure it's always visible */}
      <MainNavigation
        activeView={activeView}
        onViewChange={onViewChange}
        onHomeClick={onHomeClick}
        onTemplateChange={onTemplateChange}
        template={template}
      />

      {/* Content inside scale container for proper zooming */}
      <div className="scale-container">
        <div className="min-h-screen bg-gray-100 w-full">
          <main className="adjustpadding w-full">
            {renderView()}
          </main>
          {/* <ChatWindowContainer /> */}
          {activeView === 'timeline' && <>
            <ColorLegend />
            <ColorLegendToggle collapsed />
          </>}
        </div>
      </div>
    </EpisodeSpineProvider>
  );
};