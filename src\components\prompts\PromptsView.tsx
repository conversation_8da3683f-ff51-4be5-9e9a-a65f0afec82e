import React, { useState, useEffect } from 'react';
import { BookOpen, Plus } from 'lucide-react';
import { Button } from '../ui/button';
import { StoryOverview } from './overview/StoryOverview';
import { PromptSection } from './PromptSection';
import { usePromptsState } from '../../hooks/prompts/usePromptsState';
import { useChat } from '../../contexts/ChatContext';
import { ACT_COLORS } from '../../lib/theme';

// Define a type for new prompts from chat
interface NewPromptFromChat {
  title: string;
  details: string;
  groupId: string;
}

export const PromptsView: React.FC = () => {
  const { 
    promptGroups, 
    addPromptGroup, 
    updatePromptGroup, 
    deletePromptGroup,
    addPrompt,
    updatePrompt,
    addFile,
    deleteFile
  } = usePromptsState();
  
  const { addMessage } = useChat();
  const [activeView, setActiveView] = useState<'prompts' | 'overview'>('overview');
  const [newPromptFromChat, setNewPromptFromChat] = useState<NewPromptFromChat | null>(null);

  // Listen for custom events for prompt creation from chat
  useEffect(() => {
    const handleCreatePromptEvent = (event: CustomEvent<NewPromptFromChat>) => {
      setNewPromptFromChat(event.detail);
    };
    
    window.addEventListener('create-prompt-from-chat' as any, handleCreatePromptEvent as any);
    
    return () => {
      window.removeEventListener('create-prompt-from-chat' as any, handleCreatePromptEvent as any);
    };
  }, []);

  const handleSendToChat = (content: string) => {
    addMessage(content, 'user');
  };

  // Get next color from ACT_COLORS when adding a new group
  const getNextColor = () => {
    const colors = Object.values(ACT_COLORS);
    return colors[promptGroups.length % colors.length];
  };
  
  // Handle creating a prompt from chat content
  const createPromptFromChat = (title: string, details: string) => {
    if (promptGroups.length === 0) {
      // Create a default group if none exists
      const groupId = addPromptGroup({
        title: 'Chat Prompts',
        color: getNextColor()
      });
      
      addPrompt(groupId, {
        title,
        details,
        action: '',
        tags: ['chat']
      });
      
      return;
    }
    
    // Use the first group if available
    addPrompt(promptGroups[0].id, {
      title,
      details,
      action: '',
      tags: ['chat']
    });
  };
  
  // If there's a new prompt from chat, create it
  useEffect(() => {
    if (newPromptFromChat) {
      createPromptFromChat(newPromptFromChat.title, newPromptFromChat.details);
      setNewPromptFromChat(null);
      
      // Switch to prompts view to show the new prompt
      setActiveView('prompts');
    }
  }, [newPromptFromChat]);

  return (
    <div className="h-screen flex">
      <div className="flex-1 p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <BookOpen className="w-6 h-6" />
            <h2 className="text-xl font-bold">Story Development</h2>
          </div>
          <div className="flex gap-2">
            <Button
              variant={activeView === 'overview' ? 'default' : 'ghost'}
              onClick={() => setActiveView('overview')}
            >
              Overview
            </Button>
            <Button
              variant={activeView === 'prompts' ? 'default' : 'ghost'}
              onClick={() => setActiveView('prompts')}
            >
              Prompts
            </Button>
          </div>
        </div>

        {activeView === 'overview' ? (
          <StoryOverview />
        ) : (
          <div>
            <div className="flex items-center gap-2 mb-4">
              <Button onClick={() => {
                addPromptGroup({
                  title: 'New Prompt Group',
                  color: getNextColor()
                });
              }}>
                <Plus className="h-4 w-4 mr-2" />
                Add Prompt Group
              </Button>
            </div>

            <div className="space-y-4">
              {promptGroups.map(group => (
                <PromptSection
                  key={group.id}
                  id={group.id}
                  title={group.title}
                  color={group.color}
                  prompts={group.prompts}
                  onAddPrompt={addPrompt}
                  onUpdatePrompt={(promptId, updates) => 
                    updatePrompt(group.id, promptId, updates)
                  }
                  onDeletePrompt={(promptId) =>
                    updatePromptGroup(group.id, { 
                      prompts: group.prompts.filter(p => p.id !== promptId) 
                    })
                  }
                  onUpdateSection={(id, updates) => 
                    updatePromptGroup(id, updates)
                  }
                  onDelete={() => deletePromptGroup(group.id)}
                  onAddFile={(promptId, file) => addFile(group.id, promptId, file)}
                  onDeleteFile={(promptId, fileId) => deleteFile(group.id, promptId, fileId)}
                  onSendToChat={handleSendToChat}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};