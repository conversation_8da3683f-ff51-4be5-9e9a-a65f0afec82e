# Local Development Guide - Story Unstuck

## Overview

This guide covers the local-only implementation of Story Unstuck, including:
- Story Overview component
- Custom Prompts system
- Local data storage
- Component architecture
- Development workflow

## 1. Project Structure

```
src/
├── components/
│   ├── prompts/
│   │   ├── overview/          # Story overview components
│   │   ├── preview/          # Prompt preview components
│   │   ├── PromptCard.tsx    # Individual prompt display
│   │   └── PromptSection.tsx # Prompt group container
│   └── chat/
│       ├── ChatWindow.tsx    # Main chat interface
│       ├── ModelSelector.tsx # AI model selection
│       └── PersonaSelector.tsx # Chat persona selection
├── hooks/
│   ├── prompts/
│   │   └── usePromptsState.ts # Prompt state management
│   └── chat/
│       └── useChatState.ts    # Chat state management
└── types/
    ├── prompts.ts            # Prompt-related types
    └── chat.ts               # Chat-related types
```

## 2. GitHub Repository Setup

1. Create a new repository:
```bash
git init
git add .
git commit -m "Initial commit"
git branch -M main
git remote add origin <repository-url>
git push -u origin main
```

2. Configure branch protection:
   - Go to Settings > Branches
   - Add rule for `main` branch
   - Require pull request reviews
   - Require status checks to pass

3. Create development workflow:
```yaml
# .github/workflows/development.yml
name: Development

on:
  push:
    branches: [ develop ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build
      - run: npm test
```

## 3. Local Data Storage

### Storage Structure

All data is stored in browser localStorage with the following structure:

```typescript
// Local storage keys and data structures

// Key: 'story_unstuck_prompt_categories'
interface PromptCategory {
  id: string;
  title: string;
  description?: string;
  color: string;
  position: number;
  created_at: string;
  updated_at: string;
}

// Key: 'story_unstuck_prompts'
interface Prompt {
  id: string;
  category_id: string;
  title: string;
  details: string;
  action_steps: string[];
  files: string[];
  tags: string[];
  created_at: string;
  updated_at: string;
}

// Key: 'story_unstuck_user'
interface User {
  id: string;
  email: string;
  displayName?: string;
  created_at: string;
  last_login: string;
}
```

### Local Storage API

```typescript
// src/lib/localStorage.ts
import { logger } from '../utils/logger';

// Storage keys
const CATEGORIES_KEY = 'story_unstuck_prompt_categories';
const PROMPTS_KEY = 'story_unstuck_prompts';

// Helper functions
const getStoredData = <T>(key: string): T[] => {
  try {
    const data = localStorage.getItem(key);
    return data ? JSON.parse(data) : [];
  } catch (error) {
    logger.error(`Failed to parse ${key} from localStorage:`, error);
    return [];
  }
};

const setStoredData = <T>(key: string, data: T[]): void => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    logger.error(`Failed to save ${key} to localStorage:`, error);
  }
};

// Prompt-related operations
export const promptsApi = {
  async getCategories(): Promise<PromptCategory[]> {
    return getStoredData<PromptCategory>(CATEGORIES_KEY);
  },

  async getPrompts(categoryId: string): Promise<Prompt[]> {
    const prompts = getStoredData<Prompt>(PROMPTS_KEY);
    return prompts.filter(prompt => prompt.category_id === categoryId);
  },

  async createPrompt(prompt: Omit<Prompt, 'id' | 'created_at' | 'updated_at'>): Promise<Prompt> {
    const newPrompt: Prompt = {
      ...prompt,
      id: `prompt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const prompts = getStoredData<Prompt>(PROMPTS_KEY);
    prompts.push(newPrompt);
    setStoredData(PROMPTS_KEY, prompts);

    return newPrompt;
  }
};
```

## 4. Local Development Features

### Mock Chat Integration (Optional)

For local development, you can implement a mock chat system:

```typescript
// src/lib/mockChat.ts
import { logger } from '../utils/logger';

// Mock responses for local development
const mockResponses = [
  "That's an interesting story idea! Consider developing the character's motivation further.",
  "This plot point could benefit from more conflict. What obstacles might your character face?",
  "Great character development! How does this relate to your overall story theme?",
  "Consider the pacing here. Does this scene move the story forward effectively?"
];

export const chatApi = {
  async sendMessage(content: string, model: string): Promise<{ content: string; role: 'assistant' }> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    // Return a random mock response
    const response = mockResponses[Math.floor(Math.random() * mockResponses.length)];

    logger.debug('Mock chat response generated:', { content, model, response });

    return {
      content: response,
      role: 'assistant'
    };
  }
};
```

### Chat State Management

```typescript
// src/hooks/chat/useChatState.ts
import { create } from 'zustand';

interface ChatState {
  messages: Message[];
  model: string;
  persona: string;
  addMessage: (content: string, role: 'user' | 'assistant') => void;
  setModel: (model: string) => void;
  setPersona: (persona: string) => void;
}

export const useChatStore = create<ChatState>((set) => ({
  messages: [],
  model: 'gpt-4',
  persona: 'writer',
  addMessage: (content, role) =>
    set((state) => ({
      messages: [...state.messages, { id: Date.now(), content, role }]
    })),
  setModel: (model) => set({ model }),
  setPersona: (persona) => set({ persona })
}));
```

## 5. Local Deployment

### Building for Production

1. Build the application:
```bash
npm run build
```

2. Preview the build locally:
```bash
npm run preview
```

3. Serve static files (optional):
```bash
# Using any static file server
npx serve dist
# or
python -m http.server 3000 --directory dist
```

### Environment Configuration

Create environment files for different modes:

```bash
# .env (default)
VITE_APP_NAME=Story Unstuck
VITE_DEV_MODE=true

# .env.production
VITE_APP_NAME=Story Unstuck
VITE_DEV_MODE=false
```

## 6. Development Workflow

1. Local Development:
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Run tests
npm test
```

2. Making Changes:
   - Create feature branch
   - Make changes
   - Run tests
   - Create pull request
   - Deploy to staging
   - Merge to main

3. Data Management:
```bash
# Clear all local data (for testing)
# Open browser console and run:
localStorage.clear()

# Export data (implement in app)
# Download localStorage data as JSON

# Import data (implement in app)
# Upload and restore JSON data
```

## 7. Local Storage API

### Prompts API Interface

```typescript
interface LocalPromptsApi {
  // Categories
  getCategories(): Promise<PromptCategory[]>;
  createCategory(category: Omit<PromptCategory, 'id' | 'created_at' | 'updated_at'>): Promise<PromptCategory>;
  updateCategory(id: string, updates: Partial<PromptCategory>): Promise<PromptCategory>;
  deleteCategory(id: string): Promise<void>;

  // Prompts
  getPrompts(categoryId: string): Promise<Prompt[]>;
  createPrompt(prompt: Omit<Prompt, 'id' | 'created_at' | 'updated_at'>): Promise<Prompt>;
  updatePrompt(id: string, updates: Partial<Prompt>): Promise<Prompt>;
  deletePrompt(id: string): Promise<void>;
}
```

### Data Export/Import API

```typescript
interface DataManagementApi {
  exportData(): {
    categories: PromptCategory[];
    prompts: Prompt[];
    user: User;
    exportDate: string;
  };

  importData(data: {
    categories?: PromptCategory[];
    prompts?: Prompt[];
    user?: User;
  }): Promise<void>;

  clearAllData(): Promise<void>;
}
```

## 8. Testing Strategy

1. Unit Tests:
```typescript
// src/__tests__/prompts/PromptCard.test.tsx
import { render, fireEvent } from '@testing-library/react';
import { PromptCard } from '../../components/prompts/PromptCard';

describe('PromptCard', () => {
  it('renders prompt content', () => {
    const prompt = {
      id: '1',
      title: 'Test Prompt',
      content: 'Test content'
    };
    const { getByText } = render(<PromptCard prompt={prompt} />);
    expect(getByText('Test Prompt')).toBeInTheDocument();
  });
});
```

2. Integration Tests:
```typescript
// src/__tests__/integration/prompts.test.ts
import { promptsApi } from '../../lib/localStorage';

describe('Local Prompts API', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  it('creates and retrieves prompts', async () => {
    const category = await promptsApi.createCategory({
      title: 'Test Category',
      color: '#000000',
      position: 0
    });

    const prompt = await promptsApi.createPrompt({
      category_id: category.id,
      title: 'Test Prompt',
      details: 'Test details',
      action_steps: [],
      files: [],
      tags: []
    });

    const prompts = await promptsApi.getPrompts(category.id);
    expect(prompts).toContainEqual(prompt);
  });
});
```

## 9. Error Handling

```typescript
// src/utils/error.ts
export class ApiError extends Error {
  constructor(
    public status: number,
    public code: string,
    message: string
  ) {
    super(message);
  }
}

export function handleApiError(error: unknown) {
  if (error instanceof ApiError) {
    // Handle specific API errors
    switch (error.code) {
      case 'auth/unauthorized':
        // Handle auth errors
        break;
      case 'prompts/not-found':
        // Handle not found errors
        break;
      default:
        // Handle other API errors
    }
  } else {
    // Handle unexpected errors
    console.error('Unexpected error:', error);
  }
}
```

## 10. Performance Optimization

1. Code Splitting:
```typescript
// src/App.tsx
import { lazy, Suspense } from 'react';

const PromptsView = lazy(() => import('./components/views/PromptsView'));
const ChatWindow = lazy(() => import('./components/chat/ChatWindow'));

function App() {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <PromptsView />
      <ChatWindow />
    </Suspense>
  );
}
```

2. Caching:
```typescript
// src/hooks/prompts/usePromptsState.ts
const CACHE_KEY = 'prompts_cache';
const CACHE_TTL = 1000 * 60 * 5; // 5 minutes

function usePromptsCache() {
  const getCache = () => {
    const cached = localStorage.getItem(CACHE_KEY);
    if (cached) {
      const { data, timestamp } = JSON.parse(cached);
      if (Date.now() - timestamp < CACHE_TTL) {
        return data;
      }
    }
    return null;
  };

  const setCache = (data: any) => {
    localStorage.setItem(CACHE_KEY, JSON.stringify({
      data,
      timestamp: Date.now()
    }));
  };

  return { getCache, setCache };
}
```

## 11. Local Security Considerations

1. Data Privacy:
   - All data stays in user's browser
   - No external data transmission
   - User controls their own data

2. Input Validation:
   - Sanitize user input before storage
   - Validate data types and formats
   - Handle edge cases gracefully

3. Error Handling:
   - Graceful degradation when localStorage fails
   - Clear error messages for users
   - Fallback mechanisms

4. Browser Compatibility:
   - Check localStorage availability
   - Handle quota exceeded errors
   - Provide alternative storage methods if needed

## 12. Monitoring

1. Error Tracking:
```typescript
// src/utils/monitoring.ts
export function trackError(error: Error, context?: Record<string, any>) {
  // Send to error tracking service
  console.error('Error:', error, context);
}

export function trackMetric(name: string, value: number) {
  // Send to metrics service
  console.log('Metric:', name, value);
}
```

2. Performance Monitoring:
```typescript
// src/utils/performance.ts
export function measureTiming(name: string, fn: () => Promise<any>) {
  const start = performance.now();
  return fn().finally(() => {
    const duration = performance.now() - start;
    trackMetric(`${name}_duration`, duration);
  });
}
```

## Next Steps

1. Set up local development environment
2. Implement core components
3. Add data export/import features
4. Implement testing strategy
5. Optimize performance
6. Add accessibility features
7. Create user documentation
8. Package for distribution
### A. Memory Management

```typescript
class MemoryManager {
  private static readonly MAX_CACHE_SIZE = 100 * 1024 * 1024; // 100MB
  private static readonly CLEANUP_THRESHOLD = 0.9; // 90%

  private memoryUsage = new Map<string, number>();
  private totalUsage = 0;

  track(key: string, size: number) {
    this.memoryUsage.set(key, size);
    this.totalUsage += size;

    if (this.totalUsage > MemoryManager.MAX_CACHE_SIZE * MemoryManager.CLEANUP_THRESHOLD) {
      this.cleanup();
    }
  }

  private cleanup() {
    const entries = Array.from(this.memoryUsage.entries())
      .sort(([, a], [, b]) => b - a);

    let freedSpace = 0;
    const targetReduction = this.totalUsage - (MemoryManager.MAX_CACHE_SIZE * 0.7);

    for (const [key, size] of entries) {
      this.memoryUsage.delete(key);
      freedSpace += size;
      if (freedSpace >= targetReduction) break;
    }

    this.totalUsage -= freedSpace;
  }
}
```

### B. Worker Thread Pool

```typescript
import { Worker } from 'worker_threads';

class WorkerPool {
  private workers: Worker[] = [];
  private queue: Array<{
    task: any;
    resolve: (value: any) => void;
    reject: (error: any) => void;
  }> = [];
  private activeWorkers = 0;

  constructor(private size: number, private workerScript: string) {
    for (let i = 0; i < size; i++) {
      this.workers.push(new Worker(workerScript));
    }
  }

  async execute(task: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.queue.push({ task, resolve, reject });
      this.processQueue();
    });
  }

  private async processQueue() {
    if (this.queue.length === 0 || this.activeWorkers >= this.size) {
      return;
    }

    const worker = this.workers[this.activeWorkers++];
    const { task, resolve, reject } = this.queue.shift()!;

    try {
      const result = await worker.postMessage(task);
      resolve(result);
    } catch (error) {
      reject(error);
    } finally {
      this.activeWorkers--;
      this.processQueue();
    }
  }
}
```

### C. Adaptive Rate Limiting

```typescript
class AdaptiveRateLimiter {
  private windowStats = new Map<string, {
    count: number;
    errors: number;
    latency: number[];
  }>();

  private readonly MAX_ERROR_RATE = 0.05; // 5%
  private readonly TARGET_LATENCY = 500; // 500ms

  async shouldAllow(key: string): Promise<boolean> {
    const stats = this.windowStats.get(key) || {
      count: 0,
      errors: 0,
      latency: []
    };

    // Calculate error rate
    const errorRate = stats.count > 0 ? stats.errors / stats.count : 0;
    if (errorRate > this.MAX_ERROR_RATE) {
      return false;
    }

    // Check average latency
    const avgLatency = stats.latency.length > 0
      ? stats.latency.reduce((a, b) => a + b) / stats.latency.length
      : 0;

    if (avgLatency > this.TARGET_LATENCY) {
      return false;
    }

    return true;
  }

  recordMetrics(key: string, latency: number, error: boolean) {
    const stats = this.windowStats.get(key) || {
      count: 0,
      errors: 0,
      latency: []
    };

    stats.count++;
    if (error) stats.errors++;
    stats.latency.push(latency);

    // Keep only last 100 latency samples
    if (stats.latency.length > 100) {
      stats.latency.shift();
    }

    this.windowStats.set(key, stats);
  }
}
```

### D. Circuit Breaker

```typescript
enum CircuitState {
  CLOSED,
  OPEN,
  HALF_OPEN
}

class CircuitBreaker {
  private state = CircuitState.CLOSED;
  private failures = 0;
  private lastFailure: number | null = null;
  private readonly FAILURE_THRESHOLD = 5;
  private readonly RESET_TIMEOUT = 30000; // 30 seconds

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitState.OPEN) {
      if (this.shouldReset()) {
        this.state = CircuitState.HALF_OPEN;
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess() {
    this.failures = 0;
    this.state = CircuitState.CLOSED;
  }

  private onFailure() {
    this.failures++;
    this.lastFailure = Date.now();

    if (this.failures >= this.FAILURE_THRESHOLD) {
      this.state = CircuitState.OPEN;
    }
  }

  private shouldReset(): boolean {
    return this.lastFailure !== null &&
           Date.now() - this.lastFailure > this.RESET_TIMEOUT;
  }
}
```

### E. Metrics Collection

```typescript
class MetricsCollector {
  private metrics = new Map<string, {
    count: number;
    errors: number;
    latency: number[];
    lastUpdate: number;
  }>();

  record(metric: {
    name: string;
    value: number;
    error?: boolean;
    tags?: Record<string, string>;
  }) {
    const key = this.getMetricKey(metric.name, metric.tags);
    const stats = this.metrics.get(key) || {
      count: 0,
      errors: 0,
      latency: [],
      lastUpdate: Date.now()
    };

    stats.count++;
    if (metric.error) stats.errors++;
    stats.latency.push(metric.value);
    stats.lastUpdate = Date.now();

    this.metrics.set(key, stats);
  }

  getMetrics(): Record<string, {
    count: number;
    errorRate: number;
    avgLatency: number;
    p95Latency: number;
  }> {
    const result: Record<string, any> = {};

    for (const [key, stats] of this.metrics.entries()) {
      const sortedLatency = [...stats.latency].sort((a, b) => a - b);
      const p95Index = Math.floor(sortedLatency.length * 0.95);

      result[key] = {
        count: stats.count,
        errorRate: stats.errors / stats.count,
        avgLatency: stats.latency.reduce((a, b) => a + b) / stats.latency.length,
        p95Latency: sortedLatency[p95Index] || 0
      };
    }

    return result;
  }

  private getMetricKey(name: string, tags?: Record<string, string>): string {
    if (!tags) return name;
    const sortedTags = Object.entries(tags)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([k, v]) => `${k}=${v}`)
      .join(',');
    return `${name}{${sortedTags}}`;
  }
}
```

This completes the integration guide with advanced optimization techniques for production deployment. These components provide robust memory management, worker thread pooling, adaptive rate limiting, circuit breaking, and comprehensive metrics collection.

The system is now ready for high-scale production use with proper monitoring, error handling, and performance optimization in place.