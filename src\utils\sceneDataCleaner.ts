import { logger } from './logger';

/**
 * Utility for completely removing scene data from templates
 */

/**
 * Finds all scene-related keys in localStorage
 * @param projectId Optional project ID to limit the search
 * @returns Array of localStorage keys
 */
export function findAllSceneKeys(projectId?: string): string[] {
  const keys = [];
  
  // Create a specific project prefix pattern
  const projectPrefix = projectId ? `project_${projectId}_` : '';
  
  // If we have a project ID, only look for keys with that project ID
  if (projectId) {
    // Find all keys for this specific project
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(projectPrefix)) {
        keys.push(key);
      }
    }
    
    // Also look for legacy keys that might belong to this project
    // This is a heuristic - we look for write_scene_ keys that match our scene IDs
    const projectSceneKeys = keys.filter(k => k.includes('_scene_'));
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('write_scene_')) {
        // Extract the scene ID from the key
        const sceneId = key.replace('write_scene_', '');
        // Check if we have a project-scoped key with the same scene ID
        if (projectSceneKeys.some(k => k.endsWith(`_scene_${sceneId}`))) {
          keys.push(key);
        }
      }
    }
  } else {
    // If no project ID, get all scene-related keys
    const patterns = [
      'project_', 
      'write_scene_', 
      'scene_', 
      'episode_data'
    ];
  
    // Find all matching keys
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && patterns.some(pattern => key.includes(pattern))) {
        keys.push(key);
      }
    }
  }
  
  logger.debug(`Found ${keys.length} scene-related keys`, { 
    projectId, 
    sampleKeys: keys.slice(0, 5)
  });
  
  return keys;
}

/**
 * Completely removes all scene data from localStorage
 * @param projectId Optional project ID to limit the operation
 * @returns Number of items removed
 */
export function purgeAllSceneData(projectId?: string): number {
  const keys = findAllSceneKeys(projectId);
  logger.debug(`Purging ${keys.length} scene-related items`, { projectId });
  
  // Remove all found keys
  keys.forEach(key => {
    try {
      localStorage.removeItem(key);
      logger.debug(`Removed key: ${key}`);
    } catch (error) {
      logger.error(`Failed to remove key ${key}:`, error);
    }
  });
  
  logger.debug(`Purged ${keys.length} scene-related items from localStorage`);
  return keys.length;
}

/**
 * Removes scenes from episode data while preserving structure
 * @param projectId The project ID
 * @returns True if successful, false otherwise
 */
export function stripScenesFromEpisodeData(projectId: string): boolean {
  if (!projectId) {
    logger.warn('No project ID provided to stripScenesFromEpisodeData');
    return false;
  }
  
  try {
    // Get episode data
    const episodeDataKey = `project_${projectId}_episode_data`;
    const episodeDataStr = localStorage.getItem(episodeDataKey);
    logger.debug(`Looking for episode data at key: ${episodeDataKey}`);
    
    if (!episodeDataStr) {
      logger.warn(`No episode data found for project ${projectId}`);
      return false;
    }
    
    // Also find and remove all scene data for this project
    const sceneKeys = findAllSceneKeys(projectId).filter(key => 
      key.includes('_scene_') && !key.includes('episode_data')
    );
    
    logger.debug(`Found ${sceneKeys.length} scene keys to remove`);
    sceneKeys.forEach(key => {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        logger.error(`Failed to remove scene key ${key}:`, error);
      }
    });
    
    // Parse episode data
    const episodeData = JSON.parse(episodeDataStr);
    
    // Strip scenes from each act
    const strippedData = Object.entries(episodeData).reduce((acc, [episodeId, episodeInfo]: [string, any]) => {
      // Process each act in the episode
      const strippedActs = episodeInfo.acts.map((act: any) => ({
        ...act,
        scenes: [] // Replace scenes with empty array
      }));
      
      // Update the episode with stripped acts
      acc[episodeId] = {
        ...episodeInfo,
        acts: strippedActs
      };
      
      return acc;
    }, {} as Record<string, any>);
    
    // Save stripped data back to localStorage
    localStorage.setItem(episodeDataKey, JSON.stringify(strippedData));
    
    logger.debug(`Successfully stripped scenes from episode data for project ${projectId}`);
    return true;
  } catch (error) {
    logger.error('Failed to strip scenes from episode data:', error);
    return false;
  }
}

/**
 * Completely resets a project's template to blank state
 * @param projectId The project ID
 * @returns True if successful, false otherwise
 */
export function resetProjectToBlankState(projectId: string): boolean {
  if (!projectId) {
    logger.warn('No project ID provided to resetProjectToBlankState');
    return false;
  }
  
  try {
    // First, purge all scene data
    const purgedCount = purgeAllSceneData(projectId);
    logger.debug(`Purged ${purgedCount} items for project ${projectId}`);
    
    // Enable blank templates mode
    localStorage.setItem('use_blank_templates', 'true');
    
    // Also enable empty templates mode
    localStorage.setItem('use_empty_templates', 'true');
    
    logger.debug(`Reset project ${projectId} to blank state`);
    return true;
  } catch (error) {
    logger.error('Failed to reset project to blank state:', error);
    return false;
  }
}

/**
 * Checks if a project has any scene data
 * @param projectId The project ID
 * @returns True if scene data exists, false otherwise
 */
export function hasSceneData(projectId: string): boolean {
  if (!projectId) return false;
  
  // Look for scene keys
  const sceneKeys = findAllSceneKeys(projectId);
  
  // Check if any scene keys exist
  return sceneKeys.length > 0;
}

/**
 * Gets statistics about scene data
 * @param projectId Optional project ID to limit the analysis
 * @returns Statistics about scene data
 */
export function getSceneDataStats(projectId?: string): {
  totalKeys: number;
  totalSize: number;
  episodeDataSize: number;
  sceneDataSize: number;
} {
  const keys = findAllSceneKeys(projectId);
  let totalSize = 0;
  let episodeDataSize = 0;
  let sceneDataSize = 0;
  
  keys.forEach(key => {
    try {
      const value = localStorage.getItem(key);
      if (value) {
        const size = value.length;
        totalSize += size;
        
        if (key.includes('episode_data')) {
          episodeDataSize += size;
        } else if (key.includes('scene_')) {
          sceneDataSize += size;
        }
      }
    } catch (error) {
      logger.error(`Failed to analyze key ${key}:`, error);
    }
  });
  
  return {
    totalKeys: keys.length,
    totalSize,
    episodeDataSize,
    sceneDataSize
  };
}