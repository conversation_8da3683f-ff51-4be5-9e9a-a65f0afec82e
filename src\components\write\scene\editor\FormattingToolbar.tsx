import React, { useState } from 'react';
import { Wand2 } from 'lucide-react';
import { Button } from '../../../ui/button';
import { cn } from '../../../../lib/utils';
import { formatFountainText, analyzeFountainText, validateFountainScenes } from '../../../../utils/fountainUtils';

interface FormattingToolbarProps {
  content: string;
  onFormat: (formattedContent: string) => void;
  onInsertTemplate: (template: string) => void;
}

export const FormattingToolbar: React.FC<FormattingToolbarProps> = ({
  content,
  onFormat,
  onInsertTemplate
}) => {
  const [isFormatting, setIsFormatting] = useState(false);
  
  // Format the content
  const handleFormat = () => {
    setIsFormatting(true);
    
    // Check if the content has valid scene headings
    if (!validateFountainScenes(content)) {
      // Add a scene heading if none exists
      const formattedContent = `INT. SCENE - DAY\n\n${content}`;
      onFormat(formatFountainText(formattedContent));
    } else {
      // Format existing content
      onFormat(formatFountainText(content));
    }
    
    const formattedContent = formatFountainText(content);
    onFormat(formattedContent);
    setTimeout(() => setIsFormatting(false), 1000);
  };
  
  return (
    <div className="p-2 bg-gray-50 border-b-2 border-black">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleFormat}
            className={cn(
              "h-8 px-2 text-xs",
              isFormatting && "bg-purple-100 text-purple-600"
            )}
            title="Format as Fountain"
            disabled={isFormatting}
          >
            <Wand2 className="h-3 w-3 mr-1" />
            Format
          </Button>
        </div>
      </div>
    </div>
  );
};