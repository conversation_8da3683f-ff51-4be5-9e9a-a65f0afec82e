import React from 'react';
import { FileDown } from 'lucide-react';
import { Button } from '../../../ui/button';
import { scenesToFountain } from '../../../../utils/fountainParser';
import type { Scene } from '../../../../types/write';

interface ExportButtonProps {
  scene: Scene;
  filename?: string;
}

export const ExportButton: React.FC<ExportButtonProps> = ({
  scene,
  filename = 'scene'
}) => {
  const handleExport = () => {
    // Convert scene to Fountain format
    const fountainText = scenesToFountain([scene]);
    
    // Create a blob and download link
    const blob = new Blob([fountainText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    // Create and trigger download
    const a = document.createElement('a');
    a.href = url;
    a.download = `${filename}.fountain`;
    document.body.appendChild(a);
    a.click();
    
    // Clean up
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  
  return (
    <Button
      variant="ghost"
      onClick={handleExport}
      className="border-2 border-black"
      title="Export as Fountain"
    >
      <FileDown className="h-4 w-4 mr-2" />
      Export
    </Button>
  );
};