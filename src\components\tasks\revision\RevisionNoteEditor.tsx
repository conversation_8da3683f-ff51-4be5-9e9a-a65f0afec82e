import React, { useState } from 'react';
import { Button } from '../../ui/button';

interface RevisionNoteEditorProps {
  initialContent?: string;
  onSave: (content: string) => void;
  onCancel: () => void;
}

export const RevisionNoteEditor: React.FC<RevisionNoteEditorProps> = ({
  initialContent = '',
  onSave,
  onCancel
}) => {
  const [content, setContent] = useState(initialContent);
  
  return (
    <div className="border-4 border-black p-4 bg-white">
      <textarea
        value={content}
        onChange={(e) => setContent(e.target.value)}
        placeholder="Paste revision notes here..."
        rows={10}
        className="w-full mb-4 p-2 border-2 border-black"
      />
      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={onCancel} className="border-2 border-black">
          Cancel
        </Button>
        <Button 
          onClick={() => onSave(content)}
          disabled={!content.trim()}
          className="bg-black text-white hover:bg-gray-800"
        >
          Save Notes
        </Button>
      </div>
    </div>
  );
}; 