/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        // Primary colors
        primary: '#000000',
        'primary-hover': '#1a1a1a',
        'primary-active': '#333333',
        
        // Accent colors
        accent: '#00FFF0',
        'accent-hover': '#00e6d9',
        'accent-active': '#00ccc2',
        
        // Secondary colors
        secondary: {
          pink: '#FF3AF2',
          yellow: '#FFE600',
          green: '#4DFF4D',
          blue: '#4D4DFF',
          orange: '#FF9B4D',
          purple: '#B44DFF',
        },
        
        // UI colors
        success: '#4DFF4D',
        warning: '#FFE600',
        error: '#FF4D4D',
        info: '#00FFF0',
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'monospace'],
      },
      fontSize: {
        'xs': '0.75rem',    // 12px
        'sm': '0.875rem',   // 14px
        'base': '1rem',     // 16px
        'lg': '1.125rem',   // 18px
        'xl': '1.25rem',    // 20px
        '2xl': '1.5rem',    // 24px
        '3xl': '1.875rem',  // 30px
        '4xl': '2.25rem',   // 36px
      },
      fontWeight: {
        normal: 400,
        medium: 500,
        semibold: 600,
        bold: 700,
      },
      borderWidth: {
        '3': '3px',
        '6': '6px'
      }
    },
  },
  plugins: [
    require("tailwindcss-animate")
  ],
  darkMode: 'class', // Enable dark mode with class strategy
};