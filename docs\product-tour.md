# Product Tour

## Overview
SUSCleanup is a collaborative story development platform for managing projects, episodes, acts, scenes, ideas, prompts, and more. This document provides a walkthrough of the main features and user flows.

## Main Features

### 1. Project Management
- Create, edit, and manage projects.
- Project types: TV, film, novel, etc.
- Collaborators and project status.

### 2. Episode & Act Structure
- Organize content into episodes, acts, and scenes.
- Drag-and-drop scene management.
- Color-coded acts and storylines.

### 3. Idea Pile
- Capture and categorize ideas (notes, images, links, etc.).
- Tagging, filtering, and project references.

### 4. Prompts
- Create and group writing prompts.
- Attach files (PDF, text) to prompts.

### 5. Workshop & Timeline
- Visualize story structure and plot points.
- Use the timeline to track progress and structure.

### 6. Write Module
- Dedicated writing environment.
- Links to episodes/acts for context.

## User Flow Example
1. Create a new project.
2. Add episodes and acts.
3. Capture ideas in the Idea Pile.
4. Organize scenes and storylines.
5. Use prompts and workshop tools to develop your story.
6. Write and revise in the Write module.

## Screenshots & GIFs
*(Add images or GIFs here to illustrate key features and flows.)*

## Tips & Best Practices
- Use tags and filters to organize ideas.
- Collaborate with team members using the project sharing feature.
- Regularly use the timeline and workshop for high-level story planning. 