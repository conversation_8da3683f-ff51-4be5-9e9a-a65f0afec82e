import { supabase, getAuthUser, withAuth } from './client';
import type { Project, Episode, Act, Scene, Storyline } from '../../types/project';
import { logger } from '../../utils/logger';
import { clearProjectData } from '../../utils/storageUtils';

// Projects API
export const projectsApi = {
  async getProjects(): Promise<Project[]> {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .order('updated_at', { ascending: false });
      
    if (error) {
      logger.error('Failed to fetch projects:', error);
      throw error;
    }
    
    return data;
  },
  
  async createProject(name: string, type: 'tv-30' | 'tv-60' | 'film' | 'novel', template: 'classic' | 'enhanced' = 'classic'): Promise<Project> {
    try {
      return await withAuth(async () => {
        const { data, error } = await supabase
          .from('projects')
          .insert([{ name, type, template }])
          .select()
          .single();
        
        if (error) {
          logger.error('Failed to create project:', error);
          throw error;
        }
        
        return data;
      });
    } catch (error) {
      logger.error('Project creation failed:', error);
      throw error;
    }
  },
  
  async getProject(id: string): Promise<Project> {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('id', id)
      .single();
      
    if (error) {
      logger.error(`Failed to fetch project ${id}:`, error);
      throw error;
    }
    
    return data;
  },
  
  async updateProject(id: string, updates: Partial<Project>): Promise<Project> {
    try {
      return await withAuth(async () => {
        // Remove lastModified from updates as it doesn't exist in the database schema
        const { lastModified, ...dbUpdates } = updates;
        
        const { data, error } = await supabase
          .from('projects')
          .update(dbUpdates)
          .eq('id', id)
          .select()
          .single();
        
        if (error) {
          logger.error(`Failed to update project ${id}:`, error);
          throw error;
        }
        
        return data;
      });
    } catch (error) {
      logger.error(`Project update failed for ${id}:`, error);
      throw error;
    }
  },
  
  async deleteProject(id: string): Promise<void> {
    try {
      await withAuth(async () => {
        const { error } = await supabase
          .from('projects')
          .delete()
          .eq('id', id);
          
        if (error) {
          logger.error(`Failed to delete project ${id}:`, error);
          throw error;
        }
        
        // Clear all project-related data from localStorage
        clearProjectData(id);
        
        logger.debug(`Project ${id} deleted successfully`);
      });
    } catch (error) {
      logger.error(`Failed to delete project ${id}:`, error);
      throw error;
    }
  }
};

// Episodes API
export const episodesApi = {
  async getEpisodes(projectId: string): Promise<Episode[]> {
    const { data, error } = await supabase
      .from('episodes')
      .select('*')
      .eq('project_id', projectId)
      .order('number');
      
    if (error) {
      logger.error(`Failed to fetch episodes for project ${projectId}:`, error);
      throw error;
    }
    
    return data;
  },
  
  async createEpisode(projectId: string, number: number, title: string): Promise<Episode> {
    const { data, error } = await supabase
      .from('episodes')
      .insert([{ project_id: projectId, number, title }])
      .select()
      .single();
      
    if (error) {
      logger.error(`Failed to create episode for project ${projectId}:`, error);
      throw error;
    }
    
    return data;
  },
  
  async getEpisode(id: string): Promise<Episode> {
    const { data, error } = await supabase
      .from('episodes')
      .select('*')
      .eq('id', id)
      .single();
      
    if (error) {
      logger.error(`Failed to fetch episode ${id}:`, error);
      throw error;
    }
    
    return data;
  },
  
  async updateEpisode(id: string, updates: Partial<Episode>): Promise<Episode> {
    const { data, error } = await supabase
      .from('episodes')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
      
    if (error) {
      logger.error(`Failed to update episode ${id}:`, error);
      throw error;
    }
    
    return data;
  },
  
  async deleteEpisode(id: string): Promise<void> {
    const { error } = await supabase
      .from('episodes')
      .delete()
      .eq('id', id);
      
    if (error) {
      logger.error(`Failed to delete episode ${id}:`, error);
      throw error;
    }
  }
};

// Acts API
export const actsApi = {
  async getActs(episodeId: string): Promise<Act[]> {
    const { data, error } = await supabase
      .from('acts')
      .select('*')
      .eq('episode_id', episodeId)
      .order('position');
      
    if (error) {
      logger.error(`Failed to fetch acts for episode ${episodeId}:`, error);
      throw error;
    }
    
    return data;
  },
  
  async createAct(episodeId: string, title: string, color: string, position: number): Promise<Act> {
    const { data, error } = await supabase
      .from('acts')
      .insert([{ episode_id: episodeId, title, color, position }])
      .select()
      .single();
      
    if (error) {
      logger.error(`Failed to create act for episode ${episodeId}:`, error);
      throw error;
    }
    
    return data;
  },
  
  async updateAct(id: string, updates: Partial<Act>): Promise<Act> {
    const { data, error } = await supabase
      .from('acts')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
      
    if (error) {
      logger.error(`Failed to update act ${id}:`, error);
      throw error;
    }
    
    return data;
  },
  
  async deleteAct(id: string): Promise<void> {
    const { error } = await supabase
      .from('acts')
      .delete()
      .eq('id', id);
      
    if (error) {
      logger.error(`Failed to delete act ${id}:`, error);
      throw error;
    }
  }
};

// Scenes API
export const scenesApi = {
  async getScenes(actId: string): Promise<Scene[]> {
    const { data, error } = await supabase
      .from('scenes')
      .select('*')
      .eq('act_id', actId)
      .order('position');
      
    if (error) {
      logger.error(`Failed to fetch scenes for act ${actId}:`, error);
      throw error;
    }
    
    return data;
  },
  
  async createScene(actId: string, title: string, position: number): Promise<Scene> {
    const { data, error } = await supabase
      .from('scenes')
      .insert([{ act_id: actId, title, position }])
      .select()
      .single();
      
    if (error) {
      logger.error(`Failed to create scene for act ${actId}:`, error);
      throw error;
    }
    
    return data;
  },
  
  async updateScene(id: string, updates: Partial<Scene>): Promise<Scene> {
    const { data, error } = await supabase
      .from('scenes')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
      
    if (error) {
      logger.error(`Failed to update scene ${id}:`, error);
      throw error;
    }
    
    return data;
  },
  
  async deleteScene(id: string): Promise<void> {
    const { error } = await supabase
      .from('scenes')
      .delete()
      .eq('id', id);
      
    if (error) {
      logger.error(`Failed to delete scene ${id}:`, error);
      throw error;
    }
  }
};

// Storylines API
export const storylinesApi = {
  async getStorylines(projectId: string): Promise<Storyline[]> {
    const { data, error } = await supabase
      .from('storylines')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at');
      
    if (error) {
      logger.error(`Failed to fetch storylines for project ${projectId}:`, error);
      throw error;
    }
    
    return data;
  },
  
  async createStoryline(projectId: string, title: string, color: string): Promise<Storyline> {
    const { data, error } = await supabase
      .from('storylines')
      .insert([{ project_id: projectId, title, color }])
      .select()
      .single();
      
    if (error) {
      logger.error(`Failed to create storyline for project ${projectId}:`, error);
      throw error;
    }
    
    return data;
  },
  
  async updateStoryline(id: string, updates: Partial<Storyline>): Promise<Storyline> {
    const { data, error } = await supabase
      .from('storylines')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
      
    if (error) {
      logger.error(`Failed to update storyline ${id}:`, error);
      throw error;
    }
    
    return data;
  },
  
  async deleteStoryline(id: string): Promise<void> {
    const { error } = await supabase
      .from('storylines')
      .delete()
      .eq('id', id);
      
    if (error) {
      logger.error(`Failed to delete storyline ${id}:`, error);
      throw error;
    }
  },
  
  async toggleEpisode(storylineId: string, episodeId: string, isActive: boolean): Promise<void> {
    // Check if the relationship exists
    const { data: existing } = await supabase
      .from('storyline_episodes')
      .select('*')
      .eq('storyline_id', storylineId)
      .eq('episode_id', episodeId)
      .single();
      
    if (existing) {
      // Update existing relationship
      const { error } = await supabase
        .from('storyline_episodes')
        .update({ is_active: isActive })
        .eq('storyline_id', storylineId)
        .eq('episode_id', episodeId);
        
      if (error) {
        logger.error(`Failed to update storyline episode relationship:`, error);
        throw error;
      }
    } else {
      // Create new relationship
      const { error } = await supabase
        .from('storyline_episodes')
        .insert([{ storyline_id: storylineId, episode_id: episodeId, is_active: isActive }]);
        
      if (error) {
        logger.error(`Failed to create storyline episode relationship:`, error);
        throw error;
      }
    }
  }
};