import { useState, useCallback, useEffect } from 'react';
import { logger } from '../../utils/logger';
import type { Character, CharacterScene, CharacterAppearance } from '../../types/character';

export function useCharacterState(projectId: string) {
  const [characters, setCharacters] = useState<Character[]>([]);
  const [selectedCharacter, setSelectedCharacter] = useState<string | null>(null);
  const [appearances, setAppearances] = useState<CharacterAppearance[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Local storage keys
  const CHARACTERS_KEY = `characters_${projectId}`;
  const APPEARANCES_KEY = `appearances_${projectId}`;

  // Load characters from localStorage
  useEffect(() => {
    const loadCharacters = () => {
      try {
        setIsLoading(true);
        const saved = localStorage.getItem(CHARACTERS_KEY);
        const data = saved ? JSON.parse(saved) : [];
        setCharacters(data);
        logger.debug('Characters loaded from localStorage:', data.length);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to load characters'));
        logger.error('Failed to load characters from localStorage:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadCharacters();
  }, [projectId, CHARACTERS_KEY]);

  // Load appearances when character selected
  useEffect(() => {
    if (!selectedCharacter) return;

    const loadAppearances = () => {
      try {
        const saved = localStorage.getItem(APPEARANCES_KEY);
        const allAppearances = saved ? JSON.parse(saved) : [];
        const characterAppearances = allAppearances.filter(
          (app: CharacterAppearance) => app.characterId === selectedCharacter
        );
        setAppearances(characterAppearances);
        logger.debug('Character appearances loaded from localStorage:', characterAppearances.length);
      } catch (err) {
        logger.error('Failed to load character appearances from localStorage:', err);
      }
    };

    loadAppearances();
  }, [selectedCharacter, APPEARANCES_KEY]);

  const createCharacter = useCallback((
    character: Omit<Character, 'id' | 'createdAt' | 'updatedAt'>
  ) => {
    try {
      const newCharacter: Character = {
        ...character,
        id: `char-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const updatedCharacters = [...characters, newCharacter];
      setCharacters(updatedCharacters);
      localStorage.setItem(CHARACTERS_KEY, JSON.stringify(updatedCharacters));
      logger.debug('Character created locally:', newCharacter.id);
      return newCharacter;
    } catch (err) {
      logger.error('Failed to create character:', err);
      throw err;
    }
  }, [characters, CHARACTERS_KEY]);

  const updateCharacter = useCallback((
    id: string,
    updates: Partial<Character>
  ) => {
    try {
      const updatedCharacters = characters.map(c =>
        c.id === id ? { ...c, ...updates, updatedAt: new Date() } : c
      );
      setCharacters(updatedCharacters);
      localStorage.setItem(CHARACTERS_KEY, JSON.stringify(updatedCharacters));

      const updatedCharacter = updatedCharacters.find(c => c.id === id);
      logger.debug('Character updated locally:', id);
      return updatedCharacter;
    } catch (err) {
      logger.error('Failed to update character:', err);
      throw err;
    }
  }, [characters, CHARACTERS_KEY]);

  const deleteCharacter = useCallback((id: string) => {
    try {
      const updatedCharacters = characters.filter(c => c.id !== id);
      setCharacters(updatedCharacters);
      localStorage.setItem(CHARACTERS_KEY, JSON.stringify(updatedCharacters));

      if (selectedCharacter === id) {
        setSelectedCharacter(null);
      }
      logger.debug('Character deleted locally:', id);
    } catch (err) {
      logger.error('Failed to delete character:', err);
      throw err;
    }
  }, [characters, selectedCharacter, CHARACTERS_KEY]);

  const updateCharacterScene = useCallback((
    characterId: string,
    sceneId: string,
    updates: Partial<CharacterScene>
  ) => {
    try {
      // For local storage, we'll just log this operation
      // In a real implementation, this would update scene data
      logger.debug('Character scene updated locally:', { characterId, sceneId, updates });
      return { characterId, sceneId, ...updates };
    } catch (err) {
      logger.error('Failed to update character scene:', err);
      throw err;
    }
  }, []);

  return {
    characters,
    selectedCharacter,
    appearances,
    isLoading,
    error,
    setSelectedCharacter,
    createCharacter,
    updateCharacter,
    deleteCharacter,
    updateCharacterScene
  };
}