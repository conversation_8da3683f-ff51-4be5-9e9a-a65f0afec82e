import { useState, useCallback, useEffect } from 'react';
import { charactersApi } from '../../lib/supabase/characters';
import { logger } from '../../utils/logger';
import type { Character, CharacterScene, CharacterAppearance } from '../../types/character';

export function useCharacterState(projectId: string) {
  const [characters, setCharacters] = useState<Character[]>([]);
  const [selectedCharacter, setSelectedCharacter] = useState<string | null>(null);
  const [appearances, setAppearances] = useState<CharacterAppearance[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Load characters
  useEffect(() => {
    const loadCharacters = async () => {
      try {
        setIsLoading(true);
        const data = await charactersApi.getCharacters(projectId);
        setCharacters(data);
        logger.debug('Characters loaded:', data.length);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to load characters'));
        logger.error('Failed to load characters:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadCharacters();
  }, [projectId]);

  // Load appearances when character selected
  useEffect(() => {
    if (!selectedCharacter) return;

    const loadAppearances = async () => {
      try {
        const data = await charactersApi.getCharacterAppearances(selectedCharacter, projectId);
        setAppearances(data);
        logger.debug('Character appearances loaded:', data.length);
      } catch (err) {
        logger.error('Failed to load character appearances:', err);
      }
    };

    loadAppearances();
  }, [selectedCharacter, projectId]);

  // Subscribe to character changes
  useEffect(() => {
    if (!selectedCharacter) return;

    const subscription = charactersApi.subscribeToCharacter(
      selectedCharacter,
      (character) => {
        setCharacters(prev => 
          prev.map(c => c.id === character.id ? character : c)
        );
        logger.debug('Character updated:', character.id);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [selectedCharacter]);

  const createCharacter = useCallback(async (
    character: Omit<Character, 'id' | 'createdAt' | 'updatedAt'>
  ) => {
    try {
      const newCharacter = await charactersApi.createCharacter(character);
      setCharacters(prev => [...prev, newCharacter]);
      logger.debug('Character created:', newCharacter.id);
      return newCharacter;
    } catch (err) {
      logger.error('Failed to create character:', err);
      throw err;
    }
  }, []);

  const updateCharacter = useCallback(async (
    id: string,
    updates: Partial<Character>
  ) => {
    try {
      const updatedCharacter = await charactersApi.updateCharacter(id, updates);
      setCharacters(prev =>
        prev.map(c => c.id === id ? updatedCharacter : c)
      );
      logger.debug('Character updated:', id);
      return updatedCharacter;
    } catch (err) {
      logger.error('Failed to update character:', err);
      throw err;
    }
  }, []);

  const deleteCharacter = useCallback(async (id: string) => {
    try {
      await charactersApi.deleteCharacter(id);
      setCharacters(prev => prev.filter(c => c.id !== id));
      if (selectedCharacter === id) {
        setSelectedCharacter(null);
      }
      logger.debug('Character deleted:', id);
    } catch (err) {
      logger.error('Failed to delete character:', err);
      throw err;
    }
  }, [selectedCharacter]);

  const updateCharacterScene = useCallback(async (
    characterId: string,
    sceneId: string,
    updates: Partial<CharacterScene>
  ) => {
    try {
      const updatedScene = await charactersApi.updateCharacterScene(
        characterId,
        sceneId,
        updates
      );
      logger.debug('Character scene updated:', { characterId, sceneId });
      return updatedScene;
    } catch (err) {
      logger.error('Failed to update character scene:', err);
      throw err;
    }
  }, []);

  return {
    characters,
    selectedCharacter,
    appearances,
    isLoading,
    error,
    setSelectedCharacter,
    createCharacter,
    updateCharacter,
    deleteCharacter,
    updateCharacterScene
  };
}