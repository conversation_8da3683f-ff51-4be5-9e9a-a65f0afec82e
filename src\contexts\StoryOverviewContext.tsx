import React, { createContext, useContext, useState } from 'react';

interface StoryOverviewContextType {
  isVisible: boolean;
  setIsVisible: (visible: boolean) => void;
}

const StoryOverviewContext = createContext<StoryOverviewContextType | undefined>(undefined);

export function StoryOverviewProvider({ children }: { children: React.ReactNode }) {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <StoryOverviewContext.Provider value={{ isVisible, setIsVisible }}>
      {children}
    </StoryOverviewContext.Provider>
  );
}

export function useStoryOverview() {
  const context = useContext(StoryOverviewContext);
  if (context === undefined) {
    throw new Error('useStoryOverview must be used within a StoryOverviewProvider');
  }
  return context;
}
