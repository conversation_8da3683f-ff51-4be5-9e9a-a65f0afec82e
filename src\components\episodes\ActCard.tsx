import React from 'react';
import { Plus, X, <PERSON>, <PERSON>Off, MoveVertical } from 'lucide-react';
import { Button } from '../ui/button';
import { cn } from '../../lib/utils';
import { EditableText } from './header/EditableText';
import type { Act, Scene } from '../../types/episode';
import { SceneCard } from './SceneCard';

interface ActCardProps {
  act: any;
  episodeActs?: any[];
  onAddScene: () => void;
  onDeleteAct: () => void;
  onSceneTitleChange: (sceneId: number, title: string) => void;
  onSceneDelete: (sceneId: number) => void;
  onActTitleChange: (title: string) => void;
  onActSubtitleChange: (subtitle: string) => void;
  onSceneDrop: (sceneData: any) => void;
}

export const ActCard: React.FC<ActCardProps> = ({
  act,
  episodeActs,
  onAddScene,
  onDeleteAct,
  onSceneTitleChange,
  onSceneDelete,
  onActTitleChange,
  onActSubtitleChange,
  onSceneDrop
}) => {
  const allActs = episodeActs || [];
  const allScenes = allActs.flatMap((a: any) => a.scenes.map((scene: any) => ({ ...scene, actId: a.id })));

  return (
    <div className="border-4 border-black">
      <div className={cn(
        'flex items-center justify-between px-2 py-1',
        act.color,
        'border-b-4 border-black'
      )}>
        <div className="flex flex-col">
          <EditableText
            value={act.title}
            onChange={onActTitleChange}
            isTitle
            placeholder="Enter title..."
          />
          <EditableText
            value={act.subtitle || ''}
            onChange={onActSubtitleChange}
            placeholder="Add subtitle..."
          />
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            onClick={onDeleteAct}
            className="h-5 w-5 p-0 hover:bg-white/10 text-white"
            title="Delete Act"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
      
      <div className="p-2">
        <div className="grid grid-cols-1 gap-2">
          {act.scenes.map((scene: any) => {
            // Find the global index of this scene in allScenes
            const globalIndex = allScenes.findIndex((s: any) => s.id === scene.id && s.actId === act.id);
            return (
              <SceneCard
                key={scene.id}
                scene={scene}
                index={globalIndex}
                groupId={act.id}
                color={act.color}
                storylineId={scene.storylineId}
                onUpdate={updates => onSceneTitleChange(scene.id, updates.title !== undefined ? updates.title : scene.title)}
                onDelete={() => onSceneDelete(scene.id)}
                onDragStart={() => {}}
                onDragOver={() => {}}
                onDrop={() => {}}
                onStorylineChange={undefined}
                isEpisodeExpanded={false}
              />
            );
          })}
          <Button
            variant={"outline" as any}
            size="sm"
            onClick={onAddScene}
            className="w-full border-dashed text-xs text-gray-500 hover:bg-gray-50"
          >
            <Plus className="h-3 w-3 mr-1" /> Add Scene
          </Button>
        </div>
      </div>
    </div>
  );
};