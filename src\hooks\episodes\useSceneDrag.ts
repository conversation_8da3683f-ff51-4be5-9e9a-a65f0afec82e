import { useState, useCallback } from 'react';
import { logger } from '../../utils/logger';
import type { Scene } from '../../types/episode';

interface UseSceneDragOptions {
  actId: string;
  onReorderScenes: (scenes: Scene[]) => void;
}

export function useSceneDrag({ actId, onReorderScenes }: UseSceneDragOptions) {
  const [draggedScene, setDraggedScene] = useState<Scene | null>(null);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dropTarget, setDropTarget] = useState<number | null>(null);

  const handleDragStart = useCallback((scene: Scene, index: number) => {
    setDraggedScene(scene);
    setDraggedIndex(index);
    logger.debug('Scene drag started:', { scene, index, actId });
  }, [actId]);

  const handleDragOver = useCallback((e: React.DragEvent, index: number) => {
    e.preventDefault();
    setDropTarget(index);
    e.dataTransfer.dropEffect = 'move';
  }, []);

  const handleDrop = useCallback((scenes: Scene[], targetIndex: number) => {
    if (draggedScene === null || draggedIndex === null) return scenes;

    // Only reorder if we're dropping in a different position
    if (draggedIndex !== targetIndex) {
      try {
        const newScenes = [...scenes];
        const [movedScene] = newScenes.splice(draggedIndex, 1);
        newScenes.splice(targetIndex, 0, movedScene);
        
        logger.debug('Scene reordered:', { 
          from: draggedIndex, 
          to: targetIndex, 
          scene: draggedScene 
        });
        
        onReorderScenes(newScenes);
        return newScenes;
      } catch (error) {
        logger.error('Failed to reorder scenes:', error);
        return scenes;
      }
    }
    
    return scenes;
  }, [draggedScene, draggedIndex, onReorderScenes]);

  const handleDragEnd = useCallback(() => {
    setDraggedScene(null);
    setDraggedIndex(null);
    setDropTarget(null);
  }, []);

  return {
    draggedScene,
    draggedIndex,
    dropTarget,
    handleDragStart,
    handleDragOver,
    handleDrop,
    handleDragEnd
  };
}