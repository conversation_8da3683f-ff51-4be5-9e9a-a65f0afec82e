import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { produce } from 'immer';
import { logger } from '../utils/logger';
import { clearProjectData, getProjectScopedKey, generateUniqueId } from '../utils/storageUtils';
import type { Project } from '../types/project';

interface ProjectState {
  currentProject: Project | null;
  projects: Project[];
  lastSaved: Date | null;
  loading: boolean;
  error: string | null;
}

interface ProjectContextType extends ProjectState {
  openProject: (project: Project) => void;
  createProject: (name: string, type: 'tv-30' | 'tv-60' | 'film' | 'novel', template?: 'classic' | 'enhanced') => void;
  saveProject: () => void;
  deleteProject: (id: string) => void;
  closeProject: () => void;
  updateProject: (id: string, updates: Partial<Project>) => void;
  addCollaborator: (email: string) => void;
  removeCollaborator: (email: string) => void;
}

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

const STORAGE_KEY = 'story_unstuck_projects'; 
const AUTOSAVE_INTERVAL = 30000; // 30 seconds

export function ProjectProvider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState<ProjectState>(() => {
    return {
      currentProject: null,
      projects: [],
      lastSaved: null,
      loading: false,
      error: null
    };
  });

  // Load projects on mount
  useEffect(() => {
    const loadProjects = async () => {
      const localProjects: Project[] = [];
      
      // Load from localStorage only
      try {
        const savedProjects = localStorage.getItem(STORAGE_KEY);
        if (savedProjects) {
          let parsedProjects = JSON.parse(savedProjects);
          // Patch: Ensure parsedProjects is always an array
          if (!Array.isArray(parsedProjects)) {
            logger.warn('Corrupted projects data in localStorage, resetting to empty array.');
            parsedProjects = [];
            localStorage.setItem(STORAGE_KEY, JSON.stringify(parsedProjects));
          }
          localProjects.push(...parsedProjects.map((p: any) => ({
            ...p,
            lastModified: new Date(p.updated_at || p.lastModified || Date.now())
          })));
          logger.debug('Projects loaded from localStorage:', parsedProjects);
        }
        
        // Use local projects
        setState(prev => ({
          ...prev,
          projects: localProjects
        }));
        
      } catch (localStorageError) {
        logger.error('Failed to load projects from localStorage:', localStorageError);
      }
    };
    
    loadProjects();
  }, []);

  // Autosave timer
  useEffect(() => {
    if (!state.currentProject) return;

    const timer = setInterval(() => {
      saveProject();
    }, AUTOSAVE_INTERVAL);

    return () => clearInterval(timer);
  }, [state.currentProject]); // eslint-disable-line react-hooks/exhaustive-deps

  const saveProject = useCallback(() => {
    if (!state.currentProject) return;
    
    setState(produce(draft => {
      const now = new Date();
      
      // Find the current project in the projects array
      const projectIndex = draft.projects.findIndex(p => p.id === draft.currentProject?.id);
      if (projectIndex >= 0) {
        draft.projects[projectIndex] = {
          ...draft.currentProject,
          lastModified: now
        };
      }
      
      if (draft.currentProject) {
        draft.currentProject.lastModified = now;
      }
      
      draft.lastSaved = now;
    }));
    
    // Save to localStorage
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state.projects.map(p => 
        p.id === state.currentProject?.id ? {...state.currentProject, lastModified: new Date()} : p
      )));
    } catch (error) {
      logger.error('Failed to save project to localStorage', error);
    }
  }, [state.currentProject, state.projects]);
  
  const openProject = useCallback((project: Project) => {
    setState(produce(draft => {
      draft.currentProject = {
        ...project,
        lastModified: new Date(project.updated_at || project.lastModified)
      };
      draft.lastSaved = null;
    }));
    
    // Clear any scene selection when switching projects
    localStorage.removeItem(getProjectScopedKey(project.id, 'activeSceneId'));
    
    logger.debug('Project opened:', project);
  }, []);

  const createProject = useCallback((name: string, type: 'tv-30' | 'tv-60' | 'film' | 'novel', template: 'classic' | 'enhanced' = 'classic') => {
    // Generate a UUID for the new project
    const id = generateUniqueId('proj');
    
    // Create the new project object
    const newProject: Project = {
      id,
      name,
      type,
      template,
      status: 'active',
      lastModified: new Date(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    // Add to local projects
    setState(prev => {
      const next = produce(prev, draft => {
        // Add to projects array
        draft.projects.push(newProject);
        // Set as current project
        draft.currentProject = newProject;
        // Reset loading and error states
        draft.loading = false;
        draft.error = null;
      });

      // Save to localStorage
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(next.projects));
      } catch (error) {
        logger.error('Failed to save projects to localStorage:', error);
      }
      
      return next;
    });
    
    // Clear any scene-specific cached data
    clearProjectData();
    
    return newProject;
  }, []);

  const deleteProject = useCallback((id: string) => {
    setState(produce(draft => {
      draft.projects = draft.projects.filter(p => p.id !== id);
      
      // Clear current project if it's the one being deleted
      if (draft.currentProject && draft.currentProject.id === id) {
        draft.currentProject = null;
      }
    }));
    
    // Save to localStorage
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state.projects.filter(p => p.id !== id)));
    } catch (error) {
      logger.error('Failed to delete project from localStorage', error);
    }
  }, [state.projects]);

  const updateProject = useCallback((id: string, updates: Partial<Project>) => {
    setState(produce(draft => {
      const projectIndex = draft.projects.findIndex(p => p.id === id);
      if (projectIndex >= 0) {
        draft.projects[projectIndex] = {
          ...draft.projects[projectIndex],
          ...updates,
          lastModified: new Date()
        };
        
        // Update current project if it's the one being updated
        if (draft.currentProject && draft.currentProject.id === id) {
          draft.currentProject = draft.projects[projectIndex];
        }
      }
    }));
    
    // Save to localStorage
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state.projects.map(p => 
        p.id === id ? {...p, ...updates, lastModified: new Date()} : p
      )));
    } catch (error) {
      logger.error('Failed to update project in localStorage', error);
    }
  }, [state.projects]);

  const closeProject = useCallback(() => {
    setState(produce(draft => {
      if (draft.currentProject) {
        // Save current project state to projects array
        const index = draft.projects.findIndex(p => p.id === draft.currentProject?.id);
        if (index !== -1 && draft.currentProject) {
          draft.projects[index] = {
            ...draft.currentProject,
            lastModified: new Date(),
            updated_at: new Date().toISOString()
          };
        }
      }

      // Clear any scene selection when closing project
      if (draft.currentProject) {
        localStorage.removeItem(getProjectScopedKey(draft.currentProject.id, 'activeSceneId'));
      }
      
      // Clear current project
      draft.currentProject = null;
      draft.lastSaved = null;
    }));

    logger.debug('Project closed');
  }, []); 

  // Add a collaborator to the current project
  const addCollaborator = useCallback((email: string) => {
    if (!state.currentProject) return;

    setState(produce(draft => {
      if (draft.currentProject) {
        // Initialize collaborators array if it doesn't exist
        if (!draft.currentProject.collaborators) {
          draft.currentProject.collaborators = [];
        }

        // Only add if not already in the list
        if (!draft.currentProject.collaborators.includes(email)) {
          draft.currentProject.collaborators.push(email);
          
          // Update in projects array too
          const projectIndex = draft.projects.findIndex(p => p.id === draft.currentProject?.id);
          if (projectIndex >= 0) {
            if (!draft.projects[projectIndex].collaborators) {
              draft.projects[projectIndex].collaborators = [];
            }
            draft.projects[projectIndex].collaborators?.push(email);
          }
        }
      }
    }));

    // Save to localStorage
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state.projects.map(p => 
        p.id === state.currentProject?.id ? {...p, collaborators: state.currentProject.collaborators} : p
      )));
    } catch (error) {
      logger.error('Failed to add collaborator to project', error);
    }
  }, [state.currentProject, state.projects]);

  // Remove a collaborator from the current project
  const removeCollaborator = useCallback((email: string) => {
    if (!state.currentProject || !state.currentProject.collaborators) return;

    setState(produce(draft => {
      if (draft.currentProject && draft.currentProject.collaborators) {
        draft.currentProject.collaborators = draft.currentProject.collaborators.filter(e => e !== email);
        
        // Update in projects array too
        const projectIndex = draft.projects.findIndex(p => p.id === draft.currentProject?.id);
        if (projectIndex >= 0 && draft.projects[projectIndex].collaborators) {
          draft.projects[projectIndex].collaborators = draft.projects[projectIndex].collaborators?.filter(e => e !== email);
        }
      }
    }));

    // Save to localStorage
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state.projects.map(p => 
        p.id === state.currentProject?.id ? {...p, collaborators: state.currentProject.collaborators} : p
      )));
    } catch (error) {
      logger.error('Failed to remove collaborator from project', error);
    }
  }, [state.currentProject, state.projects]);

  const value = {
    ...state,
    openProject,
    createProject,
    saveProject,
    deleteProject,
    closeProject,
    updateProject,
    addCollaborator,
    removeCollaborator
  };

  return (
    <ProjectContext.Provider value={{...value}}>
      {children}
    </ProjectContext.Provider>
  );
}

export function useProject() {
  const context = useContext(ProjectContext);
  if (context === undefined) {
    throw new Error('useProject must be used within a ProjectProvider');
  }
  return context;
}