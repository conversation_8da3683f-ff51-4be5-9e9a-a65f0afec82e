import React from 'react';
import { ChevronRight, ChevronDown, FileText, Edit, Plus, CheckSquare, Square } from 'lucide-react';
import { Button } from '../../ui/button';
import { cn } from '../../../lib/utils';
import { useWriteNavigation, WriteScene } from '../../../hooks/write/useWriteNavigation';
import { useEpisodeState } from '../../../hooks/useEpisodeState';
import { logger } from '../../../utils/logger';

interface WriteNavigationProps {
  isCollapsed: boolean;
  activeSceneId?: string;
  onSceneSelect: (sceneId?: string) => void;
}

export const WriteNavigation: React.FC<WriteNavigationProps> = ({
  isCollapsed,
  activeSceneId,
  onSceneSelect
}) => {
  const { 
    availableScenes, 
    expandedEpisodes, 
    expandedActs,
    toggleEpisode, 
    toggleAct,
    markSceneComplete,
    addScene
  } = useWriteNavigation();
  const { episodes, episodeData } = useEpisodeState();

  const handleSceneClick = (sceneId: string) => {
    logger.debug('Scene clicked', { sceneId });
    onSceneSelect(sceneId);
  };

  const handleToggleComplete = (e: React.MouseEvent, sceneId: string, isComplete: boolean) => {
    e.stopPropagation();
    markSceneComplete(sceneId, !isComplete);
  };

  const handleAddScene = (e: React.MouseEvent, episodeId: number, actId: string) => {
    e.preventDefault();
    e.stopPropagation();
    
    const newSceneId = addScene(episodeId, actId);
    if (newSceneId) {
      // Auto-select the new scene
      onSceneSelect(newSceneId);
    }
  };

  return (
    <div className="flex-1 overflow-y-auto">
      {episodes.map(episodeId => {
        // Check if this episode has any scenes in availableScenes
        const episodeHasScenes = availableScenes.some(s => s.episodeId === episodeId);
        const isEpisodeExpanded = expandedEpisodes.has(episodeId);
        
        return (
          <div key={episodeId} className="border-b border-black last:border-b-0">
            <div 
              className="px-4 py-2 font-mono font-bold flex items-center justify-between cursor-pointer hover:bg-gray-50"
              onClick={() => toggleEpisode(episodeId)}
            >
              <div className="flex items-center gap-2">
                {isCollapsed ? (
                  <span>EP{episodeId}</span>
                ) : (
                  <>
                    {isEpisodeExpanded ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                    <span>Episode {episodeId}</span>
                  </>
                )}
              </div>
              {!isCollapsed && (
                <div className="text-xs text-gray-500">
                  {episodeHasScenes ? (
                    <FileText className="h-3 w-3" />
                  ) : (
                    <Edit className="h-3 w-3" />
                  )}
                </div>
              )}
            </div>
            
            {isEpisodeExpanded && !isCollapsed && episodeData[episodeId] && (
              <div className="pl-4">
                {(() => {
                  // Flatten all acts' scenes for this episode
                  const allScenes = episodeData[episodeId].acts.flatMap(act => act.scenes.map(scene => ({ ...scene, actId: act.id })));
                  return episodeData[episodeId].acts.map(act => {
                    const isActExpanded = expandedActs.has(act.id);
                    return (
                      <div key={act.id} className="mb-2">
                        <div 
                          className={cn(
                            "flex items-center justify-between py-1 px-2 cursor-pointer",
                            "text-white font-mono text-xs rounded-sm",
                            act.color
                          )}
                          onClick={() => toggleAct(act.id)}
                        >
                          <div className="flex items-center gap-1">
                            {isActExpanded ? (
                              <ChevronDown className="h-3 w-3" />
                            ) : (
                              <ChevronRight className="h-3 w-3" />
                            )}
                            <span>{act.title}</span>
                          </div>
                          <span className="text-xs opacity-80">
                            {act.scenes.length} {act.scenes.length === 1 ? 'scene' : 'scenes'}
                          </span>
                        </div>
                        {isActExpanded && (
                          <div className="pl-4 py-1">
                            {act.scenes.map((scene) => {
                              // Find the global index of this scene in allScenes
                              const globalIndex = allScenes.findIndex(s => s.id === scene.id && s.actId === act.id);
                              const sceneId = `${episodeId}-${act.id}-${scene.id}`;
                              const sceneData = availableScenes.find(s => s.id === sceneId);
                              const hasContent = sceneData?.hasContent || false;
                              const isComplete = sceneData?.isComplete || false;
                              return (
                                <div 
                                  key={scene.id}
                                  className="flex items-center gap-1 my-1"
                                >
                                  <button
                                    onClick={(e) => handleToggleComplete(e, sceneId, isComplete)}
                                    className="flex-shrink-0 text-gray-500 hover:text-black transition-colors"
                                    title={isComplete ? "Mark as incomplete" : "Mark as complete"}
                                  >
                                    {isComplete ? (
                                      <CheckSquare className="h-4 w-4 text-green-600" />
                                    ) : (
                                      <Square className="h-4 w-4" />
                                    )}
                                  </button>
                                  <button
                                    onClick={() => handleSceneClick(sceneId)}
                                    className={cn(
                                      "flex-1 text-left px-2 py-1 font-mono text-xs rounded-sm",
                                      "hover:bg-gray-50 transition-colors flex items-center justify-between",
                                      activeSceneId === sceneId && "bg-gray-100 font-bold",
                                      isComplete && "text-green-600"
                                    )}
                                  >
                                    <span className={cn(
                                      "truncate",
                                      isComplete && "line-through opacity-70"
                                    )}>
                                      {`Scene ${globalIndex + 1}`}
                                    </span>
                                    {hasContent && (
                                      <FileText className="h-3 w-3 text-gray-500 ml-1 flex-shrink-0" />
                                    )}
                                  </button>
                                </div>
                              );
                            })}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => handleAddScene(e, episodeId, act.id)}
                              className="w-full text-xs text-gray-500 mt-1 border border-dashed border-gray-300"
                            >
                              <Plus className="h-3 w-3 mr-1" />
                              Add Scene
                            </Button>
                          </div>
                        )}
                      </div>
                    );
                  });
                })()}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}