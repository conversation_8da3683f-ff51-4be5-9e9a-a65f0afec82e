export interface CharacterTraits {
  physical: string[];
  personality: string[];
  skills: string[];
  flaws: string[];
}

export interface CharacterRelationship {
  characterId: string;
  type: string;
  description: string;
  dynamics: string[];
  arc?: {
    startingPoint: string;
    endingPoint: string;
    keyMoments: string[];
  };
}

export interface CharacterArc {
  startingPoint: string;
  midpoint: string;
  ending: string;
  keyMoments: Array<{
    description: string;
    episode?: string;
    scene?: string;
    impact: string;
  }>;
}

export interface Character {
  id: string;
  projectId: string;
  name: string;
  role: string;
  description: string;
  goals: string[];
  traits: CharacterTraits;
  relationships: Record<string, CharacterRelationship>;
  arc: CharacterArc;
  createdAt: Date;
  updatedAt: Date;
}

export interface CharacterScene {
  id: string;
  characterId: string;
  sceneId: string;
  isActive: boolean;
  emotionalState?: string;
  notes?: string;
  createdAt: Date;
}

export interface CharacterAppearance {
  sceneId: string;
  episodeNumber: number;
  actTitle: string;
  sceneTitle: string;
  isActive: boolean;
  emotionalState?: string;
}