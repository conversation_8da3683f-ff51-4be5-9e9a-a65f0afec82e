import React, { useState } from 'react';
import { Plus } from 'lucide-react';
import { Button } from '../../ui/button';
import { StoryBlock } from './StoryBlock';
import { cn } from '../../../lib/utils';
import { ResizablePanel } from '../../timeline/ResizablePanel';

interface Block {
  id: string;
  title: string;
  color: string;
  sections: Array<{
    id: string;
    title: string;
    content: string;
  }>;
}

const defaultBlocks: Block[] = [
  {
    id: '1',
    title: 'GENRE & THEME',
    color: 'bg-blue-500',
    sections: [
      { id: '1-1', title: 'External & Internal Genre', content: '' },
      { id: '1-2', title: 'Theme/Controlling Idea', content: '' },
      { id: '1-3', title: 'NOT Goal', content: '' },
      { id: '1-4', title: 'Four Throughlines', content: '' }
    ]
  },
  {
    id: '2',
    title: 'CHARACTER & CONFLICT',
    color: 'bg-red-500',
    sections: [
      { id: '2-1', title: 'Want vs Need', content: '' },
      { id: '2-2', title: 'Terrible Trajectory', content: '' },
      { id: '2-3', title: 'Stakes', content: '' },
      { id: '2-4', title: 'Third Choice Resolution', content: '' }
    ]
  },
  {
    id: '3',
    title: 'STORY STRUCTURE',
    color: 'bg-green-500',
    sections: [
      { id: '3-1', title: 'Nutshell Elements', content: '' },
      { id: '3-2', title: 'Obligatory Scenes', content: '' },
      { id: '3-3', title: 'Decision Points', content: '' },
      { id: '3-4', title: 'Cost of Failure', content: '' }
    ]
  },
  {
    id: '4',
    title: 'PLOT FRAMEWORK',
    color: 'bg-purple-500',
    sections: [
      { id: '4-1', title: 'Situation Framework (A/B Story)', content: '' },
      { id: '4-2', title: 'Story Strands', content: '' },
      { id: '4-3', title: 'Conflict Layers', content: '' },
      { id: '4-4', title: 'Opening Hook', content: '' }
    ]
  }
];

const blockColors = ['bg-blue-500', 'bg-red-500', 'bg-green-500', 'bg-purple-500', 'bg-yellow-500', 'bg-[#00FFF0]'];

const worksheetSections = [
  { key: 'centralTheme', label: '🎯 CENTRAL THEME QUESTION', placeholder: 'What will you ______ to _____________?' },
  { key: 'characterFoundation', label: '🟢 CHARACTER FOUNDATION', placeholder: 'Who, Trajectory, Starts, Ends...' },
  { key: 'predicament', label: '🔴 THE PREDICAMENT', placeholder: 'Loyalties, Costs, Impossible Choice...' },
  { key: 'paradox', label: '💎 THE PARADOX', placeholder: 'The more ___ tries to ___ the more ___' },
  { key: 'universalFantasy', label: '🟡 UNIVERSAL FANTASY', placeholder: 'Audience secretly wants to...' },
  { key: 'pattern', label: '🟣 THE PATTERN', placeholder: 'When torn between loyalties, character...' },
  { key: 'plan', label: '🟦 THE PLAN (4 Storylines)', placeholder: 'A-Plot, B-Plot, C-Plot, D-Plot...' },
  { key: 'sceneExecution', label: '🔵 SCENE EXECUTION', placeholder: 'Every scene tests...' },
  { key: 'finalValidation', label: '✅ FINAL VALIDATION', placeholder: 'Character trap, story paradox, why addictive...' },
];

// Add checklist items for each phase
const checklistItems = [
  { key: 'foundation', label: 'Foundation (Theme & Character)' },
  { key: 'trap', label: 'The Trap (Predicament & Paradox)' },
  { key: 'structure', label: 'Structure (Pattern & Plan)' },
  { key: 'execution', label: 'Execution (Scene Testing & Validation)' },
  { key: 'convergence', label: 'All boxes ultimately serve the central theme question' },
];

export const StoryOverview: React.FC = () => {
  const [blocks, setBlocks] = useState<Block[]>(defaultBlocks);
  const [expandedBlocks, setExpandedBlocks] = useState<Set<string>>(new Set(['1']));
  const [worksheetLayout, setWorksheetLayout] = useState(false);
  const [worksheetData, setWorksheetData] = useState(() => Object.fromEntries(worksheetSections.map(s => [s.key, ''])));
  const [checklist, setChecklist] = useState(() => Object.fromEntries(checklistItems.map(i => [i.key, false])));

  const toggleExpand = (blockId: string) => {
    setExpandedBlocks(prev => {
      const next = new Set(prev);
      if (next.has(blockId)) {
        next.delete(blockId);
      } else {
        next.add(blockId);
      }
      return next;
    });
  };

  const handleAddBlock = () => {
    const newBlock: Block = {
      id: Date.now().toString(),
      title: 'NEW BLOCK',
      color: blockColors[blocks.length % blockColors.length],
      sections: [
        { 
          id: `${blocks.length + 1}-1`,
          title: 'Section 1',
          content: ''
        }
      ]
    };
    setBlocks(prev => [...prev, newBlock]);
    setExpandedBlocks(prev => new Set(prev).add(newBlock.id));
  };

  const handleUpdateBlock = (blockId: string, updates: { title?: string; color?: string }) => {
    setBlocks(prev => prev.map(block =>
      block.id === blockId ? { ...block, ...updates } : block
    ));
  };

  const handleDeleteBlock = (blockId: string) => {
    setBlocks(prev => prev.filter(block => block.id !== blockId));
    setExpandedBlocks(prev => {
      const next = new Set(prev);
      next.delete(blockId);
      return next;
    });
  };

  const handleAddSection = (blockId: string) => {
    setBlocks(prev => prev.map(block => {
      if (block.id === blockId) {
        const newSection = {
          id: `${blockId}-${block.sections.length + 1}`,
          title: 'New Section',
          content: ''
        };
        return {
          ...block,
          sections: [...block.sections, newSection]
        };
      }
      return block;
    }));
  };

  const handleUpdateSection = (blockId: string, sectionId: string, updates: { title?: string; content?: string }) => {
    setBlocks(prev => prev.map(block => {
      if (block.id === blockId) {
        return {
          ...block,
          sections: block.sections.map(section =>
            section.id === sectionId ? { ...section, ...updates } : section
          )
        };
      }
      return block;
    }));
  };

  const handleDeleteSection = (blockId: string, sectionId: string) => {
    setBlocks(prev => prev.map(block => {
      if (block.id === blockId) {
        return {
          ...block,
          sections: block.sections.filter(section => section.id !== sectionId)
        };
      }
      return block;
    }));
  };

  const handleWorksheetChange = (key: string, value: string) => {
    setWorksheetData(prev => ({ ...prev, [key]: value }));
  };

  const handleChecklistChange = (key: string) => {
    setChecklist(prev => ({ ...prev, [key]: !prev[key] }));
  };

  return (
    <div className="p-6" style={{ transform: 'scale(0.7)', transformOrigin: 'top left', width: '143%', height: '143%' }}>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">Story Overview</h2>
        <div className="flex gap-2">
          <Button onClick={() => setWorksheetLayout(w => !w)}>
            {worksheetLayout ? 'Blocks View' : 'Worksheet View'}
          </Button>
          {!worksheetLayout && (
            <Button onClick={handleAddBlock}>
              <Plus className="h-4 w-4 mr-2" />
              Add Block
            </Button>
          )}
        </div>
      </div>

      {!worksheetLayout ? (
        <div className={cn(
          'grid gap-6',
          'grid-cols-1 md:grid-cols-2'
        )}>
          {blocks.map(block => (
            <StoryBlock
              key={block.id}
              block={block}
              isExpanded={expandedBlocks.has(block.id)}
              onToggleExpand={() => toggleExpand(block.id)}
              onUpdateBlock={(updates) => handleUpdateBlock(block.id, updates)}
              onDelete={() => handleDeleteBlock(block.id)}
              onAddSection={() => handleAddSection(block.id)}
              onUpdateSection={(sectionId, updates) => 
                handleUpdateSection(block.id, sectionId, updates)
              }
              onDeleteSection={(sectionId) => handleDeleteSection(block.id, sectionId)}
            />
          ))}
        </div>
      ) : (
        <div className="flex flex-col gap-8">
          {/* Central Theme at the top, centered */}
          <div className="flex justify-center mb-4">
            <div className="w-2/3">
              <ResizablePanel minHeight={120} defaultHeight={160}>
                <div className="flex flex-col h-full items-center">
                  <label className="font-bold text-lg mb-2 text-center">{worksheetSections[0].label}</label>
                  <textarea
                    className="flex-1 w-full border rounded p-2 resize-none text-center"
                    placeholder={worksheetSections[0].placeholder}
                    value={worksheetData[worksheetSections[0].key]}
                    onChange={e => handleWorksheetChange(worksheetSections[0].key, e.target.value)}
                    style={{ minHeight: 60, background: '#fff' }}
                  />
                </div>
              </ResizablePanel>
            </div>
          </div>

          {/* Flow labels/arrows */}
          <div className="flex justify-between items-center mb-2 px-8">
            <span className="font-semibold text-blue-700">🟦 FOUNDATION</span>
            <span className="text-xl">→</span>
            <span className="font-semibold text-red-700">🟥 THE TRAP</span>
            <span className="text-xl">→</span>
            <span className="font-semibold text-purple-700">🟣 STRUCTURE</span>
            <span className="text-xl">→</span>
            <span className="font-semibold text-yellow-600">🟨 EXECUTION</span>
          </div>

          {/* Four columns for the flow */}
          <div className="grid grid-cols-4 gap-6 auto-rows-min px-8">
            {/* FOUNDATION */}
            <div className="flex flex-col gap-6">
              {/* Character Foundation */}
              <ResizablePanel minHeight={120} defaultHeight={140}>
                <div className="flex flex-col h-full">
                  <label className="font-semibold mb-2">{worksheetSections[1].label}</label>
                  <textarea
                    className="flex-1 w-full border rounded p-2 resize-none"
                    placeholder={worksheetSections[1].placeholder}
                    value={worksheetData[worksheetSections[1].key]}
                    onChange={e => handleWorksheetChange(worksheetSections[1].key, e.target.value)}
                    style={{ minHeight: 60, background: '#fff' }}
                  />
                </div>
              </ResizablePanel>
            </div>

            {/* THE TRAP */}
            <div className="flex flex-col gap-6">
              {/* Predicament */}
              <ResizablePanel minHeight={120} defaultHeight={120}>
                <div className="flex flex-col h-full">
                  <label className="font-semibold mb-2">{worksheetSections[2].label}</label>
                  <textarea
                    className="flex-1 w-full border rounded p-2 resize-none"
                    placeholder={worksheetSections[2].placeholder}
                    value={worksheetData[worksheetSections[2].key]}
                    onChange={e => handleWorksheetChange(worksheetSections[2].key, e.target.value)}
                    style={{ minHeight: 60, background: '#fff' }}
                  />
                </div>
              </ResizablePanel>
              {/* Paradox */}
              <ResizablePanel minHeight={120} defaultHeight={120}>
                <div className="flex flex-col h-full">
                  <label className="font-semibold mb-2">{worksheetSections[3].label}</label>
                  <textarea
                    className="flex-1 w-full border rounded p-2 resize-none"
                    placeholder={worksheetSections[3].placeholder}
                    value={worksheetData[worksheetSections[3].key]}
                    onChange={e => handleWorksheetChange(worksheetSections[3].key, e.target.value)}
                    style={{ minHeight: 60, background: '#fff' }}
                  />
                </div>
              </ResizablePanel>
              {/* Universal Fantasy */}
              <ResizablePanel minHeight={120} defaultHeight={120}>
                <div className="flex flex-col h-full">
                  <label className="font-semibold mb-2">{worksheetSections[4].label}</label>
                  <textarea
                    className="flex-1 w-full border rounded p-2 resize-none"
                    placeholder={worksheetSections[4].placeholder}
                    value={worksheetData[worksheetSections[4].key]}
                    onChange={e => handleWorksheetChange(worksheetSections[4].key, e.target.value)}
                    style={{ minHeight: 60, background: '#fff' }}
                  />
                </div>
              </ResizablePanel>
            </div>

            {/* STRUCTURE */}
            <div className="flex flex-col gap-6">
              {/* Pattern */}
              <ResizablePanel minHeight={120} defaultHeight={120}>
                <div className="flex flex-col h-full">
                  <label className="font-semibold mb-2">{worksheetSections[5].label}</label>
                  <textarea
                    className="flex-1 w-full border rounded p-2 resize-none"
                    placeholder={worksheetSections[5].placeholder}
                    value={worksheetData[worksheetSections[5].key]}
                    onChange={e => handleWorksheetChange(worksheetSections[5].key, e.target.value)}
                    style={{ minHeight: 60, background: '#fff' }}
                  />
                </div>
              </ResizablePanel>
              {/* Plan */}
              <ResizablePanel minHeight={120} defaultHeight={120}>
                <div className="flex flex-col h-full">
                  <label className="font-semibold mb-2">{worksheetSections[6].label}</label>
                  <textarea
                    className="flex-1 w-full border rounded p-2 resize-none"
                    placeholder={worksheetSections[6].placeholder}
                    value={worksheetData[worksheetSections[6].key]}
                    onChange={e => handleWorksheetChange(worksheetSections[6].key, e.target.value)}
                    style={{ minHeight: 60, background: '#fff' }}
                  />
                </div>
              </ResizablePanel>
            </div>

            {/* EXECUTION */}
            <div className="flex flex-col gap-6">
              {/* Scene Execution */}
              <ResizablePanel minHeight={120} defaultHeight={120}>
                <div className="flex flex-col h-full">
                  <label className="font-semibold mb-2">{worksheetSections[7].label}</label>
                  <textarea
                    className="flex-1 w-full border rounded p-2 resize-none"
                    placeholder={worksheetSections[7].placeholder}
                    value={worksheetData[worksheetSections[7].key]}
                    onChange={e => handleWorksheetChange(worksheetSections[7].key, e.target.value)}
                    style={{ minHeight: 60, background: '#fff' }}
                  />
                </div>
              </ResizablePanel>
              {/* Final Validation */}
              <ResizablePanel minHeight={120} defaultHeight={120}>
                <div className="flex flex-col h-full">
                  <label className="font-semibold mb-2">{worksheetSections[8].label}</label>
                  <textarea
                    className="flex-1 w-full border rounded p-2 resize-none"
                    placeholder={worksheetSections[8].placeholder}
                    value={worksheetData[worksheetSections[8].key]}
                    onChange={e => handleWorksheetChange(worksheetSections[8].key, e.target.value)}
                    style={{ minHeight: 60, background: '#fff' }}
                  />
                </div>
              </ResizablePanel>
            </div>
          </div>

          {/* Footer note */}
          <div className="flex justify-center mt-8">
            <span className="italic text-gray-600">All boxes ultimately serve the central theme question.</span>
          </div>

          {/* Checklist at the bottom */}
          <div className="flex flex-col items-center mt-8">
            <div className="font-semibold mb-2">Checklist</div>
            <div className="grid grid-cols-4 gap-6 mb-2">
              <div className="flex items-center gap-2">
                <input type="checkbox" checked={checklist.foundation} onChange={() => handleChecklistChange('foundation')} />
                <span>🟦 Foundation</span>
              </div>
              <div className="flex items-center gap-2">
                <input type="checkbox" checked={checklist.trap} onChange={() => handleChecklistChange('trap')} />
                <span>🟥 The Trap</span>
              </div>
              <div className="flex items-center gap-2">
                <input type="checkbox" checked={checklist.structure} onChange={() => handleChecklistChange('structure')} />
                <span>🟣 Structure</span>
              </div>
              <div className="flex items-center gap-2">
                <input type="checkbox" checked={checklist.execution} onChange={() => handleChecklistChange('execution')} />
                <span>🟨 Execution</span>
              </div>
            </div>
            <div className="flex items-center gap-2 mt-2">
              <input type="checkbox" checked={checklist.convergence} onChange={() => handleChecklistChange('convergence')} />
              <span className="italic text-gray-600">All boxes ultimately serve the central theme question</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};