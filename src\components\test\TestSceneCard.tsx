import React, { useState } from 'react';
import { Grip, CheckSquare, Square, ChevronDown, ChevronUp, Palette, X } from 'lucide-react';
import { Button } from '../ui/button';
import { cn } from '../../lib/utils';

export const TestSceneCard: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [showColorPicker, setShowColorPicker] = useState(false);

  const collapsedHeight = 120;
  const expandedHeight = 350;
  const cardHeight = isExpanded ? `${expandedHeight}px` : `${collapsedHeight}px`;

  const sampleTitle = 'Sample Scene Title';
  const sampleContent = `EXT. COFFEE SHOP - DAY\nRemy pulls up to a local coffee shop and parks out front. Checking her watch she gets a text.\n\nShe judges and theorizes the internet detectives have, utterly unaware that she's in the epicenter of criminal activity in FLORIDA.\n\nRemy sips her coffee, lost in thought.`;

  return (
    <div
      className={cn(
        'scene-card group relative bg-white',
        'transition-all duration-200',
        'flex flex-col',
        isComplete && 'bg-green-50',
        'border-2 border-black/20',
        'w-[340px] mx-auto',
      )}
      style={{
        height: cardHeight,
        minHeight: cardHeight,
        maxHeight: cardHeight,
        boxSizing: 'border-box',
      }}
    >
      <div className="flex flex-col p-2 h-full">
        {/* Title row - flush top left */}
        <div className="w-full mb-1 mt-0.5">
          <span className={cn(
            'font-mono text-xs font-bold truncate block w-full',
            isComplete && 'text-green-700'
          )} title={sampleTitle}>
            {sampleTitle}
          </span>
        </div>
        {/* Controls row */}
        <div className="flex items-center gap-1 mb-1 w-full justify-between">
          <Grip className="h-3 w-3 text-gray-400 flex-shrink-0 cursor-move" />
          <div className="flex items-center gap-1 ml-auto">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsComplete(!isComplete)}
              className="h-5 w-5 p-0.5 text-gray-400 hover:text-gray-600"
              title={isComplete ? 'Mark as incomplete' : 'Mark as complete'}
            >
              {isComplete ? (
                <CheckSquare className="h-3 w-3 text-green-600" />
              ) : (
                <Square className="h-3 w-3" />
              )}
            </Button>
            {/* Color Picker Button */}
            <div className="relative">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowColorPicker(!showColorPicker)}
                className={cn(
                  'h-5 w-5 p-0.5',
                  'opacity-70 hover:opacity-100 transition-opacity',
                  'hover:bg-gray-100 rounded-full',
                  'border border-gray-200'
                )}
                title="Assign to Storyline"
              >
                <Palette className="h-3 w-3" />
              </Button>
              {showColorPicker && (
                <div className="absolute right-0 top-full mt-1 bg-white border-2 border-black rounded shadow-lg z-50 py-1 min-w-[150px]">
                  <div className="px-2 py-1 text-xs">Color picker here</div>
                </div>
              )}
            </div>
            {/* Expand/Collapse Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className={cn(
                'h-5 w-5 p-0.5',
                'opacity-70 hover:opacity-100 transition-opacity',
                'hover:bg-gray-100 rounded-full border border-gray-200'
              )}
              title={isExpanded ? 'Show Less' : 'Show More'}
            >
              {isExpanded ? (
                <ChevronUp className="h-3 w-3" />
              ) : (
                <ChevronDown className="h-3 w-3" />
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {}}
              className="opacity-0 group-hover:opacity-100 transition-opacity h-5 w-5 p-0.5"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>
        {/* Content */}
        <div className="flex-1 min-h-0 w-full">
          <div
            className={cn(
              'scene-content font-mono text-xs transition-all duration-200 w-full overflow-y-auto',
              !isExpanded && 'line-clamp-3',
              isComplete && 'text-green-700'
            )}
            style={{height: '100%'}}
          >
            <div className="scenecontent w-full break-words">{sampleContent}</div>
          </div>
        </div>
      </div>
    </div>
  );
}; 