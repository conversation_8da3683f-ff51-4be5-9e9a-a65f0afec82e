import { logger } from './logger';

/**
 * Creates a template structure with empty scenes array but preserves the structure
 * @param templateStructure The original template structure
 * @returns A template with empty scenes arrays
 */
export function createEmptyTemplate(templateStructure: any[]): any[] {
  return templateStructure.map(section => ({
    ...section,
    scenes: [] // Empty scenes array
  }));
}

/**
 * Clears all scene data for a specific project from localStorage
 * @param projectId The ID of the project to clear scene data for
 * @returns The number of items cleared
 */
export function clearProjectSceneData(projectId: string): number {
  if (!projectId) {
    logger.warn('No project ID provided to clearProjectSceneData');
    return 0;
  }
  
  try {
    // Use the findAllSceneKeys utility to get all scene-related keys for this project
    const keysToRemove = findAllSceneKeys(projectId);
    
    if (keysToRemove.length === 0) {
      logger.warn(`No scene data found for project ${projectId}`);
      return 0;
    }
    
    // Remove all matching keys
    keysToRemove.forEach(key => localStorage.removeItem(key));
    
    logger.debug(`Cleared ${keysToRemove.length} scene-related items for project ${projectId}`);
    return keysToRemove.length;
  } catch (error) {
    logger.error('Failed to clear project scene data:', error);
    return 0;
  }
}

/**
 * Creates a blank template structure without any pre-populated scenes
 * @param templateStructure The base template structure to use
 * @returns A copy of the template with empty scenes arrays
 */
export function createBlankTemplate(templateStructure: any[]): any[] {
  return templateStructure.map(section => ({
    ...section,
    scenes: [] // Ensure scenes array is empty
  }));
}

/**
 * Initializes a blank episode with the given template structure
 * @param episodeId The episode ID to initialize
 * @param templateStructure The template structure to use
 * @returns The episode data object with empty scenes
 */
export function initializeBlankEpisode(episodeId: number, templateStructure: any[]): any {
  return {
    [episodeId]: {
      acts: createBlankTemplate(templateStructure)
    }
  };
}

/**
 * Saves blank episode data to localStorage
 * @param projectId The project ID
 * @param episodeData The episode data to save
 * @returns True if successful, false otherwise
 */
export function saveBlankEpisodeData(projectId: string, episodeData: any): boolean {
  if (!projectId) {
    logger.warn('No project ID provided to saveBlankEpisodeData');
    return false;
  }
  
  try {
    const key = `project_${projectId}_episode_data`;
    localStorage.setItem(key, JSON.stringify(episodeData));
    logger.debug(`Saved blank episode data for project ${projectId}`);
    return true;
  } catch (error) {
    logger.error('Failed to save blank episode data:', error);
    return false;
  }
}