import React, { useState, useRef, useEffect } from 'react';
import { Grip, X, ChevronDown, ChevronUp, Palette, CheckSquare, Square, AlertCircle, Redo } from 'lucide-react';
import { But<PERSON> } from '../../ui/button';
import { cn } from '../../../lib/utils';
import { useEpisodeTrackStore } from '../../../store/episodeTrackStore';
import { safelySetJsonForDrag } from '../../../utils/dragUtils';
import type { Scene, RevisionState } from '../../../types/write';

// Define mapping for revision state styles
const revisionStateStyles: Record<RevisionState, { icon: React.ReactNode, label: string, color: string }> = {
  'complete': { 
    icon: <CheckSquare className="h-3 w-3 text-green-600" />, 
    label: 'Complete', 
    color: 'bg-green-50 border-green-600'
  },
  'in-revision': { 
    icon: <Redo className="h-3 w-3 text-orange-600" />, 
    label: 'In Revision', 
    color: 'bg-orange-50 border-orange-600'
  },
  'needs-review': { 
    icon: <AlertCircle className="h-3 w-3 text-red-600" />, 
    label: 'Needs Review', 
    color: 'bg-red-50 border-red-600'
  },
  'not-started': { 
    icon: <Square className="h-3 w-3" />, 
    label: 'Not Started', 
    color: 'border-black/20'
  }
};

interface SceneCardProps {
  scene: Scene;
  index: number;
  groupId: string;
  color: string;
  storylineId?: string | null;
  onUpdate: (updates: Partial<Scene>) => void;
  onDelete: () => void;
  onDragStart: (e: React.DragEvent, scene: Scene, index: number) => void;
  onDragOver: (e: React.DragEvent, index: number) => void;
  onDrop: (e: React.DragEvent, index: number) => void;
  onStorylineChange?: (storylineId: string | null) => void;
  isEpisodeExpanded?: boolean;
}

export const SceneCard: React.FC<SceneCardProps> = ({
  scene,
  index,
  groupId,
  color,
  storylineId,
  onUpdate,
  onDelete,
  onDragStart,
  onDragOver,
  onDrop,
  onStorylineChange,
  isEpisodeExpanded = false
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(scene.content);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [revisionState, setRevisionState] = useState<RevisionState>('not-started');
  const [showRevisionMenu, setShowRevisionMenu] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Get storylines from the store
  const storylines = useEpisodeTrackStore(state => state.storylines);

  // Get the storyline color if it exists
  const storylineColor = storylineId ? 
    storylines.find(s => s.id === storylineId)?.color : 
    null;

  // Calculate if content needs truncation
  const needsTruncation = scene.content.split('\n').length > 3 || scene.content.length > 120;

  // Check if scene has write data and revision status
  useEffect(() => {
    try {
      // Create a unique ID for the scene that includes episode and act info
      const sceneId = `${scene.episodeId}-${scene.actId}-${scene.id}`;
      const savedData = localStorage.getItem(`write_scene_${sceneId}`);
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        
        // Handle both legacy isComplete and new revisionState
        if (parsedData.revisionState) {
          setRevisionState(parsedData.revisionState);
        } else if (parsedData.isComplete) {
          setRevisionState('complete');
        } else {
          setRevisionState('not-started');
        }
        
        // Also update the content if it's different
        if (parsedData.content !== scene.content) {
          onUpdate({ content: parsedData.content });
        }
      }
    } catch (error) {
      console.error('Failed to check scene revision status:', error);
    }
  }, [scene, onUpdate]);

  // Listen for changes to the scene content from the write module
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent | CustomEvent) => {
      // Handle both StorageEvent and CustomEvent
      const isStorageEvent = e instanceof StorageEvent;
      const key = isStorageEvent ? e.key : (e as any).detail?.key;
      const newValue = isStorageEvent ? e.newValue : (e as any).detail?.newValue;
      
      if (!key || !newValue) return;
      
      // Check if this is a write_scene event for this scene
      const sceneId = `${scene.episodeId}-${scene.actId}-${scene.id}`;
      if (key === `write_scene_${sceneId}`) {
        try {
          const data = JSON.parse(newValue);
          if (data.content !== scene.content) {
            setEditContent(data.content);
            onUpdate({ content: data.content });
          }
          
          if (data.revisionState) {
            setRevisionState(data.revisionState);
          } else if (data.isComplete) {
            setRevisionState('complete');
          } else {
            setRevisionState('not-started');
          }
        } catch (error) {
          console.error('Failed to process scene update:', error);
        }
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('sceneContentChanged', handleStorageChange as EventListener);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('sceneContentChanged', handleStorageChange as EventListener);
    };
  }, [scene, onUpdate]);

  // Reset expansion state when episode expands/collapses
  useEffect(() => {
    if (!isEpisodeExpanded && isExpanded) {
      setIsExpanded(false);
    }
  }, [isEpisodeExpanded]);

  // Handle content height synchronization
  useEffect(() => {
    if (!isExpanded || !contentRef.current) return;

    const cards = document.querySelectorAll(`[data-group-id="${groupId}"]`);
    let maxHeight = 0;

    cards.forEach(card => {
      const content = card.querySelector('.scene-content');
      if (content) {
        const height = content.scrollHeight;
        maxHeight = Math.max(maxHeight, height);
      }
    });

    cards.forEach(card => {
      const content = card.querySelector('.scene-content');
      if (content && card.getAttribute('data-expanded') === 'true') {
        (content as HTMLElement).style.height = `${maxHeight}px`;
      }
    });
  }, [isExpanded, groupId, scene.content]);

  useEffect(() => {
    if (!isExpanded && contentRef.current) {
      contentRef.current.style.height = '';
    }
  }, [isExpanded]);

  const handleSave = () => {
    if (editContent.trim() !== scene.content) {
      onUpdate({ content: editContent.trim() });
      
      // Also update the write module data if it exists
      try {
        const sceneId = `${scene.episodeId}-${scene.actId}-${scene.id}`;
        const savedData = localStorage.getItem(`write_scene_${sceneId}`);
        if (savedData) {
          const parsedData = JSON.parse(savedData);
          parsedData.content = editContent.trim();
          parsedData.lastEdited = new Date().toISOString();
          localStorage.setItem(`write_scene_${sceneId}`, JSON.stringify(parsedData));
          
          // Dispatch a custom event to notify other components
          window.dispatchEvent(new CustomEvent('sceneContentChanged', {
            detail: {
              key: `write_scene_${sceneId}`,
              newValue: JSON.stringify(parsedData)
            }
          }));
        }
      } catch (error) {
        console.error('Failed to update write module data:', error);
      }
    }
    setIsEditing(false);
  };

  const handleDragStart = (e: React.DragEvent) => {
    if (isEditing) {
      e.preventDefault();
      return;
    }

    setIsDragging(true);
    e.dataTransfer.effectAllowed = 'move';
    
    if (cardRef.current) {
      const rect = cardRef.current.getBoundingClientRect();
      e.dataTransfer.setDragImage(cardRef.current, rect.width / 2, rect.height / 2);
    }
    
    safelySetJsonForDrag(e, {
      type: 'scene',
      id: scene.id,
      index,
      storylineId
    });
    
    onDragStart(e, scene, index);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const toggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isEpisodeExpanded) {
      setIsExpanded(!isExpanded);
      if (cardRef.current) {
        cardRef.current.setAttribute('data-expanded', (!isExpanded).toString());
      }
    }
  };

  const handleStorylineSelect = (newStorylineId: string | null) => {
    onStorylineChange?.(newStorylineId);
    setShowColorPicker(false);
  };

  const updateRevisionState = (newState: RevisionState) => {
    setRevisionState(newState);
    setShowRevisionMenu(false);
    
    // Update revision status in write module data
    try {
      const sceneId = `${scene.episodeId}-${scene.actId}-${scene.id}`;
      const savedData = localStorage.getItem(`write_scene_${sceneId}`);
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        parsedData.revisionState = newState;
        
        // For backward compatibility
        parsedData.isComplete = newState === 'complete';
        
        parsedData.lastEdited = new Date().toISOString();
        localStorage.setItem(`write_scene_${sceneId}`, JSON.stringify(parsedData));
        
        // Dispatch a custom event to notify other components
        window.dispatchEvent(new CustomEvent('sceneContentChanged', {
          detail: {
            key: `write_scene_${sceneId}`,
            newValue: JSON.stringify(parsedData)
          }
        }));
      } else {
        // If no write data exists yet, create it
        const newData = {
          id: sceneId,
          title: scene.title,
          content: scene.content,
          notes: [],
          characters: [],
          emotionalStart: 'Neutral',
          emotionalEnd: 'Neutral',
          emotionalProgress: 50,
          isPositiveArc: true,
          beats: [],
          revisionState: newState,
          isComplete: newState === 'complete',
          lastEdited: new Date().toISOString()
        };
        localStorage.setItem(`write_scene_${sceneId}`, JSON.stringify(newData));
        
        // Dispatch a custom event to notify other components
        window.dispatchEvent(new CustomEvent('sceneContentChanged', {
          detail: {
            key: `write_scene_${sceneId}`,
            newValue: JSON.stringify(newData)
          }
        }));
      }
    } catch (error) {
      console.error('Failed to update scene revision status:', error);
    }
  };

  const collapsedHeight = 120; // px, for collapsed cards
  const expandedHeight = 350; // px, for expanded cards
  const cardHeight = isExpanded ? `${expandedHeight}px` : `${collapsedHeight}px`;

  // Get current revision state configuration
  const stateConfig = revisionStateStyles[revisionState];

  return (
    <div 
      ref={cardRef}
      draggable={!isEditing}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragOver={(e) => onDragOver(e, index)}
      onDrop={(e) => onDrop(e, index)}
      className={cn(
        "scene-card group relative bg-white",
        "hover:shadow-sm transition-all duration-200",
        !isEditing && "cursor-move",
        isDragging && "opacity-50",
        storylineId ? "border-[3px]" : "border-2 hover:border-black",
        stateConfig.color
      )}
      style={{ 
        borderColor: storylineColor || color,
        height: cardHeight,
        minHeight: cardHeight,
        maxHeight: cardHeight
      }}
      data-scene-id={scene.id}
      data-index={index}
      data-group-id={groupId}
      data-expanded={isExpanded}
      data-revision-state={revisionState}
    >
      <div className="flex flex-col p-2 h-full">
        {/* Title row - full width */}
        <div className="w-full mb-1">
          <span className={cn(
            "font-mono text-xs font-bold truncate block w-full",
            revisionState === 'complete' && "text-green-700",
            revisionState === 'in-revision' && "text-orange-700",
            revisionState === 'needs-review' && "text-red-700"
          )} title={scene.title}>
            {scene.title || `Scene ${index + 1}`}
          </span>
        </div>
        {/* Controls row */}
        <div className="flex items-center gap-1 mb-1 w-full justify-between">
          <Grip className="h-3 w-3 text-gray-400 flex-shrink-0 cursor-move" />
          <div className="flex items-center gap-1 ml-auto">
            {/* Revision State Button */}
            <div className="relative">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowRevisionMenu(!showRevisionMenu);
                }}
                className="h-5 w-5 p-0.5 text-gray-400 hover:text-gray-600"
                title={stateConfig.label}
              >
                {stateConfig.icon}
              </Button>
              
              {/* Revision State Dropdown */}
              {showRevisionMenu && (
                <div className="absolute right-0 top-full mt-1 bg-white border-2 border-black rounded shadow-lg z-50 py-1 min-w-[150px]">
                  {Object.entries(revisionStateStyles).map(([state, config]) => (
                    <button
                      key={state}
                      onClick={(e) => {
                        e.stopPropagation();
                        updateRevisionState(state as RevisionState);
                      }}
                      className={cn(
                        "w-full px-2 py-1 text-left text-xs flex items-center gap-2 hover:bg-gray-50",
                        revisionState === state && "bg-gray-100"
                      )}
                    >
                      <span className="flex items-center gap-1">
                        {config.icon}
                        <span>{config.label}</span>
                      </span>
                    </button>
                  ))}
                </div>
              )}
            </div>
            
            {/* Color Picker Button */}
            <div className="relative">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowColorPicker(!showColorPicker);
                }}
                className={cn(
                  "h-5 w-5 p-0.5",
                  "opacity-0 group-hover:opacity-100 transition-opacity",
                  "hover:bg-gray-100 rounded-full",
                  "border border-gray-200"
                )}
                title="Assign to Storyline"
              >
                <Palette className="h-3 w-3" />
              </Button>
              {/* Color Picker Dropdown */}
              {showColorPicker && (
                <div className="absolute right-0 top-full mt-1 bg-white border-2 border-black rounded shadow-lg z-50 py-1 min-w-[150px]">
                  {storylines.map(storyline => (
                    <button
                      key={storyline.id}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleStorylineSelect(storyline.id);
                      }}
                      className={cn(
                        "w-full px-2 py-1 text-left text-xs flex items-center gap-2 hover:bg-gray-50",
                        storylineId === storyline.id && "bg-gray-100"
                      )}
                    >
                      <div 
                        className="w-3 h-3 rounded-full flex-shrink-0"
                        style={{ backgroundColor: storyline.color }}
                      />
                      <span className="truncate">{storyline.title}</span>
                    </button>
                  ))}
                  {storylineId && (
                    <>
                      <hr className="my-1 border-gray-200" />
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleStorylineSelect(null);
                        }}
                        className="w-full px-2 py-1 text-left text-xs text-gray-600 hover:bg-gray-50"
                      >
                        Remove storyline
                      </button>
                    </>
                  )}
                </div>
              )}
            </div>
            {/* Expand/Collapse Button - always visible but subtle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleExpand}
              className={cn(
                "h-5 w-5 p-0.5",
                "opacity-70 hover:opacity-100 transition-opacity",
                "hover:bg-gray-100 rounded-full border border-gray-200"
              )}
              title={isExpanded ? "Show Less" : "Show More"}
            >
              {isExpanded ? (
                <ChevronUp className="h-3 w-3" />
              ) : (
                <ChevronDown className="h-3 w-3" />
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
              className="opacity-0 group-hover:opacity-100 transition-opacity h-5 w-5 p-0.5"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>
        {/* Content */}
        <div className="flex-1 min-h-0 w-full">
          <div
            ref={contentRef}
            onClick={() => setIsEditing(true)}
            className={cn(
              "scene-content font-mono text-xs cursor-text transition-all duration-200 w-full overflow-y-auto",
              !isExpanded && !isEpisodeExpanded && "line-clamp-3",
              revisionState === 'complete' && "text-green-700",
              revisionState === 'in-revision' && "text-orange-700",
              revisionState === 'needs-review' && "text-red-700"
            )}
            style={{height: '100%'}}
          >
            {!scene.content ? (
              <span className="text-gray-400 select-none">Empty scene...</span>
            ) : (
              <div className="scenecontent w-full break-words">{scene.content}</div>
            )}
          </div>
        </div>
        
        {/* Revision status indicator */}
        {revisionState !== 'not-started' && (
          <div className={cn(
            "absolute top-2 right-2 px-1.5 py-0.5 rounded-sm text-[9px] font-medium",
            revisionState === 'complete' && "bg-green-100 text-green-800",
            revisionState === 'in-revision' && "bg-orange-100 text-orange-800",
            revisionState === 'needs-review' && "bg-red-100 text-red-800"
          )}>
            {stateConfig.label}
          </div>
        )}
      </div>
    </div>
  );
};