import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { App } from './App';
import './index.css';
import { ProjectProvider } from './contexts/ProjectContext';
import { ChatProvider } from './contexts/ChatContext';

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ProjectProvider>
      <ChatProvider>
        <App />
      </ChatProvider>
    </ProjectProvider>
  </StrictMode>
);