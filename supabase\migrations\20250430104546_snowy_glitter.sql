/*
  # Tasks Module Schema

  1. New Tables
    - `task_columns` - Columns for the Kanban board (e.g., To Do, In Progress, Done)
    - `tasks` - Individual tasks within columns
    - `task_comments` - Comments on tasks
    - `task_labels` - Labels that can be applied to tasks

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
    - Ensure proper cascading deletes
*/

-- Task columns (Kanban board columns)
CREATE TABLE IF NOT EXISTS task_columns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  color TEXT NOT NULL,
  position INTEGER NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Tasks
CREATE TABLE IF NOT EXISTS tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  column_id UUID NOT NULL REFERENCES task_columns(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  content TEXT,
  position INTEGER NOT NULL,
  labels TEXT[] DEFAULT '{}',
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Task comments
CREATE TABLE IF NOT EXISTS task_comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  content TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Task labels
CREATE TABLE IF NOT EXISTS task_labels (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  color TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE(project_id, name)
);

-- Add indexes
CREATE INDEX IF NOT EXISTS task_columns_project_id_idx ON task_columns(project_id);
CREATE INDEX IF NOT EXISTS task_columns_position_idx ON task_columns(position);
CREATE INDEX IF NOT EXISTS tasks_column_id_idx ON tasks(column_id);
CREATE INDEX IF NOT EXISTS tasks_position_idx ON tasks(position);
CREATE INDEX IF NOT EXISTS task_comments_task_id_idx ON task_comments(task_id);
CREATE INDEX IF NOT EXISTS task_labels_project_id_idx ON task_labels(project_id);

-- Enable RLS
ALTER TABLE task_columns ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_labels ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can access task columns in their projects"
  ON task_columns
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM projects
    WHERE projects.id = task_columns.project_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY "Users can access tasks in their projects"
  ON tasks
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM task_columns
    JOIN projects ON projects.id = task_columns.project_id
    WHERE task_columns.id = tasks.column_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY "Users can access task comments in their projects"
  ON task_comments
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM tasks
    JOIN task_columns ON task_columns.id = tasks.column_id
    JOIN projects ON projects.id = task_columns.project_id
    WHERE tasks.id = task_comments.task_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY "Users can access task labels in their projects"
  ON task_labels
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM projects
    WHERE projects.id = task_labels.project_id
    AND projects.user_id = auth.uid()
  ));

-- Add update timestamp triggers
CREATE TRIGGER task_columns_updated_at
  BEFORE UPDATE ON task_columns
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER tasks_updated_at
  BEFORE UPDATE ON tasks
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

-- Insert default task columns for new projects
CREATE OR REPLACE FUNCTION create_default_task_columns()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO task_columns (project_id, title, color, position)
  VALUES 
    (NEW.id, 'INBOX', 'bg-gray-500', 1),
    (NEW.id, 'IDEATION', 'bg-red-500', 2),
    (NEW.id, 'REFINE', 'bg-blue-500', 3),
    (NEW.id, 'SYSTEMIZATION', 'bg-yellow-500', 4),
    (NEW.id, 'IMPLEMENTATION', 'bg-green-500', 5),
    (NEW.id, 'ARCHIVE', 'bg-gray-700', 6);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER create_task_columns_for_new_project
  AFTER INSERT ON projects
  FOR EACH ROW
  EXECUTE FUNCTION create_default_task_columns();