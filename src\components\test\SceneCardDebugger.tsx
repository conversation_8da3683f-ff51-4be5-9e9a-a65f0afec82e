import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '../ui/button';
import { Card, CardContent, CardHeader } from '../ui/card';
import { SceneCard } from '../episodes/SceneCard';
import { StorylineColorPicker } from '../episodes/StorylineColorPicker';
import { useEpisodeTrackStore } from '../../store/episodeTrackStore';
import { RefreshCw, Bug, Plus } from 'lucide-react';

export const SceneCardDebugger: React.FC = () => {
  const [scene, setScene] = useState({
    id: Date.now(),
    title: 'Test Scene',
    content: 'This is a test scene to debug the scene card component.',
    actId: 'test-act',
    episodeId: 1,
    storylineId: null as string | null
  });
  
  const [logs, setLogs] = useState<string[]>([]);
  const storylines = useEpisodeTrackStore(state => state.storylines);
  
  // Add a log entry
  const addLog = (message: string) => {
    setLogs(prev => [message, ...prev].slice(0, 20));
  };
  
  // Create a test storyline if none exist
  useEffect(() => {
    if (storylines.length === 0) {
      const addStoryline = useEpisodeTrackStore.getState().addStoryline;
      addStoryline('Test Storyline');
      addLog('Created test storyline');
    }
  }, [storylines.length]);
  
  // Handle storyline change
  const handleStorylineChange = (storylineId: string | null) => {
    addLog(`Storyline changed to: ${storylineId || 'null'}`);
    setScene(prev => ({
      ...prev,
      storylineId
    }));
  };
  
  // Create a new test scene
  const createNewScene = () => {
    setScene({
      id: Date.now(),
      title: `Test Scene ${Date.now().toString().slice(-4)}`,
      content: 'This is a test scene to debug the scene card component.',
      actId: 'test-act',
      episodeId: 1,
      storylineId: null
    });
    addLog('Created new test scene');
  };
  
  // Reset storyline
  const resetStoryline = () => {
    setScene(prev => ({
      ...prev,
      storylineId: null
    }));
    addLog('Reset storyline to null');
  };
  
  // Mock handlers for the SceneCard component
  const mockHandlers = {
    onUpdate: () => {},
    onDelete: () => {},
    onDragStart: () => {},
    onDragOver: () => {},
    onDrop: () => {}
  };
  
  return (
    <Card className="border-4 border-black">
      <CardHeader className="border-b-4 border-black bg-purple-50">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Bug className="h-5 w-5 text-purple-600" />
            <h2 className="text-xl font-bold">Scene Card Debugger</h2>
          </div>
          <div className="flex gap-2">
            <Button 
              variant="ghost" 
              onClick={createNewScene}
              className="border-2 border-black"
            >
              <Plus className="h-4 w-4 mr-2" />
              New Scene
            </Button>
            <Button 
              variant="ghost" 
              onClick={resetStoryline}
              className="border-2 border-black"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Reset Storyline
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <div className="space-y-6">
          {/* Scene Card Test */}
          <div className="border-2 border-black p-4 rounded">
            <h3 className="font-bold mb-4">Test Scene Card</h3>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="w-full md:w-1/2">
                <SceneCard
                  scene={scene}
                  index={0}
                  groupId="test-group"
                  color="#000000"
                  storylineId={scene.storylineId}
                  onUpdate={() => {}}
                  onDelete={() => {}}
                  onDragStart={() => {}}
                  onDragOver={() => {}}
                  onDrop={() => {}}
                  onStorylineChange={handleStorylineChange}
                  debug={true}
                />
              </div>
              <div className="w-full md:w-1/2">
                <div className="border-2 border-black p-4 rounded h-full">
                  <h4 className="font-bold mb-2">Scene Properties</h4>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-medium">ID:</span> {scene.id}</p>
                    <p><span className="font-medium">Title:</span> {scene.title}</p>
                    <p><span className="font-medium">Storyline ID:</span> {scene.storylineId || 'null'}</p>
                    <p><span className="font-medium">Content Length:</span> {scene.content.length} chars</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Standalone Color Picker Test */}
          <div className="border-2 border-black p-4 rounded">
            <h3 className="font-bold mb-4">Standalone Storyline Picker</h3>
            <div className="flex items-center gap-4">
              <div className="relative p-8 border-2 border-dashed border-gray-300">
                <StorylineColorPicker
                  currentStorylineId={scene.storylineId}
                  onStorylineChange={handleStorylineChange}
                  buttonSize="md"
                  showText={true}
                  debug={true}
                />
              </div>
              <div className="flex-1">
                <h4 className="font-bold mb-2">Available Storylines</h4>
                <div className="space-y-2">
                  {storylines.length === 0 ? (
                    <p className="text-gray-500 italic">No storylines available</p>
                  ) : (
                    storylines.map(storyline => (
                      <div key={storyline.id} className="flex items-center gap-2">
                        <div 
                          className="w-4 h-4 rounded-full" 
                          style={{ backgroundColor: storyline.color }}
                        ></div>
                        <span>{storyline.title}</span>
                        <span className="text-xs text-gray-500">({storyline.id})</span>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
          
          {/* Debug Logs */}
          <div className="border-2 border-black p-4 rounded">
            <h3 className="font-bold mb-2">Debug Logs</h3>
            <div className="bg-gray-900 text-green-400 p-2 font-mono text-xs h-40 overflow-y-auto">
              {logs.length === 0 ? (
                <p className="text-gray-500">No logs yet</p>
              ) : (
                logs.map((log, index) => (
                  <div key={index} className="mb-1">
                    <span className="text-gray-500">[{new Date().toLocaleTimeString()}]</span> {log}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};