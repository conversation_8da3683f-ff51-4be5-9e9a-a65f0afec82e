import { createClient } from '@supabase/supabase-js';
import type { Database } from './database.types';
import { logger } from '../../utils/logger';

// Use default values for local development
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'http://localhost:3000';
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'local-development-key';

// Create a Supabase client, or a mock if in development mode
export const supabase = createClient<Database>(supabaseUrl, supabaseKey);

// Mock user for local storage
const LOCAL_STORAGE_USER_KEY = 'app_user';

// Initialize a mock user in localStorage for development
if (import.meta.env.DEV && typeof localStorage !== 'undefined' && !localStorage.getItem(LOCAL_STORAGE_USER_KEY)) {
  const mockUser = {
    id: 'mock-user-id',
    email: '<EMAIL>',
    user_metadata: {
      full_name: 'Mock User'
    }
  };
  localStorage.setItem(LOCAL_STORAGE_USER_KEY, JSON.stringify(mockUser));
  logger.debug('Created mock user for local development');
}

/**
 * Checks if the user is currently authenticated
 * @returns Promise that resolves to true if authenticated, false otherwise
 */
export const checkAuth = async () => {
  try {
    // Use local storage in development mode
    if (import.meta.env.DEV) {
      const user = localStorage.getItem(LOCAL_STORAGE_USER_KEY);
      return !!user;
    }
    
    const { data: { session } } = await supabase.auth.getSession();
    return !!session;
  } catch (error) {
    logger.error('Auth check failed:', error);
    return false;
  }
};

/**
 * Gets the current user, throws an error if not authenticated
 * @returns The current user
 */
export const getAuthUser = async () => {
  // Use local storage in development mode
  if (import.meta.env.DEV) {
    const userJson = localStorage.getItem(LOCAL_STORAGE_USER_KEY);
    if (!userJson) {
      throw new Error('User not authenticated');
    }
    try {
      const user = JSON.parse(userJson);
      return user;
    } catch (error) {
      logger.error('Failed to parse user from local storage:', error);
      throw new Error('Authentication error');
    }
  }
  
  const { data: { user }, error } = await supabase.auth.getUser();
  
  if (error) {
    logger.error('Failed to get user:', error);
    throw new Error('Authentication error');
  }
  
  if (!user) {
    throw new Error('User not authenticated');
  }
  
  return user;
};

/**
 * Middleware to require authentication for API calls
 * @param callback The function to execute if authenticated
 * @returns The result of the callback
 */
export const withAuth = async <T>(callback: () => Promise<T>): Promise<T> => {
  const isAuthenticated = await checkAuth();
  if (!isAuthenticated) {
    throw new Error('User not authenticated');
  }
  return callback();
};