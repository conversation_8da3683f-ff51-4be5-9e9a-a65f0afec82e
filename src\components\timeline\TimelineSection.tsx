import React, { useRef, useEffect } from 'react';
import { ChevronDown, ChevronRight, Plus } from 'lucide-react';
import { Button } from '../ui/button';
import { TimelineGrid } from './TimelineGrid';
import { ColorLegend } from './ColorLegend';
import { useEpisodeTrackStore } from '../../store/episodeTrackStore';
import { cn } from '../../lib/utils';

export const TimelineSection: React.FC = () => {
  const { 
    episodeTrack,
    colorLegend,
    addStoryline, 
    toggleEpisodeTrackExpanded,
    setHeight,
    storylines
  } = useEpisodeTrackStore();
  
  const contentRef = useRef<HTMLDivElement>(null);

  // Dynamically adjust height based on number of storylines
  useEffect(() => {
    if (!episodeTrack.isExpanded && contentRef.current) {
      const updateHeight = () => {
        // Base height calculation
        const baseHeight = 60; // Header + padding
        const storylineHeight = 48; // Height per storyline row
        const minHeight = 300; // Minimum height
        
        // Calculate height based on number of storylines (plus 1 for header)
        const contentHeight = baseHeight + (storylines.length + 1) * storylineHeight;
        
        // Use minimum height if calculated height is smaller
        const newHeight = Math.max(contentHeight, minHeight);
        setHeight(newHeight);
      };

      updateHeight();

      // Set up observer to handle dynamic content changes
      const observer = new ResizeObserver(updateHeight);
      observer.observe(contentRef.current);

      return () => observer.disconnect();
    }
  }, [episodeTrack.isExpanded, setHeight, storylines.length]);

  return (
    <section 
      className={cn(
        "fixed top-16 left-0 right-0 z-50 bg-white border-b-4 border-black",
        "transition-all duration-300 ease-in-out",
        colorLegend.isVisible && "content-with-legend"
      )}
      style={{ minHeight: 'unset', background: '#fff', ...((storylines.length < 3) ? {height: episodeTrack.isExpanded ? 'auto' : '320px'} : {}) }}
    >
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white flex items-center justify-between px-4 py-2 border-b-4 border-black marginmove">
        <div className="flex items-center gap-2 movedown">
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleEpisodeTrackExpanded}
            className="hover:bg-gray-100"
          >
            {episodeTrack.isExpanded ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
          <h2 className="font-bold">Episode Track</h2>
        </div>

        {!episodeTrack.isExpanded && (
          <Button 
            variant="ghost" 
            onClick={() => addStoryline('New Storyline')}
            className="hover:bg-gray-100 border-2 border-black movebutton"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Storyline
          </Button>
        )}
      </div>

      {/* Scrollable Content Container */}
      {!episodeTrack.isExpanded && (
        <div 
          ref={contentRef}
          className="overflow-y-auto"
          style={{ height: `${storylines.length * 48 + 60}px` }}
        >
          <div className="min-w-max">
            <TimelineGrid />
          </div>
        </div>
      )}

      {/* Color Legend is now rendered independently */}
      <ColorLegend />
    </section>
  );
};