import React, { useState } from 'react';
import { useRevisionStore } from '../../../store/revisionStore';
import { Button } from '../../ui/button';
import { UserPlus, X } from 'lucide-react';

interface CollaboratorComponentProps {
  noteId: string;
  collaborators: string[];
}

export const CollaboratorComponent: React.FC<CollaboratorComponentProps> = ({ 
  noteId, 
  collaborators = [] 
}) => {
  const [email, setEmail] = useState('');
  const [isAdding, setIsAdding] = useState(false);
  const { addCollaborator, removeCollaborator } = useRevisionStore();

  const handleAddCollaborator = () => {
    if (email.trim() && !collaborators.includes(email)) {
      addCollaborator(noteId, email.trim());
      setEmail('');
      setIsAdding(false);
    }
  };

  return (
    <div className="mt-3 border-t border-gray-200 pt-2">
      <div className="flex justify-between items-center mb-1">
        <span className="text-xs font-medium text-gray-500">Collaborators</span>
        {!isAdding && (
          <Button
            variant="outline"
            size="sm" 
            onClick={() => setIsAdding(true)}
            className="text-xs px-2 py-1 h-6 border border-gray-300 hover:bg-gray-50"
          >
            <UserPlus className="h-3 w-3 mr-1" />
            Add collaborator
          </Button>
        )}
      </div>
      
      {collaborators.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-2">
          {collaborators.map(collab => (
            <div 
              key={collab} 
              className="inline-flex items-center px-2 py-1 bg-gray-100 rounded text-xs"
            >
              {collab}
              <button 
                onClick={() => removeCollaborator(noteId, collab)}
                className="ml-1 text-gray-500 hover:text-red-500"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      )}
      
      {isAdding && (
        <div className="flex items-center mt-1">
          <input
            type="email"
            placeholder="Email address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded-l"
          />
          <Button
            size="sm"
            onClick={handleAddCollaborator}
            className="text-xs h-[26px] min-h-0 py-0 rounded-l-none text-white bg-black hover:bg-gray-800"
          >
            Add
          </Button>
        </div>
      )}
      
      {collaborators.length === 0 && !isAdding && (
        <p className="text-xs text-gray-400 italic">No collaborators added yet</p>
      )}
    </div>
  );
}; 