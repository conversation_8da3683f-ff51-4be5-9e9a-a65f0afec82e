import { logger } from './logger';

// Define a rich color palette with visually distinct colors from the theme
export const STORYLINE_COLORS = [
  '#FF3AF2', // Vibrant Pink
  '#00FFF0', // <PERSON>an
  '#FFE600', // Yellow
  '#4DFF4D', // Lime Green
  '#FF4D4D', // Coral Red
  '#4D4DFF', // Royal Blue
  '#FF9B4D', // Orange
  '#B44DFF', // Purple
  '#4DFFB4', // Mint
  '#FF4D9B', // Rose
  '#4DB4FF', // Sky Blue
  '#FFB44D'  // Gold
];

interface ColorState {
  usedColors: Set<string>;
  lastColorIndex: number;
}

const state: ColorState = {
  usedColors: new Set(),
  lastColorIndex: -1
};

/**
 * Gets the next available color from the palette
 * @returns A unique color from the palette
 */
export function getNextColor(): string {
  // If we've used all colors, reset the used colors set
  if (state.usedColors.size >= STORYLINE_COLORS.length) {
    state.usedColors.clear();
    state.lastColorIndex = -1;
    logger.debug('Color palette reset - all colors were used');
  }

  // Find the next unused color
  let nextIndex = (state.lastColorIndex + 1) % STORYLINE_COLORS.length;
  while (state.usedColors.has(STORYLINE_COLORS[nextIndex])) {
    nextIndex = (nextIndex + 1) % STORYLINE_COLORS.length;
  }

  const color = STORYLINE_COLORS[nextIndex];
  state.usedColors.add(color);
  state.lastColorIndex = nextIndex;

  logger.debug('New color assigned:', { color, usedColors: Array.from(state.usedColors) });
  return color;
}

/**
 * Releases a color back to the available pool
 * @param color The color to release
 */
export function releaseColor(color: string): void {
  state.usedColors.delete(color);
  logger.debug('Color released:', { color, remainingUsedColors: Array.from(state.usedColors) });
}

/**
 * Gets a random color from the palette that hasn't been used
 * @returns A random unused color
 */
export function getRandomColor(): string {
  const availableColors = STORYLINE_COLORS.filter(color => !state.usedColors.has(color));
  
  if (availableColors.length === 0) {
    state.usedColors.clear();
    logger.debug('Color palette reset for random selection');
    return getRandomColor();
  }

  const randomIndex = Math.floor(Math.random() * availableColors.length);
  const color = availableColors[randomIndex];
  state.usedColors.add(color);

  logger.debug('Random color assigned:', { color, usedColors: Array.from(state.usedColors) });
  return color;
}

/**
 * Converts a hex color to RGB values
 * @param hex The hex color string
 * @returns RGB values as an object
 */
export function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

/**
 * Determines if a color is light or dark
 * @param hex The hex color string
 * @returns True if the color is light, false if dark
 */
export function isLightColor(hex: string): boolean {
  const rgb = hexToRgb(hex);
  if (!rgb) return true;
  
  // Calculate relative luminance
  const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
  return luminance > 0.5;
}

/**
 * Gets appropriate text color for a background color
 * @param bgColor The background color in hex
 * @returns White for dark backgrounds, black for light backgrounds
 */
export function getTextColorForBackground(bgColor: string): string {
  return isLightColor(bgColor) ? '#000000' : '#FFFFFF';
}