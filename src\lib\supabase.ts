import { createClient } from '@supabase/supabase-js';

// Use default values for local development
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'http://localhost:3000';
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'local-development-key';

// Create a Supabase client with default values if environment variables are not available
export const supabase = createClient(supabaseUrl, supabaseKey);