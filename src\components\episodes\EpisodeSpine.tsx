import React from 'react';
import { Plus } from 'lucide-react';
import { cn } from '../../lib/utils';

interface EpisodeSpineProps {
  episodes: number[];
  openEpisodes: Set<number>;
  onEpisodeToggle: (ep: number) => void;
  onAddEpisode: () => void;
}

export const EpisodeSpine: React.FC<EpisodeSpineProps> = ({
  episodes,
  openEpisodes,
  onEpisodeToggle,
  onAddEpisode,
}) => {
  return (
    <div className="episode-spine-container sticky top-0 right-0 z-20">
      <div className="flex flex-col h-full">
        {episodes.map(ep => (
          <button
            key={ep}
            onClick={() => onEpisodeToggle(ep)}
            className={cn(
              'episode-spine-button',
              'transition-colors duration-200',
              openEpisodes.has(ep) ? 'bg-[#00FFF0]' : 'bg-white hover:bg-gray-100',
              'mb-1'
            )}
          >
            EP_{String(ep).padStart(2, '0')}
          </button>
        ))}
        {episodes.length < 8 && (
          <button
            onClick={onAddEpisode}
            className="w-10 h-10 border-2 border-black hover:bg-gray-100 flex items-center justify-center"
          >
            <Plus className="h-4 w-4" />
          </button>
        )}
      </div>
    </div>
  );
};