import { ErrorBoundary } from '../../ErrorBoundary';
import { SceneContent as BaseSceneContent } from './SceneContent';
import { logger } from '../../../utils/logger';

// Wrap SceneContent with ErrorBoundary
export const SceneContent = (props: React.ComponentProps<typeof BaseSceneContent>) => {
  return (
    <ErrorBoundary
      onError={(error) => {
        logger.error('Scene content error:', error);
      }}
      fallback={
        <div className="flex-1 flex flex-col items-center justify-center p-8 bg-red-50 border-4 border-red-300 rounded-lg">
          <h2 className="text-xl font-bold text-red-700 mb-4">Scene Editor Error</h2>
          <p className="text-gray-700 mb-6 text-center max-w-md">
            There was a problem loading the scene editor. This could be due to corrupted scene data or a browser compatibility issue.
          </p>
          <div className="flex gap-4">
            <button 
              onClick={() => window.location.reload()} 
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Reload Page
            </button>
            <button 
              onClick={props.onClose} 
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
            >
              Close Scene
            </button>
          </div>
        </div>
      }
    >
      <BaseSceneContent {...props} />
    </ErrorBoundary>
  );
};

export * from './SceneEditor';
export * from './SceneHeader';
export * from './SceneNotes';