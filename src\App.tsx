import React from 'react';
import { NavigationProvider } from './contexts/NavigationContext';
import { useProject } from './contexts/ProjectContext';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { AuthGuard } from './components/auth/AuthGuard';
import { AppContent } from './components/AppContent';
import { TemplateProvider } from './contexts/TemplateContext';
import { RevisionProvider } from './contexts/RevisionContext';
import { CollaborationProvider } from './contexts/CollaborationContext';
import { ConditionalChatWindow } from './components/chat/ConditionalChatWindow';

export function App() {
  return (
    <AuthProvider>
      <ThemeProvider>
        <NavigationProvider>
          <RevisionProvider>
            <CollaborationProvider>
              <AuthGuard>
                <TemplateProvider>
                  <AppContent />
                </TemplateProvider>
                <ConditionalChatWindow />
              </AuthGuard>
            </CollaborationProvider>
          </RevisionProvider>
        </NavigationProvider>
      </ThemeProvider>
    </AuthProvider>
  );
}