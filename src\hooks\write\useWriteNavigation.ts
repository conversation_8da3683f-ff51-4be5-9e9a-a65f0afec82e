import { useState, useCallback, useEffect } from 'react';
import { defaultActStructure } from '../../constants/episodes';
import { logger } from '../../utils/logger';
import { useProject } from '../../contexts/ProjectContext';
import { getProjectScopedKey } from '../../utils/storageUtils';
import { useEpisodeState } from '../useEpisodeState';

export interface WriteScene {
  id: string;
  actId: string;
  episodeId: number;
  title: string;
  hasContent: boolean;
  isComplete?: boolean;
  lastEdited?: string;
}

export function useWriteNavigation() {
  const { currentProject } = useProject();
  const projectId = currentProject?.id;
  const storageKeyPrefix = projectId ? `project_${projectId}_` : '';
  const { episodes, episodeData, updateEpisodeData } = useEpisodeState();
  const [activeEpisodeId, setActiveEpisodeId] = useState<number | null>(() => {
    // Try to load from project-scoped storage
    if (projectId) {
      const storedId = localStorage.getItem(getProjectScopedKey(projectId, 'activeEpisodeId'));
      return storedId ? parseInt(storedId) : null;
    }
    return null;
  });
  const [activeSceneId, setActiveSceneId] = useState<string | undefined>(() => {
    // Try to load from project-scoped storage
    if (projectId) {
      const key = getProjectScopedKey(projectId, 'activeSceneId');
      const storedId = localStorage.getItem(key);
      logger.debug('Loading active scene ID from storage', { projectId, key, storedId });
      return storedId || undefined;
    }
    return undefined;
  });
  const [isNavCollapsed, setIsNavCollapsed] = useState(false);
  const [availableScenes, setAvailableScenes] = useState<WriteScene[]>([]);
  const [expandedEpisodes, setExpandedEpisodes] = useState<Set<number>>(() => {
    // Try to load expanded episodes from project-scoped storage
    if (projectId) {
      const key = getProjectScopedKey(projectId, 'expandedEpisodes');
      const stored = localStorage.getItem(key);
      if (stored) {
        try {
          const parsed = JSON.parse(stored);
          return new Set(parsed);
        } catch (e) {
          logger.error('Failed to parse stored expanded episodes', e);
        }
      }
    }
    return new Set();
  });
  
  const [expandedActs, setExpandedActs] = useState<Set<string>>(() => {
    // Try to load expanded acts from project-scoped storage
    if (projectId) {
      const key = getProjectScopedKey(projectId, 'expandedActs');
      const stored = localStorage.getItem(key);
      if (stored) {
        try {
          const parsed = JSON.parse(stored);
          return new Set(parsed);
        } catch (e) {
          logger.error('Failed to parse stored expanded acts', e);
        }
      }
    }
    return new Set();
  });
  
  // Reset state when project changes
  useEffect(() => {
    if (projectId) {
      logger.debug('Project changed, resetting state', { projectId });
      // Load active scene from project-scoped storage
      const storedSceneId = localStorage.getItem(getProjectScopedKey(projectId, 'activeSceneId'));
      setActiveSceneId(storedSceneId || undefined);
      
      // Load active episode from project-scoped storage
      const storedEpisodeId = localStorage.getItem(getProjectScopedKey(projectId, 'activeEpisodeId'));
      setActiveEpisodeId(storedEpisodeId ? parseInt(storedEpisodeId) : null);
      
      // Load expanded episodes from project-scoped storage
      const storedExpandedEpisodes = localStorage.getItem(getProjectScopedKey(projectId, 'expandedEpisodes'));
      if (storedExpandedEpisodes) {
        try {
          const parsed = JSON.parse(storedExpandedEpisodes);
          setExpandedEpisodes(new Set(parsed));
        } catch (e) {
          logger.error('Failed to parse stored expanded episodes', e);
        }
      }
      
      // Load expanded acts from project-scoped storage
      const storedExpandedActs = localStorage.getItem(getProjectScopedKey(projectId, 'expandedActs'));
      if (storedExpandedActs) {
        try {
          const parsed = JSON.parse(storedExpandedActs);
          setExpandedActs(new Set(parsed));
        } catch (e) {
          logger.error('Failed to parse stored expanded acts', e);
        }
      }
    } else {
      // Clear state when no project is selected
      setActiveSceneId(undefined);
      setActiveEpisodeId(null);
      setExpandedEpisodes(new Set());
      setExpandedActs(new Set());
    }
  }, [projectId]);

  // Save expanded state whenever it changes
  useEffect(() => {
    if (projectId) {
      logger.debug('Saving expanded state', { projectId, episodesCount: expandedEpisodes.size, actsCount: expandedActs.size });
      // Save expanded episodes
      const episodesArray = Array.from(expandedEpisodes);
      localStorage.setItem(
        getProjectScopedKey(projectId, 'expandedEpisodes'),
        JSON.stringify(episodesArray)
      );
      
      // Save expanded acts
      const actsArray = Array.from(expandedActs);
      localStorage.setItem(
        getProjectScopedKey(projectId, 'expandedActs'),
        JSON.stringify(actsArray)
      );
    }
  }, [projectId, expandedEpisodes, expandedActs]);
  
  // Load available scenes from episode data
  useEffect(() => {
    if (!episodes.length) return;
    
    logger.debug('Loading available scenes', { projectId, episodeCount: episodes.length });
    const scenes: WriteScene[] = [];
    episodes.forEach(episodeId => {
      const episodeData_ = episodeData[episodeId];
      if (!episodeData_) return;
      episodeData_.acts.forEach(act => {
        act.scenes.forEach(scene => {
          const sceneId = `${episodeId}-${act.id}-${scene.id}`;
          let hasContent = false;
          let isComplete = false;
          let lastEdited = undefined;
          try {            
            let savedData = localStorage.getItem(getProjectScopedKey(projectId, `scene_${sceneId}`));
            if (!savedData && projectId) {
              savedData = localStorage.getItem(`write_scene_${sceneId}`);
              if (savedData) {
                localStorage.setItem(getProjectScopedKey(projectId, `scene_${sceneId}`), savedData);
                logger.debug('Migrated scene data from legacy key to project-scoped key', { sceneId });
              }
              
            }
            if (savedData) {
              const parsedData = JSON.parse(savedData);
              hasContent = Boolean(parsedData.content?.trim());
              isComplete = Boolean(parsedData.isComplete);
              lastEdited = parsedData.lastEdited;
            }
          } catch (error) {
            logger.error('Failed to parse scene data:', error);
          }
          scenes.push({
            id: sceneId,
            actId: act.id,
            episodeId: episodeId,
            title: scene.title || `Scene ${scene.id}`,
            hasContent,
            isComplete,
            lastEdited
          });
        });
      });
    });
    // Only update if different (shallow compare by id)
    setAvailableScenes(prev => {
      if (prev.length === scenes.length && prev.every((s, i) => s.id === scenes[i].id)) {
        return prev;
      }
      return scenes;
    });
  }, [episodes, episodeData, projectId]);
  
  // Auto-expand the active episode and act
  useEffect(() => {
    if (activeSceneId) {
      const parts = activeSceneId.split('-');
      if (parts.length >= 2) {
        const episodeId = parseInt(parts[0]);
        const actId = parts[1];
        
        setExpandedEpisodes(prev => {
          const next = new Set(prev);
          next.add(episodeId);
          return next;
        });
        
        setExpandedActs(prev => {
          const next = new Set(prev);
          next.add(actId);
          return next;
        });
      }
    }
  }, [activeSceneId]);
  
  const toggleNavigation = useCallback(() => {
    setIsNavCollapsed(prev => !prev);
  }, []);
  
  const toggleEpisode = useCallback((episodeId: number) => {
    setExpandedEpisodes(prev => {
      const next = new Set(prev);
      if (next.has(episodeId)) {
        next.delete(episodeId);
      } else {
        next.add(episodeId);
      }
      return next;
    });
  }, []);
  
  const toggleAct = useCallback((actId: string) => {
    setExpandedActs(prev => {
      const next = new Set(prev);
      if (next.has(actId)) {
        next.delete(actId);
      } else {
        next.add(actId);
      }
      return next;
    });
  }, []);
  
  const selectScene = useCallback((sceneId?: string) => {
    if (sceneId) {
      logger.debug('Selecting scene', { sceneId, projectId });
      // Parse episode ID from scene ID
      const parts = sceneId.split('-');
      if (parts.length >= 1) {
        const episodeId = parseInt(parts[0]);
        setActiveEpisodeId(episodeId);
      }
    }
    
    // Save active scene ID to project-scoped storage
    if (sceneId && projectId) {
      localStorage.setItem(getProjectScopedKey(projectId, 'activeSceneId'), sceneId);
    } else if (projectId && localStorage.getItem(getProjectScopedKey(projectId, 'activeSceneId'))) {
      localStorage.removeItem(getProjectScopedKey(projectId, 'activeSceneId'));
    }
    
    setActiveSceneId(sceneId);
    logger.debug('Scene selected:', { sceneId });
  }, [projectId]);
  
  const getSceneTitle = useCallback((sceneId: string): string => {
    const scene = availableScenes.find(s => s.id === sceneId);
    if (scene) return scene.title;
    
    // Fallback: Parse the scene ID to generate a title
    const parts = sceneId.split('-');
    if (parts.length >= 3) {
      const episodeId = parseInt(parts[0]);
      const actId = parts[1];
      const sceneId = parseInt(parts[2]);
      
      // Try to get the title from episodeData
      const act = episodeData[episodeId]?.acts.find(a => a.id === actId);
      const scene = act?.scenes.find(s => s.id === sceneId);
      
      if (scene) {
        return scene.title || `Scene ${sceneId}`;
      }
      
      // If not found in episodeData, use default structure
      const actTemplate = defaultActStructure.find(a => a.id === actId);
      return actTemplate ? `${actTemplate.title} Scene ${sceneId}` : `Scene ${sceneId}`;
    }
    
    return 'Unknown Scene';
  }, [availableScenes, episodeData]);
  
  const getScenesByEpisode = useCallback((episodeId: number): WriteScene[] => {
    return availableScenes.filter(scene => scene.episodeId === episodeId);
  }, [availableScenes]);
  
  const getScenesByAct = useCallback((actId: string): WriteScene[] => {
    return availableScenes.filter(scene => scene.actId === actId);
  }, [availableScenes]);
  
  const getSceneCount = useCallback((actId: string): number => {
    return availableScenes.filter(scene => scene.actId === actId).length;
  }, [availableScenes]);
  
  const markSceneComplete = useCallback((sceneId: string, isComplete: boolean) => {
    try {
      // Try project-scoped key first, then fall back to legacy key
      const storageKey = getProjectScopedKey(projectId, `scene_${sceneId}`);
      let savedData = localStorage.getItem(storageKey);
      
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        parsedData.isComplete = isComplete;
        parsedData.lastEdited = new Date().toISOString();
        localStorage.setItem(storageKey, JSON.stringify(parsedData));
        logger.debug('Updated scene completion status', { sceneId, isComplete, storageKey });
        
        // Update available scenes
        setAvailableScenes(prev => 
          prev.map(scene => 
            scene.id === sceneId 
              ? { ...scene, isComplete, lastEdited: parsedData.lastEdited }
              : scene
          )
        );
      }
    } catch (error) {
      logger.error('Failed to update scene completion status:', error);
    }
  }, [projectId]);

  const updateSceneTitle = useCallback((sceneId: string, title: string) => {
    // Parse the scene ID to get episode, act, and scene IDs
    const parts = sceneId.split('-');
    if (parts.length < 3) return;
    
    logger.debug('Updating scene title', { sceneId, title, projectId });
    
    const episodeId = parseInt(parts[0]);
    const actId = parts[1];
    const sceneIdNum = parseInt(parts[2]);
    
    // Update the scene title in episodeData
    updateEpisodeData(episodeId, data => ({
      ...data,
      acts: data.acts.map(act => 
        act.id === actId 
          ? {
              ...act,
              scenes: act.scenes.map(scene => 
                scene.id === sceneIdNum 
                  ? { ...scene, title }
                  : scene
              )
            }
          : act
      )
    }));
    
    // Update available scenes
    setAvailableScenes(prev => 
      prev.map(scene => 
        scene.id === sceneId 
          ? { ...scene, title }
          : scene
      )
    );
    
    // Also update the write module data if it exists
    try {
      const storageKey = getProjectScopedKey(projectId, `scene_${sceneId}`);
      let savedData = localStorage.getItem(storageKey);
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        parsedData.title = title;
        parsedData.lastEdited = new Date().toISOString();
        localStorage.setItem(storageKey, JSON.stringify(parsedData));
        logger.debug('Updated scene title in write data', { sceneId, title, storageKey });
      }
    } catch (error) {
      logger.error('Failed to update scene title in write data:', error);
    }
  }, [updateEpisodeData, projectId]);

  const addScene = useCallback((episodeId: number, actId: string) => {
    // Get the act from episodeData
    const act = episodeData[episodeId]?.acts.find(a => a.id === actId);
    if (!act) return;
    
    // Create a new scene
    const newSceneId = Date.now();
    const newScene = {
      id: newSceneId,
      title: `New Scene`,
      content: '',
      actId,
      episodeId
    };
    
    // Update episodeData
    updateEpisodeData(episodeId, data => ({
      ...data,
      acts: data.acts.map(a => 
        a.id === actId 
          ? { ...a, scenes: [...a.scenes, newScene] }
          : a
      )
    }));
    
    // Return the full scene ID for the new scene
    return `${episodeId}-${actId}-${newSceneId}`;
  }, [episodeData, updateEpisodeData]);

  /**
   * Import scenes into an act, with option to replace or append
   * @param episodeId The episode number
   * @param actId The act ID
   * @param scenes Array of scenes to import
   * @param mode 'replace' to overwrite, 'append' to add (default: 'replace')
   */
  const importScenes = useCallback((episodeId: number, actId: string, scenes: Scene[], mode: 'replace' | 'append' = 'replace') => {
    // Get the act from episodeData
    const act = episodeData[episodeId]?.acts.find(a => a.id === actId);
    if (!act) return [];
    
    // Remove old scene storage if replacing
    if (mode === 'replace' && projectId) {
      const oldScenes = act.scenes || [];
      oldScenes.forEach((oldScene: any) => {
        const fullSceneId = `${episodeId}-${actId}-${oldScene.id}`;
        const key = getProjectScopedKey(projectId, `scene_${fullSceneId}`);
        localStorage.removeItem(key);
        logger.debug('Removed old scene:', { key, fullSceneId });
      });
    }
    
    // Clean up episodeData for this act first
    const updatedEpisodeData = updateEpisodeData(episodeId, data => {
      const updatedData = { ...data };
      // Find the act to update
      const actIndex = updatedData.acts.findIndex(a => a.id === actId);
      if (actIndex === -1) return updatedData;
      
      // Replace or clear scenes in this act based on mode
      updatedData.acts[actIndex] = {
        ...updatedData.acts[actIndex],
        scenes: mode === 'replace' ? [] : [...updatedData.acts[actIndex].scenes]
      };
      
      return updatedData;
    });
    
    // Create new scene IDs and objects
    const newSceneIds: string[] = [];
    
    // Now add scenes one by one to ensure proper storage
    scenes.forEach((scene, index) => {
      // Use the scene's unique ID from convertToAppScenes
      const uniqueSceneId = scene.id;
      const fullSceneId = `${episodeId}-${actId}-${uniqueSceneId}`;
      
      logger.debug('Importing scene:', { 
        uniqueSceneId, 
        fullSceneId, 
        title: scene.title,
        contentLength: scene.content?.length || 0
      });
      
      // Create a copy of the scene with correct IDs for this act
      const sceneObj = {
        ...scene,
        id: uniqueSceneId,
        actId,
        episodeId,
        lastEdited: new Date().toISOString()
      };
      
      // Store scene in localStorage
      if (projectId) {
        const key = getProjectScopedKey(projectId, `scene_${fullSceneId}`);
        localStorage.setItem(key, JSON.stringify(sceneObj));
        
        // Verify scene was stored correctly
        const storedValue = localStorage.getItem(key);
        const parsedValue = storedValue ? JSON.parse(storedValue) : null;
        
        if (parsedValue && parsedValue.content) {
          logger.debug('Scene successfully stored in localStorage:', {
            key,
            sceneId: fullSceneId,
            hasContent: true,
            contentLength: parsedValue.content.length
          });
        } else {
          logger.error('Failed to store scene content:', {
            key,
            sceneId: fullSceneId,
            storedValue: storedValue ? 'exists' : 'missing'
          });
        }
      }
      
      // Add to episodeData
      updateEpisodeData(episodeId, data => {
        const updatedData = { ...data };
        const actIndex = updatedData.acts.findIndex(a => a.id === actId);
        if (actIndex === -1) return updatedData;
        
        // Add this scene to the act in episodeData
        const sceneEntry = {
          id: uniqueSceneId,
          title: scene.title || `Imported Scene ${uniqueSceneId}`,
          content: scene.content || '', 
          actId,
          episodeId
        };
        
        const currentScenes = [...updatedData.acts[actIndex].scenes];
        
        if (mode === 'replace') {
          // Find if this scene ID already exists in the current scenes
          const existingIndex = currentScenes.findIndex(s => s.id === uniqueSceneId);
          if (existingIndex >= 0) {
            currentScenes[existingIndex] = sceneEntry;
          } else {
            currentScenes.push(sceneEntry);
          }
        } else {
          currentScenes.push(sceneEntry);
        }
        
        updatedData.acts[actIndex] = {
          ...updatedData.acts[actIndex],
          scenes: currentScenes
        };
        
        return updatedData;
      });
      
      newSceneIds.push(fullSceneId);
    });
    
    // Debug: Check for duplicate IDs in episode data
    const allSceneIds = new Set<string>();
    const duplicates: string[] = [];
    
    Object.values(episodeData).forEach(episode => {
      episode.acts.forEach(act => {
        act.scenes.forEach(scene => {
          const id = scene.id.toString();
          if (allSceneIds.has(id)) {
            duplicates.push(id);
          } else {
            allSceneIds.add(id);
          }
        });
      });
    });
    
    if (duplicates.length > 0) {
      logger.warn('Found duplicate scene IDs in episodeData:', { duplicates });
    }
    
    // Debug: Log all storage keys for this project
    if (projectId) {
      setTimeout(() => {
        try {
          const allKeys = Object.keys(localStorage).filter(k => 
            k.startsWith(getProjectScopedKey(projectId, 'scene_'))
          );
          
          logger.debug(`Project has ${allKeys.length} scene keys in localStorage`);
          
          // Check each new scene ID has been properly stored
          scenes.forEach((scene, i) => {
            const uniqueSceneId = scene.id;
            const fullSceneId = `${episodeId}-${actId}-${uniqueSceneId}`;
            const key = getProjectScopedKey(projectId, `scene_${fullSceneId}`);
            
            // Verify content exists
            try {
              const stored = localStorage.getItem(key);
              if (stored) {
                const parsed = JSON.parse(stored);
                logger.debug(`Verification - Scene ${i+1}:`, { 
                  id: uniqueSceneId, 
                  fullSceneId, 
                  hasContent: Boolean(parsed?.content),
                  contentLength: parsed?.content?.length || 0
                });
              } else {
                logger.error(`Verification failed - Scene ${i+1} not found:`, { fullSceneId });
              }
            } catch (e) {
              logger.error(`Verification error for scene ${fullSceneId}:`, e);
            }
          });
          
          // Check episode data structure
          const episodeDataStr = localStorage.getItem(getProjectScopedKey(projectId, 'episode_data'));
          if (episodeDataStr) {
            const parsed = JSON.parse(episodeDataStr);
            const actScenes = parsed?.[episodeId]?.acts?.find((a: any) => a.id === actId)?.scenes || [];
            logger.debug(`Act ${actId} now has ${actScenes.length} scenes in episodeData`);
          }
        } catch (e) {
          logger.error('Failed to verify scene storage:', e);
        }
      }, 500);
    }
    
    logger.debug('Imported scenes:', { 
      episodeId, 
      actId, 
      count: scenes.length, 
      mode, 
      sceneIds: newSceneIds
    });
    
    return newSceneIds;
  }, [episodeData, updateEpisodeData, projectId]);
  
  return {
    activeEpisodeId,
    activeSceneId,
    isNavCollapsed,
    availableScenes,
    expandedEpisodes,
    expandedActs,
    toggleNavigation,
    toggleEpisode,
    toggleAct,
    selectScene,
    getSceneTitle,
    getScenesByEpisode,
    getScenesByAct,
    getSceneCount,
    markSceneComplete,
    updateSceneTitle,
    addScene, 
    importScenes,
    acts: defaultActStructure
  };
}