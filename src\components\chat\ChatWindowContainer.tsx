import React, { useEffect, useState } from 'react';
import { useEpisodeTrackStore } from '../../store/episodeTrackStore';
import { useLocalStorage } from '../../hooks/shared/useLocalStorage';

/**
 * Container component for the ChatWindow that handles positioning
 * relative to the ColorLegend and ensures proper z-index layering
 */
export const ChatWindowContainer: React.FC = () => {
  const { colorLegend, isColorLegendMinimized } = useEpisodeTrackStore();
  const [chatZIndex, setChatZIndex] = useState(9999); // DEBUG: Raise z-index for testing
  const [chatPosition, setChatPosition] = useLocalStorage<'above' | 'below'>('chat_position_relative_to_legend', 'above');

  // Adjust z-index based on color legend visibility
  useEffect(() => {
    // DEBUG: Always use 9999 for now
    setChatZIndex(9999);
  }, [colorLegend.isVisible, chatPosition]);

  // Calculate bottom offset when chat is positioned above color legend
  const getBottomOffset = () => {
    if (!colorLegend.isVisible || chatPosition !== 'above') return undefined;
    // Calculate height of color legend
    const legendHeight = isColorLegendMinimized ? 40 : 300; // Approximate heights
    return `${legendHeight}px`;
  };

  return (
    <>
      {/* DEBUG: Overlay temporarily removed to test pointer-events/z-index issues */}
      {/*
      <div 
        className="fixed inset-0 bg-black/5 pointer-events-none z-30"
        style={{ 
          opacity: colorLegend.isVisible ? 0.05 : 0,
          transition: 'opacity 0.3s ease',
          display: colorLegend.isVisible ? 'block' : 'none'
        }}
      />
      */}
      {/* Chat window with adjusted z-index */}
      <div 
        style={{ 
          zIndex: chatZIndex,
          position: 'relative',
          marginBottom: getBottomOffset()
        }}
      >
        {/* Comment out the direct rendering of <ChatWindow /> */}
        {/* <ChatWindow /> */}
      </div>
    </>
  );
};