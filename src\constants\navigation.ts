import { Home, MessageSquareText, LayoutPanelTop, GitBranch, Pen, BookOpen, ListTodo } from 'lucide-react';

export const navigationItems = [
  {
    id: 'test',
    label: 'Test',
    icon: Home,
  },
  {
    id: 'prompts',
    label: 'Prompts',
    icon: BookOpen,
  },
  {
    id: 'workshop',
    label: 'Workshop',
    icon: MessageSquareText,
  },
  {
    id: 'timeline',
    label: 'Timeline',
    icon: GitBranch,
  },
  {
    id: 'episodes',
    label: 'Episodes',
    icon: LayoutPanelTop,
  },
  {
    id: 'write',
    label: 'Write',
    icon: Pen,
  },
  {
    id: 'tasks',
    label: 'Tasks',
    icon: ListTodo,
  },
] as const;

export type ViewType = typeof navigationItems[number]['id'] | 'home' | 'test';