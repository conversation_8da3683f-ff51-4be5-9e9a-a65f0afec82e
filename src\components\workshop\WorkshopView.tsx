import React, { useState, useCallback, useMemo } from 'react';
import { QuadrantColumn } from './QuadrantColumn';
import type { PlotPoint, DragItem } from '../../types/workshop';
import { useTemplate } from '../../hooks/useTemplate';
import { useEpisodeTrackStore } from '../../store/episodeTrackStore';

// Base section definitions
const BASE_SECTIONS = [
  { baseId: 'setup', title: 'Setup', subtitle: 'Establish the world and characters', color: 'bg-blue-500' },
  { baseId: 'conflict', title: 'Conflict', subtitle: 'Introduce the main conflict', color: 'bg-red-500' },
  { baseId: 'rising', title: 'Rising Action', subtitle: 'Escalate the stakes', color: 'bg-yellow-500' },
  { baseId: 'climax', title: 'Climax', subtitle: 'The highest point of tension', color: 'bg-purple-500' },
  { baseId: 'falling', title: 'Falling Action', subtitle: 'Show the aftermath', color: 'bg-green-500' },
  { baseId: 'resolution', title: 'Resolution', subtitle: 'Wrap up loose ends', color: 'bg-gray-500' },
  { baseId: 'notes', title: 'Additional Notes', subtitle: 'Other important details', color: 'bg-indigo-500' }
] as const;

const defaultQuadrants = [
  { id: 'q1', title: 'Quadrant 1' },
  { id: 'q2', title: 'Quadrant 2' },
  { id: 'q3', title: 'Quadrant 3' },
  { id: 'q4', title: 'Quadrant 4' },
];

// Pre-compute sections for each quadrant with unique IDs
function createSectionsForQuadrant(quadrantId) {
  return BASE_SECTIONS.map(section => ({
    id: `${quadrantId}-${section.baseId}`, // Create unique IDs by prefixing with quadrant ID
    title: section.title,
    subtitle: section.subtitle,
    color: section.color,
    baseId: section.baseId,
    quadrantId // Store the quadrant ID for reference
  }));
}

export const WorkshopView: React.FC = () => {
  const [plotPoints, setPlotPoints] = useState<PlotPoint[]>([]);
  const [openQuadrants, setOpenQuadrants] = useState(new Set(['q1', 'q2', 'q3', 'q4']));
  const [expandedQuadrant, setExpandedQuadrant] = useState<string | null>(null);
  const { template } = useTemplate();
  const { moveCard } = useEpisodeTrackStore();

  // Create quadrant sections map with unique section IDs
  const quadrantSectionsMap = useMemo(() => {
    const map = {};
    defaultQuadrants.forEach(quadrant => {
      map[quadrant.id] = createSectionsForQuadrant(quadrant.id);
    });
    return map;
  }, []);

  // Create a lookup map from sectionId to quadrantId
  const sectionToQuadrantMap = useMemo(() => {
    const map = {};
    Object.entries(quadrantSectionsMap).forEach(([quadrantId, sections]) => {
      sections.forEach(section => {
        map[section.id] = quadrantId;
      });
    });
    return map;
  }, [quadrantSectionsMap]);

  const handleAddPoint = useCallback((sectionId: string) => {
    console.log('Adding point to section:', sectionId);
    
    // Find all existing points in the section
    const existingPoints = plotPoints.filter(p => p.sectionId === sectionId);
    console.log(`Section ${sectionId} has ${existingPoints.length} existing points`);
    
    // Find the first available position (0-4) for visual display
    // Note: Backend will store all cards regardless of position
    const occupiedPositions = new Set(existingPoints.map(p => p.position || 0));
    let position = 0;
    for (let i = 0; i < 5; i++) {
      if (!occupiedPositions.has(i)) {
        position = i;
        break;
      }
    }
    
    console.log('Creating new point at position:', position);
    
    // Create a new point with a unique ID
    const newPoint: PlotPoint = {
      id: `plot-${Date.now()}-${Math.random().toString(36).slice(2)}`,
      content: '',
      sectionId,
      position, // Position for visual display
      source: 'manual'
    };
    
    // Always add the new point, preserving all existing ones
    setPlotPoints(prev => {
      console.log(`Before adding: ${prev.length} total plot points`);
      const result = [...prev, newPoint];
      console.log(`After adding: ${result.length} total plot points`);
      return result;
    });
  }, [plotPoints]);

  const handleUpdatePoint = useCallback((id: string, content: string) => {
    setPlotPoints(prev => 
      prev.map(point => 
        point.id === id ? { ...point, content } : point
      )
    );
  }, []);

  const handleDeletePoint = useCallback((id: string) => {
    setPlotPoints(prev => prev.filter(point => point.id !== id));
  }, []);

  const handleUpdateSection = useCallback((sectionId: string, updates: { title?: string; subtitle?: string }) => {
    console.log('Section update:', sectionId, updates);
  }, []);

  const handleMovePoint = useCallback((draggedItem: DragItem, targetSectionId: string, targetIndex: number) => {
    console.log('handleMovePoint called with:', { draggedItem, targetSectionId, targetIndex });
    
    if (draggedItem.type === 'plot-point') {
      // Handle regular plot point dragging within the workshop
      setPlotPoints(prevPoints => {
        // Store previous state for verification
        const prevPointsBackup = JSON.parse(JSON.stringify(prevPoints));
        
        // Count cards by section before the operation
        const initialCounts: Record<string, number> = {};
        prevPoints.forEach(p => {
          initialCounts[p.sectionId] = (initialCounts[p.sectionId] || 0) + 1;
        });
        console.log('Initial card counts by section:', initialCounts);
        console.log('Total cards before:', prevPoints.length);
        
        // Find the card being moved
        const cardBeingMoved = prevPoints.find(p => p.id === draggedItem.id);
        if (!cardBeingMoved) {
          console.error('Card not found in plotPoints:', draggedItem.id);
          return prevPoints; // Return current state if card not found
        }
        
        // Create a new array without the dragged item
        const pointsWithoutDragged = prevPoints.filter(p => p.id !== draggedItem.id);
        
        // Create an updated version of the dragged item with new section and position
        const updatedItem: PlotPoint = {
          ...cardBeingMoved,
          sectionId: targetSectionId,
          position: targetIndex,
          // Keep other properties
          content: cardBeingMoved.content,
          source: cardBeingMoved.source || 'manual'
        };
        
        // Add the updated item to the points array
        const newPoints = [...pointsWithoutDragged, updatedItem];
        
        // Count cards after the operation to verify count is maintained
        const finalCounts: Record<string, number> = {};
        newPoints.forEach(p => {
          finalCounts[p.sectionId] = (finalCounts[p.sectionId] || 0) + 1;
        });
        console.log('Final card counts by section:', finalCounts);
        console.log('Total cards after:', newPoints.length);
        
        // Verify we haven't lost any cards unexpectedly
        if (newPoints.length < prevPoints.length) {
          console.error('CARD COUNT DECREASED! Restoring previous state to prevent data loss.');
          return prevPointsBackup;
        }
        
        return newPoints;
      });
    } else if (draggedItem.type === 'storyline-card') {
      // Get the quadrant ID for this section
      const targetQuadrantId = sectionToQuadrantMap[targetSectionId];
      console.log('Target quadrant identified:', targetQuadrantId, 'for section:', targetSectionId);

      if (!targetQuadrantId) {
        console.error('Cannot determine quadrant for section:', targetSectionId);
        return;
      }

      // This is a card from the color legend (timeline storyline)
      console.log('Moving storyline card to workshop:', draggedItem.id);
      
      // First, use moveCard to update global state with exact position
      moveCard(draggedItem.id, {
        type: 'workshop',
        quadrantId: targetSectionId, // Pass the full section ID for precise placement
        position: targetIndex // Pass the exact position (0-4)
      });
      
      // Keep track of the card count before the operation
      const cardCountBefore = plotPoints.length;
      
      // Then add it to our local workshop state with exact position
      setPlotPoints(prevPoints => {
        // Store previous state for verification
        const prevPointsBackup = JSON.parse(JSON.stringify(prevPoints));
        
        // Check if this card is already in our plots
        const cardAlreadyExists = prevPoints.some(p => p.id === draggedItem.id);
        
        let newPoints;
        if (cardAlreadyExists) {
          console.log('Card already exists in plotPoints, updating position only');
          // Update the existing card's position and section
          newPoints = prevPoints.map(p => {
            if (p.id === draggedItem.id) {
              return {
                ...p,
                sectionId: targetSectionId,
                position: targetIndex
              };
            }
            return p;
          });
        } else {
          console.log('Card is new to plotPoints, adding it');
          // Card doesn't exist in plotPoints yet, add it
          const newPoint: PlotPoint = {
            id: draggedItem.id,
            content: draggedItem.content,
            sectionId: targetSectionId,
            position: targetIndex,
            source: draggedItem.source || 'manual',
          };
          newPoints = [...prevPoints, newPoint];
        }
        
        // Verify integrity of the operation
        if (!cardAlreadyExists && newPoints.length <= prevPoints.length) {
          console.error('FAILED TO ADD NEW CARD! Restoring previous state.');
          return prevPointsBackup;
        }
        
        return newPoints;
      });
    }
  }, [moveCard, sectionToQuadrantMap, plotPoints.length]);

  const toggleQuadrant = useCallback((quadrantId: string) => {
    setOpenQuadrants(prev => {
      const next = new Set(prev);
      if (next.has(quadrantId)) {
        next.delete(quadrantId);
      } else {
        next.add(quadrantId);
      }
      return next;
    });
  }, []);

  // Filter plot points by quadrant
  const getQuadrantPlotPoints = useCallback((quadrantId: string) => {
    // Get the section IDs for this quadrant
    const sectionIds = quadrantSectionsMap[quadrantId].map(s => s.id);
    
    // Return plot points that belong to those sections
    return plotPoints.filter(point => sectionIds.includes(point.sectionId));
  }, [plotPoints, quadrantSectionsMap]);

  return (
    <div className={`h-[calc(100vh-4rem)] ${template !== 'classic' ? 'shrinksize' : 'expandsize'}`}>
      <div className="w-full h-full flex">
        {defaultQuadrants.map(quadrant => (
          <QuadrantColumn
            key={quadrant.id}
            {...quadrant}
            plotSections={quadrantSectionsMap[quadrant.id]}
            plotPoints={getQuadrantPlotPoints(quadrant.id)}
            isOpen={openQuadrants.has(quadrant.id)}
            isExpanded={expandedQuadrant === quadrant.id}
            expandedQuadrant={expandedQuadrant}
            onToggle={() => toggleQuadrant(quadrant.id)}
            onExpand={() => {
              setExpandedQuadrant(quadrant.id);
            }}
            onCollapse={() => {
              setExpandedQuadrant(null);
            }}
            onAddPlotPoint={handleAddPoint}
            onUpdatePlotPoint={handleUpdatePoint}
            onDeletePlotPoint={handleDeletePoint}
            onUpdateSection={handleUpdateSection}
            onMovePoint={handleMovePoint}
            style={{ 
              paddingTop: "20px", 
              paddingBottom: "5px", 
              width: expandedQuadrant ? (expandedQuadrant === quadrant.id ? '100%' : '0') : '25%', 
              minWidth: expandedQuadrant ? (expandedQuadrant === quadrant.id ? '100%' : '0') : '200px'
            }}
          />
        ))}
      </div>
    </div>
  );
};