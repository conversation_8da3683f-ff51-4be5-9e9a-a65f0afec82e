import React from 'react';
import { Plus } from 'lucide-react';
import { EpisodeSpineButton } from './EpisodeSpineButton';

interface EpisodeSpineProps {
  episodes: number[];
  openEpisodes: Set<number>;
  onEpisodeToggle: (ep: number) => void;
  onAddEpisode: () => void;
}

export const EpisodeSpine: React.FC<EpisodeSpineProps> = ({
  episodes,
  openEpisodes,
  onEpisodeToggle,
  onAddEpisode,
}) => {
  return (
    <div className="episode-spine-container">
      <div className="flex flex-col h-full">
        {episodes.map(ep => (
          <EpisodeSpineButton
            key={ep}
            episodeNumber={ep}
            isOpen={openEpisodes.has(ep)}
            onToggle={onEpisodeToggle}
            totalEpisodes={episodes.length}
          />
        ))}
        {episodes.length < 8 && (
          <button
            onClick={onAddEpisode}
            className="w-10 h-10 border-2 border-black hover:bg-gray-100 flex items-center justify-center"
          >
            <Plus className="h-4 w-4" />
          </button>
        )}
      </div>
    </div>
  );
};