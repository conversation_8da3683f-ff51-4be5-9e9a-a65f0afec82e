import React, { useState } from 'react';
import { FileText, Upload, X } from 'lucide-react';
import { Button } from '../../ui/button';
import { cn } from '../../../lib/utils';
import { parseScenes, convertToAppScenes } from '../../../utils/fountainParser';
import { formatFountainText } from '../../../utils/fountainUtils';
import { logger } from '../../../utils/logger';
import type { ParsedScene } from '../../../utils/fountainParser';
import type { Scene } from '../../../types/scene';
import { defaultActStructure } from '../../../constants/episodes';

interface FountainImportProps {
  onImport: (scenes: Scene[], actId: string) => void;
  onCancel: () => void;
  actId: string;
  episodeId: number;
}

export const FountainImport: React.FC<FountainImportProps> = ({
  onImport,
  onCancel,
  actId,
  episodeId
}) => {
  const [text, setText] = useState('');
  const [parsedScenes, setParsedScenes] = useState<ParsedScene[]>([]);
  const [isParsing, setIsParsing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedScenes, setSelectedScenes] = useState<Set<number>>(new Set());
  const [previewMode, setPreviewMode] = useState<'list' | 'detail'>('list');
  const [selectedPreviewScene, setSelectedPreviewScene] = useState<number | null>(null);

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value);
    setError(null);
    setParsedScenes([]);
    setSelectedScenes(new Set());
    setSelectedPreviewScene(null);
  };

  const handleParse = () => {
    console.debug('FountainImport: handleParse called');
    if (!text.trim()) {
      setError('Please enter some text to parse');
      return;
    }

    setIsParsing(true);
    setError(null);

    try {
      const scenes = parseScenes(text);
      setParsedScenes(scenes);
      // Select all scenes by default
      setSelectedScenes(new Set(scenes.map((_, index) => index)));
      
      if (scenes.length === 0) {
        setError('No scenes found in the text. Make sure it follows Fountain format with scene headings (INT./EXT.)');
      } else {
        logger.debug(`Successfully parsed ${scenes.length} scenes`);
      }
    } catch (err) {
      logger.error('Failed to parse Fountain text:', err);
      setError('Failed to parse the text. Please check the format and try again.');
    } finally {
      setIsParsing(false);
    }
  };

  const handleImport = () => {
    if (selectedScenes.size === 0) {
      setError('No scenes to import');
      return;
    }

    // Format each selected scene before conversion
    const formattedScenes = parsedScenes
      .filter((_, index) => selectedScenes.has(index))
      .map(scene => {
        // Apply automatic formatting to the scene content and heading
        return {
          ...scene,
          content: formatFountainText(scene.content),
          heading: scene.heading.toUpperCase() // Ensure heading is properly formatted
        };
      });
    
    // Log the selected scenes' content to verify they have content
    formattedScenes.forEach((scene, idx) => {
      logger.debug(`Selected scene ${idx+1} (formatted): heading="${scene.heading}", content length=${scene.content.length}`);
    });

    // Convert the selected scenes to app format with the proper act and episode IDs
    const allAppScenes = convertToAppScenes(formattedScenes, actId, episodeId);
    
    // Validate that converted scenes have content
    const scenesWithoutContent = allAppScenes.filter(scene => !scene.content?.trim());
    if (scenesWithoutContent.length > 0) {
      logger.warn(`Warning: ${scenesWithoutContent.length} scenes have no content after conversion`);
    }
    
    // Log all converted scenes with their content length
    allAppScenes.forEach((scene, idx) => {
      logger.debug(`Converted app scene ${idx+1}: id=${scene.id}, title="${scene.title}", content length=${scene.content?.length || 0}`);
    });

    // Auto-distribute scenes across acts based on template
    const acts = defaultActStructure;
    
    // Clear localStorage scene data first to avoid conflicts
    if (allAppScenes.length > 0 && window.confirm('Clear existing scene data before import? (Recommended to avoid conflicts)')) {
      try {
        // Get all existing scene keys for this project and episode
        const projectId = localStorage.getItem('current_project_id');
        const prefix = projectId ? `project_${projectId}_scene_${episodeId}-` : '';
        
        Object.keys(localStorage)
          .filter(key => key.startsWith(prefix))
          .forEach(key => {
            localStorage.removeItem(key);
            logger.debug('Removed scene data:', { key });
          });
          
        logger.debug('Cleared existing scene data before import');
      } catch (e) {
        logger.error('Failed to clear existing scene data:', e);
      }
    }
    
    // Calculate how many scenes to allocate per act
    // This ensures all scenes are distributed evenly
    const totalScenes = allAppScenes.length;
    const totalActs = acts.length;
    
    // Base number of scenes per act (minimum 1)
    const baseScenesPerAct = Math.max(1, Math.floor(totalScenes / totalActs));
    
    // Extra scenes to distribute to earlier acts
    let remainingScenes = totalScenes - (baseScenesPerAct * totalActs);
    
    // Create a distribution plan
    const distribution: { actId: string, sceneCount: number }[] = acts.map(act => {
      // Allocate extra scenes to earlier acts if needed
      const extraScenes = remainingScenes > 0 ? 1 : 0;
      if (extraScenes > 0) remainingScenes--;
      
      return {
        actId: act.id,
        sceneCount: baseScenesPerAct + extraScenes
      };
    });
    
    logger.debug('Scene distribution plan:', { distribution, totalScenes });
    
    // Now distribute scenes according to the plan
    let sceneIdx = 0;
    for (const plan of distribution) {
      if (sceneIdx >= allAppScenes.length) break; // No more scenes to assign
      
      // Slice the already converted scenes with unique IDs
      const actScenes = allAppScenes.slice(sceneIdx, sceneIdx + plan.sceneCount);
      sceneIdx += plan.sceneCount;
      
      if (actScenes.length > 0) {
        // Log what we're importing to each act
        logger.debug(`Importing ${actScenes.length} scenes to act "${plan.actId}" with scene IDs: ${actScenes.map(s => s.id).join(', ')}`);
        
        // Call onImport for each act with its corresponding scenes
        onImport(actScenes, plan.actId);
      }
    }
    
    // Close the import modal
    onCancel();
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      const content = event.target?.result as string;
      setText(content);
    };
    reader.onerror = () => {
      setError('Failed to read the file');
    };
    reader.readAsText(file);
  };

  const toggleSceneSelection = (index: number) => {
    setSelectedScenes(prev => {
      const next = new Set(prev);
      if (next.has(index)) {
        next.delete(index);
      } else {
        next.add(index);
      }
      return next;
    });
  };

  const toggleAllScenes = () => {
    if (selectedScenes.size === parsedScenes.length) {
      // Deselect all
      setSelectedScenes(new Set());
    } else {
      // Select all
      setSelectedScenes(new Set(parsedScenes.map((_, index) => index)));
    }
  };

  const viewSceneDetail = (index: number) => {
    setSelectedPreviewScene(index);
    setPreviewMode('detail');
  };

  const backToList = () => {
    setSelectedPreviewScene(null);
    setPreviewMode('list');
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white border-4 border-black p-6 rounded-lg w-full max-w-3xl max-h-[90vh] flex flex-col text-black">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-black">Import Fountain Screenplay</h2>
          <Button variant="ghost" onClick={onCancel} className="h-8 w-8 p-1 text-black">
            <X className="h-5 w-5 text-black" />
          </Button>
        </div>

        {parsedScenes.length === 0 ? (
          <>
            <div className="mb-4">
              <label className="block font-bold mb-2 text-black">Paste Fountain Text</label>
              <textarea
                value={text}
                onChange={handleTextChange}
                className="w-full h-64 p-4 border-4 border-black font-mono text-sm text-black bg-white"
                placeholder="Paste your Fountain-formatted screenplay here..."
              />
            </div>

            <div className="mb-4">
              <label className="block font-bold mb-2 text-black">Or Upload a File</label>
              <input
                type="file"
                accept=".fountain,.txt,.ft"
                onChange={handleFileUpload}
                className="hidden"
                id="fountainFile"
              />
              <label
                htmlFor="fountainFile"
                className="flex items-center gap-2 p-3 border-4 border-black cursor-pointer hover:bg-gray-50 text-black bg-white"
              >
                <Upload className="h-5 w-5 text-black" />
                <span className="text-black">Select Fountain File</span>
              </label>
            </div>

            {error && (
              <div className="mb-4 p-3 bg-red-50 border-2 border-red-500 text-red-700">
                {error}
              </div>
            )}

            <div className="flex justify-end gap-2 mt-6">
              <Button variant="outline" onClick={onCancel} className="border-2 border-black text-black bg-white">
                Cancel
              </Button>
              <Button
                onClick={handleParse}
                disabled={isParsing || !text.trim()}
                className="bg-black text-white border-2 border-black hover:bg-gray-800"
              >
                Parse Screenplay
              </Button>
            </div>
          </>
        ) : (
          previewMode === 'list' ? (
            <>
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-bold">Found {parsedScenes.length} Scenes</h3>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={toggleAllScenes}
                      className="text-xs border border-black"
                    >
                      {selectedScenes.size === parsedScenes.length ? 'Deselect All' : 'Select All'}
                    </Button>
                  </div>
                </div>
                <div className="border-4 border-black p-4 h-64 overflow-y-auto">
                  {parsedScenes.map((scene, index) => (
                    <div 
                      key={index}
                      className={cn(
                        "mb-4 p-3 border-2 cursor-pointer transition-colors",
                        selectedScenes.has(index) 
                          ? "border-black bg-gray-50" 
                          : "border-gray-300 hover:border-black"
                      )}
                      onClick={() => toggleSceneSelection(index)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="font-bold">{scene.heading}</div>
                        <div className="text-xs text-gray-500">Scene {index + 1}</div>
                      </div>
                      <div className="text-sm text-gray-600 line-clamp-2 mb-2">
                        {scene.content.substring(0, 150)}
                        {scene.content.length > 150 && '...'}
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            checked={selectedScenes.has(index)}
                            onChange={(e) => {
                              e.stopPropagation();
                              toggleSceneSelection(index);
                            }}
                            className="form-checkbox h-4 w-4 text-black border-2 border-black"
                          />
                          <span className="ml-2 text-xs">Include in import</span>
                        </div>
                        <div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              viewSceneDetail(index);
                            }}
                            className="text-xs border border-black"
                          >
                            View Full Scene
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  variant="ghost"
                  onClick={() => setParsedScenes([])}
                  className="border-2 border-black"
                >
                  Back
                </Button>
                <Button onClick={handleImport} className="border-2 border-black">
                  <FileText className="h-4 w-4 mr-2" />
                  Import {selectedScenes.size} Scenes
                </Button>
              </div>
            </>
          ) : (
            <>
              {selectedPreviewScene !== null && (
                <>
                  <div className="mb-4">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="font-bold">Scene Preview</h3>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={backToList}
                        className="text-xs border border-black"
                      >
                        Back to List
                      </Button>
                    </div>
                    <div className="border-4 border-black p-4 h-64 overflow-y-auto">
                      <div className="font-bold mb-2">{parsedScenes[selectedPreviewScene].heading}</div>
                      <div className="font-mono text-sm whitespace-pre-wrap">
                        {parsedScenes[selectedPreviewScene].content}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex justify-between">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedScenes.has(selectedPreviewScene)}
                        onChange={() => toggleSceneSelection(selectedPreviewScene)}
                        className="form-checkbox h-4 w-4 text-black border-2 border-black mr-2"
                      />
                      <span>Include this scene in import</span>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        onClick={backToList}
                        className="border-2 border-black"
                      >
                        Back to List
                      </Button>
                      <Button 
                        onClick={handleImport} 
                        className="border-2 border-black"
                      >
                        <FileText className="h-4 w-4 mr-2" />
                        Import {selectedScenes.size} Scenes
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </>
          )
        )}
      </div>
    </div>
  );
};