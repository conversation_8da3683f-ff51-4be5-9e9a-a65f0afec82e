import { create } from 'zustand';
import { logger } from '../utils/logger';
import { getRandomColor, releaseColor } from '../utils/colorUtils';
import type { StorylineCard, DropTarget } from '../types/timeline';
import type { Story } from '../types/workshop';

// Default to 6 episodes
const DEFAULT_EPISODES: string[] = ['EP_01', 'EP_02', 'EP_03', 'EP_04', 'EP_05', 'EP_06'];

const DEFAULT_STORYLINES = [
  { id: 'storyline-1', title: 'Main Plot', color: '#FF3AF2', episodes: {}, cards: [] },
  { id: 'storyline-2', title: 'Character Arc', color: '#00FFF0', episodes: {}, cards: [] },
  { id: 'storyline-3', title: 'Subplot', color: '#FFE600', episodes: {}, cards: [] }
];

const defaultSections = [
  { id: 'setup', title: 'Setup', color: 'bg-blue-500', guide: 'Establish the world and characters' },
  { id: 'conflict', title: 'Conflict', color: 'bg-red-500', guide: 'Introduce the main conflict' },
  { id: 'rising', title: 'Rising Action', color: 'bg-yellow-500', guide: 'Escalate the stakes' },
  { id: 'climax', title: 'Climax', color: 'bg-purple-500', guide: 'The highest point of tension' },
  { id: 'falling', title: 'Falling Action', color: 'bg-green-500', guide: 'Show the aftermath' },
  { id: 'resolution', title: 'Resolution', color: 'bg-gray-500', guide: 'Wrap up loose ends' }
];

const MOCK_CARDS: (StorylineCard & { type: 'card' })[] = [
  {
    id: 'mock-1',
    content: 'Character development arc for the protagonist',
    source: 'manual',
    color: 'white',
    sectionId: 'setup',
    position: 0,
    storylineId: undefined,
    episodeId: undefined,
    type: 'card'
  },
  {
    id: 'mock-2',
    content: 'Major plot twist in episode 3',
    source: 'manual',
    color: 'white',
    sectionId: 'conflict',
    position: 0,
    storylineId: undefined,
    episodeId: undefined,
    type: 'card'
  },
  {
    id: 'mock-3',
    content: 'Subplot involving secondary characters',
    source: 'manual',
    color: 'white',
    sectionId: 'rising',
    position: 0,
    storylineId: undefined,
    episodeId: undefined,
    type: 'card'
  }
];

export const MOCK_PLOT_POINTS: (StorylineCard & { type: 'plot-point' })[] = defaultSections.map(section => ({
  id: section.id,
  content: section.guide,
  source: 'manual',
  color: 'white',
  sectionId: section.id,
  position: 0,
  storylineId: undefined,
  episodeId: undefined,
  type: 'plot-point'
}));

export const useEpisodeTrackStore = create<{
  isCollapsed: boolean;
  storylines: {
    id: string;
    title: string;
    color: string;
    episodes: Record<string, boolean>;
    cards: StorylineCard[];
  }[];
  unassignedCards: StorylineCard[];
  stories: Story[];
  episodes: string[];
  height: number;
  episodeTrack: { isExpanded: boolean };
  colorLegend: { isExpanded: boolean; isVisible: boolean };
  isColorLegendMinimized: boolean;
  addStoryline: (title: string) => void;
  updateStoryline: (id: string, updates: Partial<Omit<StorylineCard, 'id'>>) => void;
  removeStoryline: (id: string) => void;
  toggleEpisode: (storylineId: string, episodeId: string) => void;
  addCard: (card: Omit<StorylineCard, 'id'>) => string;
  createPlotPoint: (options: Partial<StorylineCard>) => string;
  moveCard: (cardId: string, target: DropTarget | { type: 'workshop'; quadrantId: string; position: number }) => void;
  removeCard: (cardId: string) => void;
  deleteAllCards: () => void;
  toggleEpisodeTrackExpanded: () => void;
  toggleColorLegendExpanded: () => void;
  setColorLegendVisible: (visible: boolean) => void;
  minimizeColorLegend: (minimized: boolean) => void;
  setHeight: (height: number) => void;
  addEpisode: (episodeId: string) => void;
  removeEpisode: (episodeId: string) => void;
  updateStorylineForScene: (sceneId: string, storylineId: string | null) => void;
}>((set, get) => ({
  isCollapsed: false,
  storylines: [...DEFAULT_STORYLINES],
  unassignedCards: [...MOCK_CARDS, ...MOCK_PLOT_POINTS],
  stories: [],
  episodes: [...DEFAULT_EPISODES],
  height: 300,
  episodeTrack: { isExpanded: false },
  colorLegend: { isExpanded: true, isVisible: true },
  isColorLegendMinimized: true,

  addStoryline: (title) => {
    const id = `storyline-${Date.now()}-${Math.random().toString(36).slice(2)}`;
    const color = getRandomColor();
    set((state) => ({
      storylines: [...state.storylines, { id, title, color, episodes: {}, cards: [] }]
    }));
    logger.debug('Storyline added:', { id, title, color });
  },

  updateStoryline: (id, updates) => {
    set((state) => {
      const storyline = state.storylines.find(s => s.id === id);
      if (updates.color && storyline) releaseColor(storyline.color);
      return {
        storylines: state.storylines.map(s => s.id === id ? { ...s, ...updates } : s)
      };
    });
  },

  removeStoryline: (id) => {
    set((state) => {
      // Release the color when removing a storyline
      const storyline = state.storylines.find(s => s.id === id);
      if (storyline) releaseColor(storyline.color);
      
      return {
        storylines: state.storylines.filter(s => s.id !== id)
      };
    });
  },

  toggleEpisode: (storylineId, episodeId) => set((state) => ({
    storylines: state.storylines.map(storyline =>
      storyline.id === storylineId
        ? {
            ...storyline,
            episodes: {
              ...storyline.episodes,
              [episodeId]: !storyline.episodes[episodeId]
            }
          }
        : storyline
    )
  })),

  addCard: (card) => {
    const id = `card-${Date.now()}-${Math.random().toString(36).slice(2)}`;
    const newCard = { ...card, id };
    
    // Add new cards to unassignedCards (color legend)
    set((state) => {
      // Check if a similar card already exists by content
      const duplicateExists = state.unassignedCards.some(
        c => c.content === card.content && c.sectionId === card.sectionId
      );

      if (duplicateExists) {
        console.log('Similar card already exists, not adding duplicate');
        return state; // No change to state
      }

      // Card is unique, add it
      console.log('Adding new card to color legend:', { id, card });
      return {
        unassignedCards: [...state.unassignedCards, newCard]
      };
    });
    
    return id;
  },

  createPlotPoint: (options) => {
    const id = `plot-${Date.now()}-${Math.random().toString(36).slice(2)}`;
    const newCard: StorylineCard = {
      id,
      content: options.content ?? '',
      source: 'manual',
      color: options.color ?? 'white',
      sectionId: options.sectionId ?? 'setup',
      position: options.position ?? 0,
      storylineId: options.storylineId,
      episodeId: options.episodeId,
      type: 'plot-point'
    };
    
    // Add to unassignedCards (color legend)
    set((state) => {
      // Check if this plotPoint should be treated as a direct workshop add
      const isDirectWorkshopAdd = options.sectionId && options.sectionId.includes('-');

      // If direct workshop add, don't add to color legend
      if (isDirectWorkshopAdd) {
        console.log('Direct workshop add, not adding to color legend');
        return {
          unassignedCards: [...state.unassignedCards, newCard]
        };
      }

      // Otherwise check for duplicates
      const duplicateExists = state.unassignedCards.some(
        c => c.content === newCard.content && c.sectionId === newCard.sectionId
      );

      if (duplicateExists) {
        console.log('Similar plot point already exists, not adding duplicate');
        return state; // No change to state
      }

      // Plot point is unique, add it
      console.log('Creating plot point in color legend:', { id, options });
      return {
        unassignedCards: [...state.unassignedCards, newCard]
      };
    });
    
    return id;
  },

  moveCard: (cardId, target) => {
    // Use setState function to ensure we're working with the latest state
    set(state => {
      console.log('moveCard called with:', { cardId, target });
      console.log('Current card count:', state.unassignedCards.length);
      
      // Find the card in unassignedCards or storylines
      let foundCard = state.unassignedCards.find(c => c.id === cardId);
      let cardSource = 'unassigned';
      
      if (!foundCard) {
        for (const storyline of state.storylines) {
          const card = storyline.cards.find(c => c.id === cardId);
          if (card) {
            foundCard = card;
            cardSource = storyline.id;
            break;
          }
        }
      }
      
      if (!foundCard) {
        console.error('Card not found:', cardId);
        return state; // Return state unchanged if card not found
      }
      
      console.log('Found card:', foundCard, 'Source:', cardSource);
      
      // Store original state to revert to if cards get lost
      const originalState = JSON.parse(JSON.stringify(state));
      const originalCardCount = state.unassignedCards.length + 
        state.storylines.reduce((sum, s) => sum + s.cards.length, 0);
      
      // Create a deep copy of the current state to work with
      let newUnassignedCards = JSON.parse(JSON.stringify(state.unassignedCards));
      let newStorylines = JSON.parse(JSON.stringify(state.storylines));
      
      // Step 1: Remove the card from its current location
      if (cardSource === 'unassigned') {
        newUnassignedCards = newUnassignedCards.filter(c => c.id !== cardId);
      } else {
        // Card is in a storyline
        newStorylines = newStorylines.map(storyline => {
          if (storyline.id === cardSource) {
            return {
              ...storyline,
              cards: storyline.cards.filter(c => c.id !== cardId)
            };
          }
          return storyline;
        });
      }
      
      // Step 2: Create copy of the card with updated props for new location
      const cardCopy = JSON.parse(JSON.stringify(foundCard));
      
      // Step 3: Move the card to its new location based on target
      if (target.type === 'workshop') {
        const targetSectionId = target.quadrantId;
        const targetPosition = target.position !== undefined ? target.position : 0;
        
        // Update card properties for workshop target
        cardCopy.sectionId = targetSectionId;
        cardCopy.position = targetPosition;
        cardCopy.storylineId = undefined;
        cardCopy.episodeId = undefined;
        cardCopy.color = 'white';
        
        // Count existing cards in this section before we add the new one
        const cardsInSection = newUnassignedCards.filter(c => c.sectionId === targetSectionId).length;
        console.log(`Target section ${targetSectionId} currently has ${cardsInSection} cards`);
        
        // Add the card to workshop - no longer adding to unassignedCards
        // Instead, it will be tracked in the workshop component directly
        console.log(`Card moved to workshop section ${targetSectionId}, position ${targetPosition}`);
        
        // IMPORTANT: We're NOT adding the card back to unassignedCards when it goes to workshop
        // This fixes the issue where unassigned count doesn't decrease when cards move to workshop
      } else if (target.type === 'storyline' && target.storylineId) {
        // Find the target storyline
        const targetStorylineIndex = newStorylines.findIndex(s => s.id === target.storylineId);
        
        if (targetStorylineIndex !== -1) {
          // Update card properties for storyline target
          cardCopy.storylineId = target.storylineId;
          cardCopy.episodeId = target.episodeId;
          cardCopy.position = target.position || 0;
          cardCopy.color = newStorylines[targetStorylineIndex].color;
          
          // Add the card to the storyline
          newStorylines[targetStorylineIndex] = {
            ...newStorylines[targetStorylineIndex],
            cards: [...newStorylines[targetStorylineIndex].cards, cardCopy]
          };
          
          console.log('Moved card to storyline:', {
            storylineId: target.storylineId,
            episodeId: target.episodeId,
            position: cardCopy.position
          });
        }
      } else {
        // For any other target, add to unassigned cards with default props
        cardCopy.storylineId = undefined;
        cardCopy.episodeId = undefined;
        cardCopy.color = 'white';
        cardCopy.position = newUnassignedCards.length; // Put at end
        
        newUnassignedCards.push(cardCopy);
      }
      
      const newState = {
        ...state,
        unassignedCards: newUnassignedCards,
        storylines: newStorylines
      };
      
      // Verify we haven't lost any cards (except for workshop moves)
      // For workshop moves, total count should decrease by 1 as we're removing from unassignedCards
      const newCardCount = newState.unassignedCards.length + 
        newState.storylines.reduce((sum, s) => sum + s.cards.length, 0);
      
      console.log('Card count verification:', {
        before: originalCardCount,
        after: newCardCount,
        difference: newCardCount - originalCardCount
      });
      
      // For workshop moves, we expect the total count to decrease by 1
      // For storyline moves, the total count should remain the same
      const isWorkshopMove = target.type === 'workshop';
      const expectedCountChange = isWorkshopMove ? -1 : 0;
      
      // Check if count decreased unexpectedly (more than expected for workshop move)
      if (newCardCount < originalCardCount + expectedCountChange) {
        console.error('CRITICAL ERROR: Unexpected card count decrease! Reverting to prevent data loss.');
        return originalState;
      }
      
      return newState;
    });
  },

  removeCard: (cardId) => {
    set((state) => ({
      unassignedCards: state.unassignedCards.filter(c => c.id !== cardId),
      storylines: state.storylines.map(s => ({
        ...s,
        cards: s.cards.filter(c => c.id !== cardId)
      }))
    }));
  },

  deleteAllCards: () => {
    set((state) => ({
      unassignedCards: [],
      storylines: state.storylines.map(s => ({ ...s, cards: [] }))
    }));
  },

  toggleEpisodeTrackExpanded: () => {
    set((state) => ({
      episodeTrack: { ...state.episodeTrack, isExpanded: !state.episodeTrack.isExpanded }
    }));
  },

  toggleColorLegendExpanded: () => {
    set((state) => ({
      colorLegend: { ...state.colorLegend, isExpanded: !state.colorLegend.isExpanded }
    }));
  },

  setColorLegendVisible: (visible) => {
    set((state) => ({
      colorLegend: { ...state.colorLegend, isVisible: visible }
    }));
  },

  minimizeColorLegend: (minimized) => {
    set(() => ({
      isColorLegendMinimized: minimized
    }));
  },

  setHeight: (height) => {
    set(() => ({ height }));
  },

  addEpisode: (episodeId) => {
    set((state) => ({
      episodes: [...state.episodes, episodeId]
    }));
  },

  removeEpisode: (episodeId) => {
    set((state) => ({
      episodes: state.episodes.filter(id => id !== episodeId)
    }));
  },

  updateStorylineForScene: (sceneId, storylineId) => {
    logger.debug('updateStorylineForScene not yet implemented', sceneId, storylineId);
  }
}));