import React from 'react';
import { cn } from '../../lib/utils';

interface PromptsLayoutProps {
  children: React.ReactNode;
  chat: React.ReactNode;
}

export const PromptsLayout: React.FC<PromptsLayoutProps> = ({ children, chat }) => {
  return (
    <div className="h-[calc(100vh-4rem)] flex overflow-hidden">
      <main className="flex-1 overflow-y-auto">
        {children}
      </main>
      <aside className="w-96 border-l-4 border-black bg-white flex flex-col">
        {chat}
      </aside>
    </div>
  );
};