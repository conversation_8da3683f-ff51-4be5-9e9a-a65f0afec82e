import React, { useState, useCallback } from 'react';
import { Clock, ArrowRight, Trash2, Users } from 'lucide-react';
import { Card, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { cn } from '../../lib/utils';
import { useProject } from '../../contexts/ProjectContext';
import type { Project } from '../../types/project';

interface ProjectCardProps {
  project: Project;
  onClick: (project: Project) => void;
  isDeleting?: boolean;
  // We'll handle deletion internally, so we don't need onDelete prop
  onDeleteStart?: (projectId: string) => void; // Optional callback for parent to track deletion status
  onDeleteEnd?: (projectId: string, success: boolean) => void; // Optional callback for parent to track completion
}

export const ProjectCard: React.FC<ProjectCardProps> = ({ 
  project, 
  onClick, 
  isDeleting = false,
  onDeleteStart,
  onDeleteEnd
}) => {
  const { deleteProject } = useProject(); // Get deleteProject directly from context
  const [isDeleting_internal, setIsDeleting_internal] = useState(false);
  
  // Use an internal state for deleting if no external state is provided
  const effectiveIsDeleting = isDeleting || isDeleting_internal;
  
  const handleDelete = useCallback(async (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (confirm(`Are you sure you want to delete "${project.name}"?`)) {
      try {
        // Track deletion status internally if needed
        setIsDeleting_internal(true);
        
        // Notify parent component of deletion start if callback provided
        if (onDeleteStart) {
          onDeleteStart(project.id);
        }
        
        // Call deleteProject directly from context
        await deleteProject(project.id);
        
        // Notify parent of successful completion if callback provided
        if (onDeleteEnd) {
          onDeleteEnd(project.id, true);
        }
      } catch (error) {
        console.error('Error deleting project:', error);
        alert(`Error deleting project: ${error instanceof Error ? error.message : 'Unknown error'}`);
        
        // Notify parent of failed completion if callback provided
        if (onDeleteEnd) {
          onDeleteEnd(project.id, false);
        }
      } finally {
        // Reset internal deletion state
        setIsDeleting_internal(false);
      }
    }
  }, [project, deleteProject, onDeleteStart, onDeleteEnd]);

  const statusColors = {
    active: 'bg-green-100 text-green-800',
    draft: 'bg-yellow-100 text-yellow-800',
    completed: 'bg-blue-100 text-blue-800',
  };

  const typeLabels = {
    'tv-30': '30-Minute Show',
    'tv-60': '60-Minute Show',
    'film': 'Feature Film',
    'novel': 'Novel'
  };

  return (
    <Card className="border-4 border-black hover:shadow-lg transition-all">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-bold text-lg">{project.name}</h4>
            <div className="flex items-center gap-4 text-sm text-gray-500">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                {new Date(project.lastModified).toLocaleDateString()}
              </div>
              <span className="text-gray-400">•</span>
              <span>{typeLabels[project.type]}</span>
            </div>
            {project.description && (
              <p className="text-sm text-gray-600 mt-2">{project.description}</p>
            )}
            {project.collaborators && project.collaborators.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {project.collaborators.map(collab => (
                  <span key={collab} className="text-xs bg-gray-100 px-2 py-1 rounded">
                    {collab}
                  </span>
                ))}
              </div>
            )}
          </div>
          <div className="flex items-center gap-2">
            <span className={cn(
              'px-2 py-1 rounded text-sm font-medium',
              statusColors[project.status]
            )}>
              {effectiveIsDeleting ? 'Deleting...' : project.status}
            </span>
            <div className="flex gap-1">
              <Button
                size="sm"
                variant="ghost"
                onClick={handleDelete}
                className={cn(
                  "hover:bg-red-100 text-red-500",
                  effectiveIsDeleting && "opacity-50 cursor-not-allowed"
                )}
                disabled={effectiveIsDeleting}
                title="Delete Project"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  // Dispatch a custom event to open project settings
                  window.dispatchEvent(new CustomEvent('openProjectSettings', {
                    detail: { projectId: project.id }
                  }));
                }}
                className="hover:bg-gray-100"
                title="Project Settings"
              >
                <Users className="h-4 w-4" />
              </Button>
              <Button 
                size="sm" 
                onClick={() => onClick(project)}
                className="hover:bg-gray-100"
              >
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};