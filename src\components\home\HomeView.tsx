import React, { useState, useCallback, useEffect } from 'react';
import { Plus, FolderOpen, Lightbulb } from 'lucide-react';
import { Button } from '../ui/button';
import { ProjectCard } from './ProjectCard';
import { ProjectModal } from './ProjectModal';
import { ProjectSettings } from './ProjectSettings';
import { useProject } from '../../contexts/ProjectContext';
import { useNavigation } from '../../contexts/NavigationContext';

import type { Project } from '../../types/project';
import type { ViewType } from '../../constants/navigation';

interface HomeViewProps {
  onNavigate: (view: ViewType) => void; 
}

export const HomeView: React.FC<HomeViewProps> = ({ onNavigate }) => {
  const { projects, createProject, openProject } = useProject();
  const { setCurrentView } = useNavigation();
  
  const [modalType, setModalType] = useState<Project['type'] | null>(null);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  // Listen for the custom event to open project settings
  useEffect(() => {
    const handleOpenSettings = () => {
      setIsSettingsOpen(true);
    };
    
    window.addEventListener('openProjectSettings', handleOpenSettings);
    
    return () => {
      window.removeEventListener('openProjectSettings', handleOpenSettings);
    };
  }, []);

  const handleCreateProject = (name: string, type: Project['type']) => {
    console.log('Creating project:', { name, type });
    createProject(name, type);
    setModalType(null);
    setCurrentView('workshop');
  };

  const handleOpenProject = (project: Project) => {
    console.log('Opening project:', project);
    
    // Make sure we're using the classic template
    const projectToOpen = {
      ...project,
      template: 'classic'
    };
    
    // Open the project
    openProject(projectToOpen);
    
    // Set the current view to the Write module
    setCurrentView('write');
    
    // We no longer need to expand all acts automatically
    // Let the useWriteNavigation hook restore the previous state
  };

  // These callbacks will let us track deletion state without having to pass the delete function
  const handleDeleteStart = useCallback((projectId: string) => {
    setIsDeleting(projectId);
    setDeleteError(null);
  }, []);

  const handleDeleteEnd = useCallback((projectId: string, success: boolean) => {
    setIsDeleting(null);
    if (!success) {
      setDeleteError(`Failed to delete project. Please try again.`);
    }
  }, []);

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center gap-2">
            <FolderOpen className="h-8 w-8" />
            <h1 className="text-2xl font-bold">Story Projects</h1>
          </div>
          <div className="flex gap-2">
            <Button onClick={() => setModalType('tv-30')}>
              <Plus className="h-4 w-4 mr-2" />
              30-Min Show
            </Button>
            <Button onClick={() => setModalType('tv-60')}>
              <Plus className="h-4 w-4 mr-2" />
              60-Min Show
            </Button>
            <Button onClick={() => setModalType('film')}>
              <Plus className="h-4 w-4 mr-2" />
              Feature Film
            </Button>
          </div>
        </div>

        {deleteError && (
          <div className="mb-4 p-4 bg-red-100 border-l-4 border-red-500 text-red-700">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm">{deleteError}</p>
              </div>
              <div className="ml-auto pl-3">
                <div className="-mx-1.5 -my-1.5">
                  <button
                    onClick={() => setDeleteError(null)}
                    className="inline-flex rounded-md p-1.5 text-red-500 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-600 focus:ring-offset-2"
                  >
                    <span className="sr-only">Dismiss</span>
                    <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="space-y-6">
            <section>
              <h2 className="text-xl font-bold mb-4">Recent Projects</h2>
              <div className="space-y-4">
                {projects.length === 0 ? (
                  <div className="bg-white border-4 border-black p-6 rounded-lg">
                    <p className="text-gray-500 text-center">No recent projects</p>
                  </div>
                ) : (
                  projects.map(project => (
                    <ProjectCard
                      key={project.id}
                      project={project}
                      onOpen={handleOpenProject}
                      isDeleting={isDeleting === project.id}
                      onDeleteStart={handleDeleteStart}
                      onDeleteEnd={handleDeleteEnd}
                    />
                  ))
                )}
              </div>
            </section>
          </div>

          <div className="space-y-6">
            <section>
              <h2 className="text-xl font-bold mb-4">Quick Access</h2>
              <div className="grid grid-cols-2 gap-4">
                <Button 
                  className="h-32 border-4 border-black hover:bg-gray-50"
                  onClick={() => onNavigate('ideapile')}
                >
                  <div className="flex flex-col items-center gap-2">
                    <Lightbulb className="h-8 w-8" />
                    <span className="font-bold">Idea Pile</span>
                    <span className="text-sm text-gray-600">Collect and organize ideas</span>
                  </div>
                </Button>
              </div>
            </section>
          </div>
        </div>
      </div>

      <ProjectModal
        type={modalType}
        isOpen={modalType !== null}
        onClose={() => setModalType(null)}
        onCreateProject={handleCreateProject}
      />
      
      <ProjectSettings
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
      />
    </div>
  );
};