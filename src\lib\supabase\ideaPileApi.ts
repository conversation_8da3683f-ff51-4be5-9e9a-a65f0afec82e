import { supabase } from './client';
import type { IdeaItem, IdeaTag, IdeaReference } from '../../types/ideaPile';
import { logger } from '../../utils/logger';

export const ideaPileApi = {
  // Core idea management
  async getIdeas(filters?: {
    category?: string;
    status?: string;
    tags?: string[];
    search?: string;
  }) {
    try {
      let query = supabase
        .from('idea_items')
        .select(`
          *,
          tags:idea_tags(tag),
          references:idea_references(*)
        `);

      // Apply filters
      if (filters?.category) {
        query = query.eq('category', filters.category);
      }
      if (filters?.status) {
        query = query.eq('status', filters.status);
      }
      if (filters?.search) {
        query = query.or(`title.ilike.%${filters.search}%,content.ilike.%${filters.search}%`);
      }
      if (filters?.tags?.length) {
        // This is a simplification - proper tag filtering would be more complex
        query = query.or(filters.tags.map(tag => `tags.tag.eq.${tag}`).join(','));
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Failed to fetch ideas:', error);
      throw error;
    }
  },

  async createIdea(idea: Omit<IdeaItem, 'id' | 'created_at' | 'updated_at'>) {
    try {
      const { data, error } = await supabase
        .from('idea_items')
        .insert([idea])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Failed to create idea:', error);
      throw error;
    }
  },

  async updateIdea(id: string, updates: Partial<IdeaItem>) {
    try {
      const { data, error } = await supabase
        .from('idea_items')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Failed to update idea:', error);
      throw error;
    }
  },

  async deleteIdea(id: string) {
    try {
      const { error } = await supabase
        .from('idea_items')
        .delete()
        .eq('id', id);

      if (error) throw error;
    } catch (error) {
      logger.error('Failed to delete idea:', error);
      throw error;
    }
  },

  // Tag management
  async addTag(ideaId: string, tag: string) {
    try {
      const { data, error } = await supabase
        .from('idea_tags')
        .insert([{ idea_id: ideaId, tag }])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Failed to add tag:', error);
      throw error;
    }
  },

  async removeTag(ideaId: string, tag: string) {
    try {
      const { error } = await supabase
        .from('idea_tags')
        .delete()
        .eq('idea_id', ideaId)
        .eq('tag', tag);

      if (error) throw error;
    } catch (error) {
      logger.error('Failed to remove tag:', error);
      throw error;
    }
  },

  // Project references
  async addReference(reference: Omit<IdeaReference, 'id' | 'created_at'>) {
    try {
      const { data, error } = await supabase
        .from('idea_references')
        .insert([reference])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Failed to add reference:', error);
      throw error;
    }
  },

  async removeReference(id: string) {
    try {
      const { error } = await supabase
        .from('idea_references')
        .delete()
        .eq('id', id);

      if (error) throw error;
    } catch (error) {
      logger.error('Failed to remove reference:', error);
      throw error;
    }
  }
};