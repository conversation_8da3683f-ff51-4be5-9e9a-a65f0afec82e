import React, { createContext, useContext, useState, useCallback, ReactNode, useEffect } from 'react';
import { useProject } from './ProjectContext';

interface EpisodeSpineContextType {
  openEpisodes: Set<number>;
  toggleEpisode: (ep: number) => void;
  setOpenEpisodes: (episodes: Set<number>) => void;
}

const EpisodeSpineContext = createContext<EpisodeSpineContextType | undefined>(undefined);

export const useEpisodeSpine = () => {
  const context = useContext(EpisodeSpineContext);
  if (!context) {
    throw new Error('useEpisodeSpine must be used within an EpisodeSpineProvider');
  }
  return context;
};

export const EpisodeSpineProvider = ({ children }: { children: ReactNode }) => {
  const { currentProject } = useProject();
  const projectId = currentProject?.id || 'default';
  const projectType = currentProject?.type || 'tv-30';
  
  // Define default episodes based on project type
  const getDefaultOpenEpisodes = () => {
    if (projectType === 'film') {
      return new Set([1]); // For film, only one episode
    } else if (projectType === 'novel') {
      return new Set([1, 2, 3]); // For novel, first 3 chapters
    } else {
      return new Set([1, 2, 3, 4]); // For TV, first 4 episodes
    }
  };

  const storageKey = `openEpisodes_${projectId}`;

  const [openEpisodes, setOpenEpisodesState] = useState<Set<number>>(() => {
    const saved = localStorage.getItem(storageKey);
    if (saved) {
      return new Set(JSON.parse(saved));
    }
    return getDefaultOpenEpisodes();
  });

  // Reset episodes when project changes
  useEffect(() => {
    // When project changes, load episodes for that project
    const saved = localStorage.getItem(storageKey);
    if (saved) {
      setOpenEpisodesState(new Set(JSON.parse(saved)));
    } else {
      setOpenEpisodesState(getDefaultOpenEpisodes());
    }
  }, [projectId, projectType]);

  useEffect(() => {
    localStorage.setItem(storageKey, JSON.stringify(Array.from(openEpisodes)));
  }, [openEpisodes, storageKey]);

  const toggleEpisode = useCallback((ep: number) => {
    setOpenEpisodesState(prev => {
      const next = new Set(prev);
      if (next.has(ep)) {
        next.delete(ep);
      } else {
        next.add(ep);
      }
      return next;
    });
  }, []);

  const setOpenEpisodes = useCallback((episodes: Set<number>) => {
    setOpenEpisodesState(new Set(episodes));
  }, []);

  return (
    <EpisodeSpineContext.Provider value={{ openEpisodes, toggleEpisode, setOpenEpisodes }}>
      {children}
    </EpisodeSpineContext.Provider>
  );
};