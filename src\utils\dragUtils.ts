/**
 * Safely parses JSON data from a drag event
 * @param e The drag event
 * @param dataType The data type to retrieve (default: 'application/json')
 * @returns The parsed data or null if parsing fails
 */
export function safelyParseJsonFromDrag<T = any>(e: React.DragEvent, dataType = 'application/json'): T | null {
  try {
    // First check if the dataTransfer contains our expected format
    if (!e.dataTransfer.types.includes(dataType)) {
      return null;
    }
    
    // Get the data as string
    const jsonData = e.dataTransfer.getData(dataType);
    if (!jsonData) {
      return null;
    }
    
    // Parse the JSON data
    return JSON.parse(jsonData) as T;
  } catch (error) {
    console.error('Failed to parse drag data:', error);
    return null;
  }
}

/**
 * Safely sets JSON data for a drag event
 * @param e The drag event
 * @param data The data to set
 * @param dataType The data type to set (default: 'application/json')
 * @returns true if successful, false otherwise
 */
export function safelySetJsonForDrag(e: React.DragEvent, data: any, dataType = 'application/json'): boolean {
  try {
    const jsonString = JSON.stringify(data);
    e.dataTransfer.setData(dataType, jsonString);
    return true;
  } catch (error) {
    console.error('Failed to set drag data:', error);
    return false;
  }
}