import React, { useState, useEffect, useRef } from 'react';
import { Minimize2, MessageSquare, SplitSquareHorizontal, Trash2, ChevronDown, ChevronUp, Save, Plus, Upload, List } from 'lucide-react';
import { Button } from '../ui/button';
import { Card, CardHeader, CardContent } from '../ui/card';
import { ModelSelector } from './ModelSelector';
import { PersonaSelector } from './PersonaSelector';
import { TextSplitter } from './TextSplitter';
import { ChatMessage } from './ChatMessage';
import { useChat } from '../../contexts/ChatContext';
import { useEpisodeTrackStore } from '../../store/episodeTrackStore';
import { cn } from '../../lib/utils';

/**
 * EnhancedChatWindow is a simplified version of ChatWindow designed specifically
 * for use in the PromptsLayout as a sidebar component.
 */
export const EnhancedChatWindow: React.FC = () => {
  const {
    messages,
    addMessage,
    selectedModel,
    setSelectedModel,
    selectedPersona,
    setSelectedPersona,
    saveChat,
    savedChats,
    loadChat,
    currentChatId,
    clearChat,
    createPromptFromChat,
    uploadContent,
    isLoading,
    useStreaming,
    setUseStreaming
  } = useChat();

  const { deleteAllCards } = useEpisodeTrackStore();
  
  const [input, setInput] = useState('');
  const [showTextSplitter, setShowTextSplitter] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [showSavedChats, setShowSavedChats] = useState(false);
  const [isSelectingMessages, setIsSelectingMessages] = useState(false);
  const [selectedMessages, setSelectedMessages] = useState<string[]>([]);
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [uploadText, setUploadText] = useState('');
  const [saveTitle, setSaveTitle] = useState('');
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSend = () => {
    if (!input.trim()) return;
    addMessage(input, 'user');
    setInput('');
  };

  const handleDeleteAllCards = () => {
    if (window.confirm('Are you sure you want to delete all cards from this session?')) {
      deleteAllCards();
    }
  };

  const toggleMinimized = () => {
    setIsMinimized(!isMinimized);
  };
  
  // Toggle message selection mode
  const toggleMessageSelection = () => {
    setIsSelectingMessages(!isSelectingMessages);
    if (isSelectingMessages) {
      setSelectedMessages([]);
    }
  };
  
  // Toggle a message selection
  const toggleMessageSelected = (messageId: string) => {
    if (selectedMessages.includes(messageId)) {
      setSelectedMessages(prev => prev.filter(id => id !== messageId));
    } else {
      setSelectedMessages(prev => [...prev, messageId]);
    }
  };
  
  // Create a prompt from selected messages
  const handleCreatePrompt = () => {
    if (selectedMessages.length === 0) return;
    
    const { title, content } = createPromptFromChat(selectedMessages);
    
    // Dispatch custom event to create a prompt
    const createPromptEvent = new CustomEvent('create-prompt-from-chat', {
      detail: {
        title,
        details: content,
        groupId: '' // Let the PromptsView determine the group
      }
    });
    
    window.dispatchEvent(createPromptEvent);
    
    // Reset selection mode
    setIsSelectingMessages(false);
    setSelectedMessages([]);
    
    // Give user feedback
    alert(`Prompt "${title}" has been created! Switch to the Prompts tab to view it.`);
  };
  
  // Handle file upload
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    try {
      const text = await file.text();
      setUploadText(text);
      setShowUploadDialog(true);
    } catch (error) {
      console.error('Error reading file:', error);
      alert('Failed to read file. Please try again.');
    }
  };
  
  // Submit uploaded content to chat
  const handleUploadSubmit = () => {
    if (!uploadText.trim()) return;
    
    uploadContent(uploadText);
    setShowUploadDialog(false);
    setUploadText('');
  };
  
  // Handle saving chat
  const handleSaveChat = () => {
    if (showSaveDialog) {
      const title = saveTitle.trim() || undefined;
      saveChat(title);
      setSaveTitle('');
      setShowSaveDialog(false);
    } else {
      setShowSaveDialog(true);
    }
  };
  
  // Start a new chat
  const handleNewChat = () => {
    if (messages.length > 1) {
      if (window.confirm('Save current chat before starting a new one?')) {
        handleSaveChat();
      }
    }
    clearChat();
  };

  return (
    <div className="h-full flex flex-col">
      <Card className="flex-1 flex flex-col border-0 shadow-none">
        <CardHeader className="border-b-4 border-black p-3 bg-gradient-to-r from-gray-50 to-gray-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <h3 className="font-bold">AI Assistant</h3>
              
              {currentChatId && (
                <span className="text-xs bg-gray-100 px-1 rounded">
                  Saved
                </span>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDeleteAllCards}
                className="h-8 w-8 p-1 text-red-500 hover:text-red-600 hover:bg-red-50"
                title="Delete all cards"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowTextSplitter(!showTextSplitter)}
                className="h-8 w-8 p-1"
                title="Text Splitter"
              >
                <SplitSquareHorizontal className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleMinimized}
                className="h-8 w-8 p-1"
                title={isMinimized ? "Expand" : "Minimize"}
              >
                {isMinimized ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronUp className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
          {!isMinimized && (
            <>
              <div className="flex gap-2 mt-2">
                <ModelSelector
                  value={selectedModel}
                  onChange={setSelectedModel}
                />
                <PersonaSelector
                  value={selectedPersona}
                  onChange={setSelectedPersona}
                />
                <div className="flex items-center ml-2">
                  <label className="flex items-center text-xs cursor-pointer">
                    <input 
                      type="checkbox" 
                      checked={useStreaming} 
                      onChange={(e) => setUseStreaming(e.target.checked)} 
                      className="mr-1 h-3 w-3"
                    />
                    Streaming
                  </label>
                </div>
              </div>
              
              <div className="flex gap-2 mt-2">
                <Button 
                  size="sm" 
                  variant="outline" 
                  className="flex-1"
                  onClick={handleNewChat}
                >
                  <Plus className="h-4 w-4 mr-2" /> New Chat
                </Button>
                <Button 
                  size="sm" 
                  variant="outline" 
                  className="flex-1"
                  onClick={handleSaveChat}
                >
                  <Save className="h-4 w-4 mr-2" /> Save Chat
                </Button>
                <Button 
                  size="sm" 
                  variant="outline" 
                  className="flex-1"
                  onClick={() => setShowSavedChats(!showSavedChats)}
                >
                  <List className="h-4 w-4 mr-2" /> History
                </Button>
              </div>
              
              {showSaveDialog && (
                <div className="mt-2 p-2 border-2 border-black rounded">
                  <h4 className="text-sm font-bold mb-2">Save Current Chat</h4>
                  <input
                    type="text"
                    value={saveTitle}
                    onChange={(e) => setSaveTitle(e.target.value)}
                    placeholder="Chat title (optional)"
                    className="w-full p-2 border-2 border-black mb-2 text-sm"
                  />
                  <div className="flex gap-2 justify-end">
                    <Button 
                      size="sm" 
                      variant="ghost"
                      onClick={() => setShowSaveDialog(false)}
                    >
                      Cancel
                    </Button>
                    <Button 
                      size="sm"
                      onClick={() => {
                        const chatId = saveChat(saveTitle.trim() || undefined);
                        setShowSaveDialog(false);
                        setSaveTitle('');
                      }}
                    >
                      Save
                    </Button>
                  </div>
                </div>
              )}
              
              {showSavedChats && savedChats.length > 0 && (
                <div className="mt-2 max-h-40 overflow-y-auto border-2 border-black rounded p-2">
                  <h4 className="text-sm font-bold mb-2">Saved Chats</h4>
                  <div className="space-y-2">
                    {savedChats.map((chat) => (
                      <div 
                        key={chat.id} 
                        className={cn(
                          "flex justify-between items-center p-2 text-sm rounded cursor-pointer hover:bg-gray-100",
                          currentChatId === chat.id && "bg-gray-200"
                        )}
                        onClick={() => {
                          if (currentChatId !== chat.id) {
                            loadChat(chat.id);
                            setShowSavedChats(false);
                          }
                        }}
                      >
                        <div className="truncate flex-1">
                          <span className="font-medium">{chat.title}</span>
                          <span className="text-xs text-gray-500 ml-2">
                            {new Date(chat.updatedAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </>
          )}
        </CardHeader>

        {!isMinimized && (
          <CardContent className="flex-1 overflow-hidden flex flex-col p-0">
            {showTextSplitter ? (
              <TextSplitter onClose={() => setShowTextSplitter(false)} />
            ) : (
              <>
                <div className="flex-1 overflow-y-auto p-4 space-y-4">
                  {isSelectingMessages && (
                    <div className="bg-yellow-100 p-2 rounded border-2 border-yellow-300 mb-4">
                      <div className="flex justify-between items-center">
                        <p className="text-sm">
                          Select messages to create a prompt ({selectedMessages.length} selected)
                        </p>
                        <div className="flex gap-2">
                          <Button 
                            size="sm" 
                            variant="ghost" 
                            onClick={toggleMessageSelection}
                          >
                            Cancel
                          </Button>
                          <Button 
                            size="sm" 
                            disabled={selectedMessages.length === 0}
                            onClick={handleCreatePrompt}
                          >
                            Create Prompt
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {messages.map((message) => (
                    <ChatMessage
                      key={message.id}
                      id={message.id}
                      content={message.content}
                      type={message.type}
                      isSelectable={isSelectingMessages}
                      isSelected={selectedMessages.includes(message.id)}
                      onSelect={toggleMessageSelected}
                      isStreaming={message.isStreaming}
                    />
                  ))}
                  
                  {isLoading && (
                    <div className="flex items-center space-x-2 p-3 bg-gray-100 rounded-lg border-2 border-black">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                      </div>
                      <span className="text-sm text-gray-500">AI is thinking...</span>
                    </div>
                  )}
                  
                  <div ref={messagesEndRef} />
                </div>

                <div className="border-t-4 border-black p-4">
                  {showUploadDialog ? (
                    <div className="space-y-2">
                      <textarea
                        value={uploadText}
                        onChange={(e) => setUploadText(e.target.value)}
                        className="w-full p-2 border-2 border-black h-32 font-mono text-sm"
                        placeholder="Review and edit uploaded content..."
                      />
                      <div className="flex justify-end gap-2">
                        <Button 
                          variant="ghost" 
                          onClick={() => {
                            setShowUploadDialog(false);
                            setUploadText('');
                          }}
                        >
                          Cancel
                        </Button>
                        <Button onClick={handleUploadSubmit}>
                          Send to Chat
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <>
                      <div className="flex gap-2 mb-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          className="flex-1"
                          onClick={() => fileInputRef.current?.click()}
                        >
                          <Upload className="h-4 w-4 mr-2" /> Upload
                        </Button>
                        <input
                          type="file"
                          ref={fileInputRef}
                          className="hidden"
                          accept=".txt,.md,.fountain"
                          onChange={handleFileUpload}
                        />
                        <Button 
                          variant="outline" 
                          size="sm"
                          className="flex-1"
                          onClick={toggleMessageSelection}
                        >
                          <Plus className="h-4 w-4 mr-2" /> Create Prompt
                        </Button>
                      </div>
                      <div className="flex gap-2">
                        <textarea
                          value={input}
                          onChange={(e) => setInput(e.target.value)}
                          className="flex-1 p-3 border-4 border-black resize-none h-24 font-mono text-sm"
                          placeholder="Ask me anything about your story..."
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                              e.preventDefault();
                              handleSend();
                            }
                          }}
                        />
                        <Button 
                          onClick={handleSend} 
                          className="self-end"
                          disabled={isLoading}
                        >
                          {isLoading ? 'Thinking...' : 'Send'}
                        </Button>
                      </div>
                    </>
                  )}
                </div>
              </>
            )}
          </CardContent>
        )}
      </Card>
    </div>
  );
};