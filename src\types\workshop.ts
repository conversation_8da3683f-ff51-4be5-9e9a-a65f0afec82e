export interface PlotPoint {
  id: string;
  content: string;
  sectionId: string;
  position?: number;
  source: 'workshop' | 'chat' | 'manual'; 
  color?: string;                         
  storylineId?: string;
  episodeId?: string;
  metadata?: Record<string, any>;
}

export interface Story {
  id: string;
  title: string;
  content: string;
  quadrantId: string;
}

export interface Section {
  id: string;
  title: string;
  color: string;
  guide: string;
}

export interface Quadrant {
  id: string;
  title: string;
}

export interface DragItem {
  id: string;
  type: 'plot-point' | 'storyline-card';
  sectionId?: string;
  content: string;
  source?: 'workshop' | 'chat' | 'manual';
}