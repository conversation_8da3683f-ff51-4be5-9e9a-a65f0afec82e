import React from 'react';
import { Square } from 'lucide-react';
import { Button } from './button';
import { useEpisodeTrackStore } from '../../store/episodeTrackStore';

interface ColorLegendToggleProps {
  collapsed?: boolean;
}

export const ColorLegendToggle: React.FC<ColorLegendToggleProps> = ({ collapsed }) => {
  const { colorLegend, setColorLegendVisible, minimizeColorLegend } = useEpisodeTrackStore();

  // If color legend is not visible, show a button to restore it
  if (!colorLegend.isVisible) {
    return (
      <Button
        variant="ghost"
        onClick={() => {
          setColorLegendVisible(true);
          if (collapsed) minimizeColorLegend(true);
          else minimizeColorLegend(false);
        }}
        className="fixed bottom-4 right-4 border-2 border-black shadow-md z-50"
        title="Show Color Legend"
      >
        <Square className="h-4 w-4 mr-2" />
        Show Legend
      </Button>
    );
  }

  return null;
};