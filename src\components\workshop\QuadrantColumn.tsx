import React, { useState, useEffect, useRef } from 'react';
import { ChevronRight, Maximize2, Minimize2 } from 'lucide-react';
import { Button } from '../ui/button';
import { cn } from '../../lib/utils';
import { PlotPointCard } from './PlotPointCard';
import { SectionHeader } from './SectionHeader';
import type { PlotPoint, DragItem } from '../../types/workshop';

/** SECTION INTERFACE **/
interface Section {
  id: string;
  title: string;
  subtitle?: string;
  color?: string;
}

interface QuadrantColumnProps {
  id: string;
  title: string;
  plotSections: Section[];
  plotPoints: PlotPoint[];
  isOpen: boolean;
  isExpanded: boolean;
  expandedQuadrant?: string | null;
  onToggle: () => void;
  onExpand: () => void;
  onCollapse: () => void;
  onAddPlotPoint: (sectionId: string) => void;
  onUpdatePlotPoint: (id: string, content: string) => void;
  onDeletePlotPoint: (id: string) => void;
  onUpdateSection: (sectionId: string, updates: { title?: string; subtitle?: string }) => void;
  onMovePoint?: (draggedItem: DragItem, targetSectionId: string, targetIndex: number) => void;
  style?: React.CSSProperties;
}

export const QuadrantColumn: React.FC<QuadrantColumnProps> = ({
  id,
  title,
  plotSections,
  plotPoints,
  isOpen,
  isExpanded,
  expandedQuadrant,
  onToggle,
  onExpand,
  onCollapse,
  onAddPlotPoint,
  onUpdatePlotPoint,
  onDeletePlotPoint,
  onUpdateSection,
  onMovePoint,
  style
}) => {
  const [emptySlotsMap, setEmptySlotsMap] = useState<Record<string, number>>({});
  const sectionsRef = useRef<Record<string, HTMLDivElement>>({});
  // Create refs for each cell position to improve drop target detection
  const cellRefs = useRef<Record<string, HTMLDivElement[]>>({});
  // Track overflow cards (cards beyond the 5 visible slots)
  const [overflowMap, setOverflowMap] = useState<Record<string, number>>({});

  // Track the section that's currently being dragged over
  const [activeDragSection, setActiveDragSection] = useState<{
    sectionId: string;
    targetIndex: number | null;
  } | null>(null);

  useEffect(() => {
    const newMap: Record<string, number> = {};
    const newOverflowMap: Record<string, number> = {};
    
    plotSections.forEach((section) => {
      const sectionPoints = plotPoints.filter(point => point.sectionId === section.id);
      // Calculate empty slots for the visual grid (max 5 slots)
      newMap[section.id] = Math.max(0, 5 - Math.min(sectionPoints.length, 5)); 
      
      // Track the number of overflow cards (those beyond the first 5)
      newOverflowMap[section.id] = Math.max(0, sectionPoints.length - 5);
      
      // Initialize cell refs for this section if not already done
      if (!cellRefs.current[section.id]) {
        cellRefs.current[section.id] = [];
      }
    });
    
    setEmptySlotsMap(newMap);
    setOverflowMap(newOverflowMap);
  }, [plotSections, plotPoints]);

  const handleAddPoint = (sectionId: string) => {
    onAddPlotPoint(sectionId);
  };

  // Improved function to calculate the drop index based on mouse position
  const getDropIndexFromPosition = (e: React.DragEvent, sectionId: string) => {
    const gridContainer = sectionsRef.current[sectionId];
    if (!gridContainer) return 0;

    // Get all cells (both filled and empty)
    const cells = Array.from(gridContainer.querySelectorAll('.card-slot, .empty-slot'));
    if (cells.length === 0) return 0;

    // Get mouse coordinates relative to the grid container
    const rect = gridContainer.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    
    // Determine grid dimensions
    const containerWidth = rect.width;
    const cellWidth = containerWidth / 5; // Assuming 5 fixed columns
    
    // Calculate the column (0-4) where the mouse is
    const columnIndex = Math.min(Math.floor(mouseX / cellWidth), 4);
    
    console.log(`Mouse at position: x=${mouseX}, calculated column: ${columnIndex}`);
    
    // Return the exact index within the 5-column grid
    return columnIndex;
  };

  const handleDragOver = (e: React.DragEvent, sectionId: string) => {
    e.preventDefault();

    // Throttle visual updates for performance
    if (e.nativeEvent.movementX === 0 && e.nativeEvent.movementY === 0) return;

    // Calculate target position based on mouse position
    const targetIndex = getDropIndexFromPosition(e, sectionId);
    
    // Update the active drag section
    setActiveDragSection({ sectionId, targetIndex });
    
    // Add visual feedback
    e.currentTarget.classList.add('bg-gray-100/50', 'border-2', 'border-dashed', 'border-black/30');
    
    // Highlight the specific cell position where the item would be dropped
    const cellElements = Array.from(e.currentTarget.querySelectorAll('.card-slot, .empty-slot'));
    cellElements.forEach((cell, idx) => {
      if (idx === targetIndex) {
        cell.classList.add('ring-2', 'ring-blue-500', 'bg-blue-50');
      } else {
        cell.classList.remove('ring-2', 'ring-blue-500', 'bg-blue-50');
      }
    });
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.currentTarget.classList.remove('bg-gray-100/50', 'border-2', 'border-dashed', 'border-black/30');
    
    // Remove all highlight effects
    const cellElements = Array.from(e.currentTarget.querySelectorAll('.card-slot, .empty-slot'));
    cellElements.forEach(cell => {
      cell.classList.remove('ring-2', 'ring-blue-500', 'bg-blue-50');
    });
    
    setActiveDragSection(null);
  };

  const handleDrop = (e: React.DragEvent, sectionId: string) => {
    e.preventDefault();
    e.currentTarget.classList.remove('bg-gray-100/50', 'border-2', 'border-dashed', 'border-black/30');
    
    // Remove all highlight effects
    const cellElements = Array.from(e.currentTarget.querySelectorAll('.card-slot, .empty-slot'));
    cellElements.forEach(cell => {
      cell.classList.remove('ring-2', 'ring-blue-500', 'bg-blue-50');
    });

    // Get the exact drop position based on mouse position
    const targetIndex = getDropIndexFromPosition(e, sectionId);
    console.log(`Drop at section ${sectionId}, exact position: ${targetIndex}`);
    
    try {
      const dragData = JSON.parse(e.dataTransfer.getData('application/json'));
      console.log('Drop data:', dragData);
      
      // Log additional source information for cross-quadrant debugging
      const sourceSection = dragData.sectionId;
      const isCrossQuadrantMove = sourceSection && sourceSection !== sectionId;
      console.log('Cross-quadrant move:', isCrossQuadrantMove, 'From section:', sourceSection, 'To section:', sectionId);

      // Count section cards before processing
      const sectionCardsCount = plotPoints.filter(p => p.sectionId === sectionId).length;
      console.log(`Section ${sectionId} has ${sectionCardsCount} cards before drop`);

      if (dragData.type === 'plot-point') {
        console.log('Moving plot point', dragData.id, 'to section', sectionId, 'at position', targetIndex);
        // NO LIMIT CHECK - we want to support unlimited cards per section
        onMovePoint?.(dragData, sectionId, targetIndex);
      } else if (dragData.type === 'storyline-card') {
        // Create a drag item from the storyline card data
        const dragItem: DragItem = {
          id: dragData.id || dragData.cardId, // Support both formats
          type: 'storyline-card',
          content: dragData.content,
          sectionId: dragData.sectionId,
          source: dragData.source || 'manual'
        };
        
        console.log('Moving storyline card', dragItem.id, 'to section', sectionId, 'at position', targetIndex);
        // NO LIMIT CHECK - we want to support unlimited cards per section
        onMovePoint?.(dragItem, sectionId, targetIndex);
      }
    } catch (error) {
      console.error('Failed to process drop:', error);
    }
    
    // Reset active drag section
    setActiveDragSection(null);
  };

  // Helper function to arrange cards and empty slots in correct visual order
  const arrangeCardsAndSlots = (sectionId: string, sectionPoints: PlotPoint[], emptySlots: number) => {
    const MAX_VISIBLE_CELLS = 5; // Fixed 5 positions in the visual grid
    const result = new Array(MAX_VISIBLE_CELLS).fill(null);
    
    // Calculate total cards in this section
    const totalCardsInSection = sectionPoints.length;
    console.log(`Section ${sectionId} has ${totalCardsInSection} total cards`);
    
    // Sort all cards by position for display
    const sortedPoints = [...sectionPoints].sort((a, b) => (a.position || 0) - (b.position || 0));
    
    // Place cards at their specified positions (0-4) in the result array
    // For the visual display, we prioritize cards with positions 0-4
    sortedPoints.forEach(point => {
      const pos = point.position || 0;
      // Only place cards with position 0-4 in the visual grid
      if (pos >= 0 && pos < MAX_VISIBLE_CELLS) {
        result[pos] = { type: 'card', data: point };
      }
    });
    
    // Fill remaining slots with empty slots
    for (let i = 0; i < MAX_VISIBLE_CELLS; i++) {
      if (result[i] === null) {
        result[i] = { type: 'empty', index: i };
      }
    }
    
    // Calculate how many cards are not shown in the visual grid
    const hiddenCards = sortedPoints.filter(p => 
      p.position === undefined || p.position < 0 || p.position >= MAX_VISIBLE_CELLS
    ).length;
    
    return { 
      cells: result,
      overflow: hiddenCards,
      totalCards: totalCardsInSection
    };
  };
  
  const baseColumnClass = cn(
    "flex flex-col border-4 border-black bg-white transition-all duration-300",
    expandedQuadrant
      ? (isExpanded ? "quadrant-expanded" : "quadrant-collapsed")
      : "",
    isOpen ? "overflow-auto" : "overflow-hidden"
  );

  return (
    <div
      className={baseColumnClass}
      style={{
        ...style,
        display: expandedQuadrant && !isExpanded ? 'none' : 'flex',
      }}
    >
      {/* Header */}
      <div className="h-12 flex items-center justify-between px-4 border-b-4 border-black sticky top-0 bg-white z-10">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={onToggle} className="hover:bg-gray-100">
            <ChevronRight className={cn("h-4 w-4 transition-transform", isOpen && "rotate-90")} />
          </Button>
          <h2 className="font-bold">{title}</h2>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={isExpanded ? onCollapse : onExpand} className="hover:bg-gray-100">
            {isExpanded ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Content */}
      {isOpen && (
        <div className="flex-1 p-4 space-y-4 overflow-y-auto">
          {plotSections.map((section) => {
            const sectionPoints = plotPoints
              .filter(point => point.sectionId === section.id);

            const emptySlots = emptySlotsMap[section.id] || 0;
            
            // Arrange cards and empty slots in visual order
            const { cells, overflow, totalCards } = arrangeCardsAndSlots(section.id, sectionPoints, emptySlots);

            return (
              <div key={section.id} className="border-4 border-black mb-4">
                <SectionHeader
                  title={section.title}
                  subtitle={section.subtitle || ''}
                  color={section.color || ''}
                  onTitleChange={(title) => onUpdateSection(section.id, { title })}
                  onSubtitleChange={(subtitle) => onUpdateSection(section.id, { subtitle })}
                  onAddPoint={() => handleAddPoint(section.id)}
                />

                <div className="flex flex-col">
                  <div
                    ref={el => el && (sectionsRef.current[section.id] = el)}
                    className="grid grid-cols-5 auto-rows-auto -m-1 p-4"
                    onDragOver={(e) => handleDragOver(e, section.id)}
                    onDragLeave={handleDragLeave}
                    onDrop={(e) => handleDrop(e, section.id)}
                  >
                    {cells.map((cell, index) => {
                      if (cell.type === 'card') {
                        const point = cell.data;
                        return (
                          <div
                            key={`card-${point.id}`}
                            ref={el => {
                              if (el) {
                                if (!cellRefs.current[section.id]) {
                                  cellRefs.current[section.id] = [];
                                }
                                cellRefs.current[section.id][index] = el;
                              }
                            }}
                            className={cn(
                              "card-slot m-1",
                              activeDragSection?.sectionId === section.id &&
                              activeDragSection?.targetIndex === index
                                ? "ring-2 ring-blue-500"
                                : ""
                            )}
                            draggable
                            onDragStart={(e) => {
                              e.dataTransfer.setData('application/json', JSON.stringify({
                                type: 'plot-point',
                                id: point.id,
                                content: point.content,
                                sectionId: point.sectionId,
                                position: index // Use the visual index as position
                              }));
                            }}
                            data-index={index}
                          >
                            <PlotPointCard
                              id={point.id}
                              content={point.content}
                              sectionId={point.sectionId ?? section.id}
                              source={point.source ?? 'manual'}
                              onUpdate={onUpdatePlotPoint}
                              onDelete={onDeletePlotPoint}
                            />
                          </div>
                        );
                      } else { // empty slot
                        return (
                          <div
                            key={`empty-${index}`}
                            ref={el => {
                              if (el) {
                                if (!cellRefs.current[section.id]) {
                                  cellRefs.current[section.id] = [];
                                }
                                cellRefs.current[section.id][index] = el;
                              }
                            }}
                            className={cn(
                              "empty-slot border-2 border-black/20 hover:border-black border-dashed p-0 flex flex-col items-center justify-center text-xs text-gray-400 cursor-pointer h-full min-h-[50px] max-h-[70px] m-1",
                              activeDragSection?.sectionId === section.id &&
                              activeDragSection?.targetIndex === index
                                ? "ring-2 ring-blue-500 bg-blue-50"
                                : ""
                            )}
                            onClick={() => {
                              // When clicking an empty slot, create a new card at this specific position
                              const newCard = {
                                content: '',
                                sectionId: section.id,
                                position: index,
                                source: 'manual'
                              };
                              onAddPlotPoint(section.id);
                            }}
                            data-index={index}
                          >
                            <div className="w-full bg-gray-100 py-0.5 px-1 border-b border-gray-300">
                              <div className="h-3"></div>
                            </div>
                            <div className="flex items-center justify-center p-1 flex-1">
                              <span className="text-xs">New plot point...</span>
                            </div>
                          </div>
                        );
                      }
                    })}
                  </div>
                  
                  {/* Show overflow indicator if we have more cards than can be displayed */}
                  {overflow > 0 && (
                    <div className="px-4 pb-2 flex justify-center items-center gap-2 text-sm font-medium border-t border-gray-200 pt-2">
                      <span className="bg-amber-100 text-amber-800 px-3 py-1 rounded-full">
                        +{overflow} more card{overflow > 1 ? 's' : ''} not shown
                      </span>
                      <span className="text-xs text-gray-600">(Total: {totalCards})</span>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};