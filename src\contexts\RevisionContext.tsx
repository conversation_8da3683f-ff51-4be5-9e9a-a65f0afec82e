import React, { createContext, useContext, useState } from 'react';
import { useRevisionStore } from '../store/revisionStore';

interface RevisionContextType {
  meetLink: string | null;
  generateMeetLink: () => void;
  revisions: any[];
  addNote: (content: string) => void;
  removeNote: (id: string) => void;
  markNoteAsProcessed: (id: string) => void;
}

const RevisionContext = createContext<RevisionContextType | undefined>(undefined);

export function RevisionProvider({ children }: { children: React.ReactNode }) {
  const [meetLink, setMeetLink] = useState<string | null>(null);
  const store = useRevisionStore();

  const generateMeetLink = () => {
    const chars = 'abcdefghijklmnopqrstuvwxyz';
    let result = '';
    for (let i = 0; i < 10; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    const link = `https://meet.google.com/${result}`;
    setMeetLink(link);
    window.open(link, '_blank');
  };

  return (
    <RevisionContext.Provider value={{ 
      meetLink, 
      generateMeetLink,
      revisions: store.notes,
      addNote: store.addNote,
      removeNote: store.deleteNote,
      markNoteAsProcessed: (id: string) => store.markAsProcessed(id, true)
    }}>
      {children}
    </RevisionContext.Provider>
  );
}

export const useRevision = () => {
  const context = useContext(RevisionContext);
  if (context === undefined) {
    throw new Error('useRevision must be used within a RevisionProvider');
  }
  return context;
};
