import React, { useState, useEffect, useRef } from 'react';
import { Minimize2, MessageSquare, SplitSquareHorizontal, Move, Trash2, Maximize2, PanelLeft, PanelRight, X, ChevronDown, ChevronUp, Eye, EyeOff, Save, Plus, Upload, List, Maximize, CornerRightDown, MoreVertical } from 'lucide-react';
import { Button } from '../ui/button';
import { Card, CardHeader, CardContent } from '../ui/card';
import { ModelSelector } from './ModelSelector';
import { TextSplitter } from './TextSplitter';
import { ChatMessage } from './ChatMessage';
import { useChat } from '../../contexts/ChatContext';
import { useEpisodeTrackStore } from '../../store/episodeTrackStore';
import { cn } from '../../lib/utils';
import { useLocalStorage } from '../../hooks/shared/useLocalStorage';

type ChatPosition = 'floating' | 'right' | 'left' | 'full';

interface ChatSize {
  width: number;
  height: number;
}

interface ChatSettings {
  position: { x: number; y: number };
  size: ChatSize;
  isMinimized: boolean;
  chatPosition: ChatPosition;
  isFullscreen: boolean;
}

const DEFAULT_CHAT_SETTINGS: ChatSettings = {
  position: { x: window.innerWidth - 380, y: 80 },
  size: { width: 360, height: 600 },
  isMinimized: false,
  chatPosition: 'floating',
  isFullscreen: false
};

// Helper for deep comparison
function isChatSettingsEqual(a: ChatSettings, b: ChatSettings) {
  return (
    a.position.x === b.position.x &&
    a.position.y === b.position.y &&
    a.size.width === b.size.width &&
    a.size.height === b.size.height &&
    a.isMinimized === b.isMinimized &&
    a.chatPosition === b.chatPosition &&
    a.isFullscreen === b.isFullscreen
  );
}

export const ChatWindow: React.FC = () => {
  const {
    messages,
    addMessage,
    isCollapsed,
    setIsCollapsed,
    selectedModel,
    setSelectedModel,
    position,
    setPosition,
    saveChat,
    savedChats,
    loadChat,
    currentChatId,
    clearChat,
    createPromptFromChat,
    uploadContent,
    isLoading,
    useStreaming,
    setUseStreaming
  } = useChat();

  const { deleteAllCards } = useEpisodeTrackStore();

  const [input, setInput] = useState('');
  const [showTextSplitter, setShowTextSplitter] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [resizeDirection, setResizeDirection] = useState<string | null>(null);
  const [chatPosition, setChatPosition] = useState<ChatPosition>(DEFAULT_CHAT_SETTINGS.chatPosition);
  const [isFullscreen, setIsFullscreen] = useState(DEFAULT_CHAT_SETTINGS.isFullscreen);
  const [isMinimized, setIsMinimized] = useState(DEFAULT_CHAT_SETTINGS.isMinimized);
  const [dragStartPos, setDragStartPos] = useState({ x: 0, y: 0 });
  const [showSavedChats, setShowSavedChats] = useState(false);
  const [isSelectingMessages, setIsSelectingMessages] = useState(false);
  const [selectedMessages, setSelectedMessages] = useState<string[]>([]);
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [uploadText, setUploadText] = useState('');
  const [saveTitle, setSaveTitle] = useState('');
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [chatSettings, setChatSettings] = useLocalStorage<ChatSettings>('chat_widget_settings', DEFAULT_CHAT_SETTINGS);
  const [snapGuides, setSnapGuides] = useState<{ x: boolean, y: boolean }>({ x: false, y: false });
  const [showMoreMenu, setShowMoreMenu] = useState(false);

  const chatRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const resizeStartRef = useRef<{ size: ChatSize, position: { x: number, y: number }, clientX: number, clientY: number } | null>(null);
  const snapThreshold = 20; // pixels from edge to trigger snap

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Save settings to localStorage ONLY when state changes
  useEffect(() => {
    setChatSettings({
      position,
      size: chatSettings.size, // or use a tracked size state if you have one
      isMinimized,
      chatPosition,
      isFullscreen,
    });
  }, [position, isMinimized, chatPosition, isFullscreen]);

  const handleDragStart = (e: React.MouseEvent) => {
    if (chatPosition !== 'floating') return;

    setIsDragging(true);
    const rect = chatRef.current?.getBoundingClientRect();
    if (rect) {
      setDragStartPos({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    }
  };

  const handleDrag = (e: React.MouseEvent) => {
    if (!isDragging || chatPosition !== 'floating') return;

    const newX = e.clientX - dragStartPos.x;
    const newY = e.clientY - dragStartPos.y;

    // Check for snap to edges
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const chatWidth = chatRef.current?.offsetWidth || 320;
    const chatHeight = chatRef.current?.offsetHeight || 600;

    let snappedX = newX;
    let snappedY = newY;
    let xSnapped = false;
    let ySnapped = false;

    // Snap to left edge
    if (newX < snapThreshold) {
      snappedX = 0;
      xSnapped = true;
    }

    // Snap to right edge
    if (newX + chatWidth > viewportWidth - snapThreshold) {
      snappedX = viewportWidth - chatWidth;
      xSnapped = true;
    }

    // Snap to top edge
    if (newY < snapThreshold) {
      snappedY = 0;
      ySnapped = true;
    }

    // Snap to bottom edge
    if (newY + chatHeight > viewportHeight - snapThreshold) {
      snappedY = viewportHeight - chatHeight;
      ySnapped = true;
    }

    // Update snap guides
    setSnapGuides({ x: xSnapped, y: ySnapped });

    // Keep chat within viewport bounds
    snappedX = Math.max(0, Math.min(viewportWidth - chatWidth, snappedX));
    snappedY = Math.max(0, Math.min(viewportHeight - chatHeight, snappedY));

    setPosition({
      x: snappedX,
      y: snappedY
    });
  };

  const handleDragEnd = () => {
    setIsDragging(false);
    setSnapGuides({ x: false, y: false });

    // Save position to localStorage
    setChatSettings(prev => ({
      ...prev,
      position
    }));
  };

  const handleResizeStart = (e: React.MouseEvent, direction: string) => {
    e.preventDefault();
    e.stopPropagation();

    if (chatPosition !== 'floating' || isFullscreen) return;

    setIsResizing(true);
    setResizeDirection(direction);

    const rect = chatRef.current?.getBoundingClientRect();
    if (rect) {
      resizeStartRef.current = {
        size: { width: rect.width, height: rect.height },
        position: { x: position.x, y: position.y },
        clientX: e.clientX,
        clientY: e.clientY
      };
    }
  };

  const handleResize = (e: React.MouseEvent) => {
    if (!isResizing || !resizeStartRef.current) return;

    e.preventDefault();

    const deltaX = e.clientX - resizeStartRef.current.clientX;
    const deltaY = e.clientY - resizeStartRef.current.clientY;
    const startSize = resizeStartRef.current.size;
    const startPos = resizeStartRef.current.position;

    const MIN_WIDTH = 280;
    const MIN_HEIGHT = 400;
    const MAX_WIDTH = window.innerWidth * 0.8;
    const MAX_HEIGHT = window.innerHeight * 0.8;

    let newWidth = startSize.width;
    let newHeight = startSize.height;
    let newX = startPos.x;
    let newY = startPos.y;

    // Handle different resize directions
    if (resizeDirection?.includes('e')) { // Right edge
      newWidth = Math.max(MIN_WIDTH, Math.min(MAX_WIDTH, startSize.width + deltaX));
    }

    if (resizeDirection?.includes('w')) { // Left edge
      const widthChange = Math.max(MIN_WIDTH, Math.min(MAX_WIDTH, startSize.width - deltaX)) - startSize.width;
      newWidth = startSize.width - widthChange;
      newX = startPos.x + widthChange;
    }

    if (resizeDirection?.includes('s')) { // Bottom edge
      newHeight = Math.max(MIN_HEIGHT, Math.min(MAX_HEIGHT, startSize.height + deltaY));
    }

    if (resizeDirection?.includes('n')) { // Top edge
      const heightChange = Math.max(MIN_HEIGHT, Math.min(MAX_HEIGHT, startSize.height - deltaY)) - startSize.height;
      newHeight = startSize.height - heightChange;
      newY = startPos.y + heightChange;
    }

    // Update position if needed
    if (newX !== startPos.x || newY !== startPos.y) {
      setPosition({ x: newX, y: newY });
    }

    // Apply new size to the element
    if (chatRef.current) {
      chatRef.current.style.width = `${newWidth}px`;
      chatRef.current.style.height = `${newHeight}px`;
    }
  };

  const handleResizeEnd = () => {
    setIsResizing(false);
    setResizeDirection(null);
    resizeStartRef.current = null;

    // Save new size to localStorage
    if (chatRef.current) {
      setChatSettings(prev => ({
        ...prev,
        size: {
          width: chatRef.current.offsetWidth,
          height: chatRef.current.offsetHeight
        }
      }));
    }
  };

  const handleSend = () => {
    if (!input.trim()) return;
    addMessage(input, 'user');
    setInput('');
  };

  const handleDeleteAllCards = () => {
    if (window.confirm('Are you sure you want to delete all cards from this session?')) {
      deleteAllCards();
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    setChatSettings(prev => ({
      ...prev,
      isFullscreen: !isFullscreen
    }));
  };

  const toggleMinimized = () => {
    setIsMinimized(!isMinimized);
    setChatSettings(prev => ({
      ...prev,
      isMinimized: !isMinimized
    }));
  };

  const setPositionMode = (mode: ChatPosition) => {
    setChatPosition(mode);
    setChatSettings(prev => ({
      ...prev,
      chatPosition: mode
    }));
    if (mode === 'floating') {
      setIsFullscreen(false);
    }
  };

  // Completely hide the chat
  const hideChat = () => {
    setIsCollapsed(true);
  };

  // Toggle message selection mode
  const toggleMessageSelection = () => {
    setIsSelectingMessages(!isSelectingMessages);
    if (isSelectingMessages) {
      setSelectedMessages([]);
    }
  };

  // Toggle a message selection
  const toggleMessageSelected = (messageId: string) => {
    if (selectedMessages.includes(messageId)) {
      setSelectedMessages(prev => prev.filter(id => id !== messageId));
    } else {
      setSelectedMessages(prev => [...prev, messageId]);
    }
  };

  // Create a prompt from selected messages
  const handleCreatePrompt = () => {
    if (selectedMessages.length === 0) return;

    const { title, content } = createPromptFromChat(selectedMessages);

    // Dispatch custom event to create a prompt
    const createPromptEvent = new CustomEvent('create-prompt-from-chat', {
      detail: {
        title,
        details: content,
        groupId: '' // Let the PromptsView determine the group
      }
    });

    window.dispatchEvent(createPromptEvent);

    // Reset selection mode
    setIsSelectingMessages(false);
    setSelectedMessages([]);

    // Give user feedback
    alert(`Prompt "${title}" has been created! Switch to the Prompts tab to view it.`);
  };

  // Handle file upload
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      const text = await file.text();
      setUploadText(text);
      setShowUploadDialog(true);
    } catch (error) {
      console.error('Error reading file:', error);
      alert('Failed to read file. Please try again.');
    }
  };

  // Submit uploaded content to chat
  const handleUploadSubmit = () => {
    if (!uploadText.trim()) return;

    uploadContent(uploadText);
    setShowUploadDialog(false);
    setUploadText('');
  };

  // Handle saving chat
  const handleSaveChat = () => {
    if (showSaveDialog) {
      const title = saveTitle.trim() || undefined;
      saveChat(title);
      setSaveTitle('');
      setShowSaveDialog(false);
    } else {
      setShowSaveDialog(true);
    }
  };

  // Start a new chat
  const handleNewChat = () => {
    if (messages.length > 1) {
      if (window.confirm('Save current chat before starting a new one?')) {
        handleSaveChat();
      }
    }
    clearChat();
  };

  // Reset chat widget by clearing localStorage settings
  const handleResetChatWidget = () => {
    // Clear chat settings from localStorage
    localStorage.removeItem('chat_widget_settings');

    // Reset state to defaults
    setPosition(DEFAULT_CHAT_SETTINGS.position);
    setChatPosition(DEFAULT_CHAT_SETTINGS.chatPosition);
    setIsMinimized(DEFAULT_CHAT_SETTINGS.isMinimized);
    setIsFullscreen(DEFAULT_CHAT_SETTINGS.isFullscreen);

    // Reset chat content
    clearChat();

    // Force reload the page to ensure all settings are reset
    window.location.reload();
  };

  // Snap to predefined positions
  const snapToPosition = (position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center') => {
    const chatWidth = chatRef.current?.offsetWidth || chatSettings.size.width;
    const chatHeight = chatRef.current?.offsetHeight || chatSettings.size.height;

    let newX = 0;
    let newY = 0;

    switch (position) {
      case 'top-left':
        newX = 0;
        newY = 0;
        break;
      case 'top-right':
        newX = window.innerWidth - chatWidth;
        newY = 0;
        break;
      case 'bottom-left':
        newX = 0;
        newY = window.innerHeight - chatHeight;
        break;
      case 'bottom-right':
        newX = window.innerWidth - chatWidth;
        newY = window.innerHeight - chatHeight;
        break;
      case 'center':
        newX = (window.innerWidth - chatWidth) / 2;
        newY = (window.innerHeight - chatHeight) / 2;
        break;
    }

    setPosition({ x: newX, y: newY });
    setChatSettings(prev => ({
      ...prev,
      position: { x: newX, y: newY }
    }));
  };

  // Dropdown menu for less-used controls
  const renderMoreMenu = () => (
    <div className={cn('absolute right-0 mt-10 mr-2 z-50 bg-white border rounded shadow-lg', showMoreMenu ? '' : 'hidden')}
         onMouseLeave={() => setShowMoreMenu(false)}>
      <Button variant="ghost" size="sm" onClick={handleNewChat} className="w-full justify-start"> <Plus className="h-4 w-4 mr-2" /> New Chat </Button>
      <Button variant="ghost" size="sm" onClick={handleSaveChat} className="w-full justify-start"> <Save className="h-4 w-4 mr-2" /> Save Chat </Button>
      <Button variant="ghost" size="sm" onClick={() => setShowSavedChats(!showSavedChats)} className="w-full justify-start"> <List className="h-4 w-4 mr-2" /> History </Button>
      <Button variant="ghost" size="sm" onClick={() => setShowUploadDialog(true)} className="w-full justify-start"> <Upload className="h-4 w-4 mr-2" /> Upload </Button>
      <div className="border-t my-1" />
      {renderPositionControls()}
    </div>
  );

  if (isCollapsed) {
    return (
      <Button
        className="fixed bottom-6 right-6 shadow-2xl rounded-full p-4 z-[2000] bg-white border-2 border-black hover:bg-gray-100"
        style={{ minWidth: 56, minHeight: 56 }}
        onClick={() => setIsCollapsed(false)}
        aria-label="Open Chat"
      >
        <MessageSquare className="h-8 w-8 text-black" />
      </Button>
    );
  }

  const renderPositionControls = () => (
    <div className="flex gap-1 border-2 border-black rounded">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setPositionMode('floating')}
        className={cn(
          "h-7 w-7 p-1",
          chatPosition === 'floating' && "bg-gray-200"
        )}
        title="Floating Window"
      >
        <Move className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setPositionMode('left')}
        className={cn(
          "h-7 w-7 p-1",
          chatPosition === 'left' && "bg-gray-200"
        )}
        title="Dock Left"
      >
        <PanelLeft className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setPositionMode('right')}
        className={cn(
          "h-7 w-7 p-1",
          chatPosition === 'right' && "bg-gray-200"
        )}
        title="Dock Right"
      >
        <PanelRight className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={toggleFullscreen}
        className={cn(
          "h-7 w-7 p-1",
          isFullscreen && "bg-gray-200"
        )}
        title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}
      >
        <Maximize2 className="h-4 w-4" />
      </Button>
    </div>
  );

  const getContainerStyles = () => {
    if (chatPosition === 'floating') {
      return {
        position: 'fixed' as const,
        zIndex: 12000,
        left: position.x,
        top: 0,
        width: chatSettings.size.width + 'px',
        height: isMinimized ? 'auto' : chatSettings.size.height + 'px'
      };
    }

    if (isFullscreen) {
      return {
        position: 'fixed' as const,
        zIndex: 12000,
        left: 0,
        top: 0,
        right: 0,
        bottom: 0,
        width: '100%',
        height: '100%'
      };
    }

    if (chatPosition === 'left') {
      return {
        position: 'fixed' as const,
        zIndex: 12000,
        left: 0,
        top: 0,
        bottom: 0,
        width: '400px',
        height: isMinimized ? 'auto' : '100vh'
      };
    }

    if (chatPosition === 'right') {
      return {
        position: 'fixed' as const,
        zIndex: 12000,
        right: 0,
        top: 0,
        bottom: 0,
        width: '400px',
        height: isMinimized ? 'auto' : '100vh'
      };
    }

    return {};
  };

  // Render resize handles
  const renderResizeHandles = () => {
    if (chatPosition !== 'floating' || isFullscreen) return null;

    const handlePositions = [
      { dir: 'n', className: 'top-0 left-0 right-0 h-2 cursor-n-resize' },
      { dir: 'e', className: 'top-0 right-0 bottom-0 w-2 cursor-e-resize' },
      { dir: 's', className: 'bottom-0 left-0 right-0 h-2 cursor-s-resize' },
      { dir: 'w', className: 'top-0 left-0 bottom-0 w-2 cursor-w-resize' },
      { dir: 'ne', className: 'top-0 right-0 w-4 h-4 cursor-ne-resize' },
      { dir: 'se', className: 'bottom-0 right-0 w-4 h-4 cursor-se-resize' },
      { dir: 'sw', className: 'bottom-0 left-0 w-4 h-4 cursor-sw-resize' },
      { dir: 'nw', className: 'top-0 left-0 w-4 h-4 cursor-nw-resize' }
    ];

    return handlePositions.map(({ dir, className }) => (
      <div
        key={dir}
        className={cn(
          "absolute z-50 opacity-0 hover:opacity-100 transition-opacity",
          className,
          isResizing && resizeDirection === dir && "opacity-100 bg-blue-200"
        )}
        onMouseDown={(e) => handleResizeStart(e, dir)}
      />
    ));
  };

  // Render snap position buttons
  const renderSnapButtons = () => {
    if (chatPosition !== 'floating') return null;

    return (
      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-white border-2 border-black rounded-t-lg px-1 py-0.5 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => snapToPosition('top-left')}
          className="h-6 w-6 p-1"
          title="Snap to Top Left"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M2 2H7V7H2V2Z" fill="currentColor"/>
          </svg>
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => snapToPosition('top-right')}
          className="h-6 w-6 p-1"
          title="Snap to Top Right"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 2H14V7H9V2Z" fill="currentColor"/>
          </svg>
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => snapToPosition('center')}
          className="h-6 w-6 p-1"
          title="Center"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="5" y="5" width="6" height="6" fill="currentColor"/>
          </svg>
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => snapToPosition('bottom-left')}
          className="h-6 w-6 p-1"
          title="Snap to Bottom Left"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M2 9H7V14H2V9Z" fill="currentColor"/>
          </svg>
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => snapToPosition('bottom-right')}
          className="h-6 w-6 p-1"
          title="Snap to Bottom Right"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 9H14V14H9V9Z" fill="currentColor"/>
          </svg>
        </Button>
      </div>
    );
  };

  return (
    <div
      ref={chatRef}
      style={getContainerStyles()}
      className={cn(
        "transition-all duration-300 ease-in-out group",
        chatPosition === 'floating' && isDragging && "opacity-75",
        chatPosition === 'floating' && !isFullscreen && "chat-resizable"
      )}
      onMouseMove={isResizing ? handleResize : isDragging ? handleDrag : undefined}
      onMouseUp={isResizing ? handleResizeEnd : handleDragEnd}
      onMouseLeave={isResizing ? handleResizeEnd : handleDragEnd}
    >
      {/* Overlay when dragging or resizing */}
      {(isDragging || isResizing) && (
        <div className="fixed inset-0 bg-black/5 z-40" />
      )}

      {/* Snap guides */}
      {snapGuides.x && (
        <div className="fixed top-0 bottom-0 w-0.5 bg-blue-500 z-40" style={{ left: position.x }} />
      )}
      {snapGuides.y && (
        <div className="fixed left-0 right-0 h-0.5 bg-blue-500 z-40" style={{ top: position.y }} />
      )}

      {/* Snap position buttons */}
      {renderSnapButtons()}

      {/* Resize handles */}
      {renderResizeHandles()}

      {/* Floating restore button when minimized */}
      {isMinimized && chatPosition === 'floating' && (
        <Button className="fixed bottom-6 right-6 z-50 rounded-full shadow-lg bg-blue-600 text-white" onClick={toggleMinimized} title="Open Chat">
          <MessageSquare className="h-6 w-6" />
        </Button>
      )}

      {/* Main chat card */}
      {!isMinimized && (
        <Card className="border-4 border-black flex flex-col h-full">
          <CardHeader className="border-b-4 border-black p-3 bg-gradient-to-r from-gray-50 to-gray-100 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <h3 className="font-bold">Chat Assistant</h3>
              <ModelSelector value={selectedModel} onChange={setSelectedModel} />
              <Button variant={showTextSplitter ? 'secondary' : 'ghost'} size="sm" onClick={() => setShowTextSplitter((prev) => !prev)} title="Text Splitter">
                <SplitSquareHorizontal className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex items-center gap-1 relative">
              <Button variant="ghost" size="sm" onClick={toggleMinimized} title={isMinimized ? 'Expand' : 'Minimize'}>
                {isMinimized ? <Maximize2 className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
              </Button>
              <Button variant="ghost" size="sm" onClick={hideChat} title="Hide Chat">
                <EyeOff className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={() => setShowMoreMenu((v) => !v)} title="More" aria-label="More Controls">
                <MoreVertical className="h-4 w-4" />
              </Button>
              {renderMoreMenu()}
            </div>
          </CardHeader>
          <CardContent className="flex-1 overflow-hidden flex flex-col p-0">
            {showTextSplitter ? (
              <div className="flex-1 overflow-y-auto"><TextSplitter onClose={() => setShowTextSplitter(false)} /></div>
            ) : (
              <>
                <div className="flex-1 overflow-y-auto p-4 space-y-4">
                  {messages.map((message) => (
                    <ChatMessage
                      key={message.id}
                      id={message.id}
                      content={message.content}
                      type={message.type}
                      isStreaming={message.isStreaming}
                    />
                  ))}
                  {isLoading && (
                    <div className="flex items-center space-x-2 p-3 bg-gray-100 rounded-lg border-2 border-black">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                      </div>
                      <span className="text-sm text-gray-500">AI is thinking...</span>
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </div>
                {/* Input area always at bottom */}
                <div className="border-t-2 border-black p-2 flex items-center gap-2 bg-white">
                  <input
                    className="flex-1 border rounded p-2"
                    placeholder="Ask about your story..."
                    value={input}
                    onChange={e => setInput(e.target.value)}
                    onKeyDown={e => { if (e.key === 'Enter') handleSend(); }}
                    disabled={isLoading}
                  />
                  <Button onClick={handleSend} disabled={isLoading || !input.trim()}>
                    Send
                  </Button>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};