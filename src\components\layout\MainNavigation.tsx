import React from 'react';
import { Home, Layout, Save, Check, ChevronDown, Settings } from 'lucide-react';
import { Button } from '../ui/button';
import { ThemeToggle } from '../ui/ThemeToggle';
import { navigationItems } from '../../constants/navigation';
import { useProject } from '../../contexts/ProjectContext';
import { cn } from '../../lib/utils';
import { useEpisodeTrackStore } from '../../store/episodeTrackStore';
import type { ViewType } from '../../constants/navigation';
import { FountainImport } from '../write/import/FountainImport';
import { defaultActStructure } from '../../constants/episodes';
import { useWriteNavigation } from '../../hooks/write/useWriteNavigation';
import { distributeScenesByTemplate } from '../../utils/distributeScenesByTemplate';

interface MainNavigationProps {
  activeView: ViewType;
  onViewChange: (view: ViewType) => void;
  onHomeClick: () => void;
  onTemplateChange: () => void;
  template: 'classic' | 'enhanced';
}

export const MainNavigation: React.FC<MainNavigationProps> = ({
  activeView,
  onViewChange,
  onHomeClick,
  onTemplateChange,
  template,
}) => {
  const { currentProject, saveProject, closeProject, lastSaved } = useProject();
  const [showSaved, setShowSaved] = React.useState(false);
  const { setColorLegendVisible } = useEpisodeTrackStore();
  const [showImportModal, setShowImportModal] = React.useState(false);
  const [exportDropdownOpen, setExportDropdownOpen] = React.useState(false);
  const exportDropdownRef = React.useRef<HTMLDivElement>(null);
  const [useBlankTemplates, setUseBlankTemplates] = React.useState<boolean>(() => 
    localStorage.getItem('use_blank_templates') === 'true'
  );
  const [useEmptyTemplates, setUseEmptyTemplates] = React.useState<boolean>(() => 
    localStorage.getItem('use_empty_templates') === 'true'
  );
  const [autoDistribute, setAutoDistribute] = React.useState(true);
  const { importScenes } = useWriteNavigation();

  // Update useBlankTemplates when localStorage changes
  React.useEffect(() => {
    const checkBlankTemplatesMode = () => {
      setUseBlankTemplates(localStorage.getItem('use_blank_templates') === 'true');
      setUseEmptyTemplates(localStorage.getItem('use_empty_templates') === 'true');
    };
    
    // Check on mount
    checkBlankTemplatesMode();
    
    // Listen for storage changes
    window.addEventListener('storage', checkBlankTemplatesMode);
    
    return () => {
      window.removeEventListener('storage', checkBlankTemplatesMode);
    };
  }, []);

  const handleHomeClick = () => {
    if (currentProject) {
      closeProject();
    }
    onHomeClick();
  };

  const handleSave = () => {
    if (currentProject) {
      saveProject();
      setShowSaved(true);
      setTimeout(() => setShowSaved(false), 2000);
    }
  };

  // When switching views, ensure color legend is visible or hidden as appropriate
  const handleViewChange = (view: ViewType) => {
    onViewChange(view);
    
    // Make color legend visible when switching to relevant views
    if (['workshop', 'episodes', 'timeline'].includes(view)) {
      setColorLegendVisible(true);
    } 
    // Hide color legend when switching to write view
    else if (view === 'write') {
      setColorLegendVisible(false);
    }
  };

  // Format last saved time
  const lastSavedText = lastSaved 
    ? new Date(lastSaved).toLocaleTimeString(undefined, { 
        hour: 'numeric', 
        minute: '2-digit' 
      })
    : null;

  const openImportModal = () => setShowImportModal(true);
  const closeImportModal = () => setShowImportModal(false);

  // Export handlers (placeholders)
  const handleExportEpisode = () => {
    setExportDropdownOpen(false);
    alert('Export Episode triggered!');
  };
  const handleExportSeries = () => {
    setExportDropdownOpen(false);
    alert('Export Whole Series triggered!');
  };
  const handleExportScript = () => {
    setExportDropdownOpen(false);
    alert('Export Whole Script triggered!');
  };

  // Keyboard/blur accessibility for dropdown
  React.useEffect(() => {
    if (!exportDropdownOpen) return;
    function handleClickOutside(event: MouseEvent) {
      if (exportDropdownRef.current && !exportDropdownRef.current.contains(event.target as Node)) {
        setExportDropdownOpen(false);
      }
    }
    function handleEscape(event: KeyboardEvent) {
      if (event.key === 'Escape') setExportDropdownOpen(false);
    }
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscape);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [exportDropdownOpen]);

  // Placeholder: Use first episode and act for import
  const currentEpisodeId = 1; // TODO: Replace with actual current episode context
  const defaultActId = defaultActStructure[0].id;
  
  // Handle toggling blank templates option
  const toggleBlankTemplates = () => {
    const newValue = !useBlankTemplates;
    setUseBlankTemplates(newValue);
    localStorage.setItem('use_blank_templates', newValue.toString());
    
    // Show confirmation
    alert(
      newValue 
        ? 'Blank templates enabled. New episodes will have no pre-populated scenes.' 
        : 'Default templates enabled. New episodes will include default scenes.'
    );
  };
  
  // Handle toggling empty templates option
  const toggleEmptyTemplates = () => {
    const newValue = !useEmptyTemplates;
    setUseEmptyTemplates(newValue);
    localStorage.setItem('use_empty_templates', newValue.toString());
    
    // Show confirmation
    alert(
      newValue 
        ? 'Empty templates enabled. New episodes will have empty scene structures.' 
        : 'Empty templates disabled. New episodes will use the selected template structure.'
    );
  };
  
  // Open project settings
  const openProjectSettings = () => {
    window.dispatchEvent(new CustomEvent('openProjectSettings'));
  };

  // Handler for importing scenes
  const handleImportScenes = (scenes, actId) => {
    importScenes(currentEpisodeId, actId, scenes, 'replace');
    setShowImportModal(false);
  };

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 flex items-center justify-between px-4 py-2 bg-white border-b-4 border-black dark:bg-gray-900 dark:border-gray-700 dark:text-white">
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          className="border-4 border-black hover:bg-gray-100 dark:border-gray-700 dark:hover:bg-gray-800"
          onClick={handleHomeClick}
        >
          <Home className="h-4 w-4 mr-2" />
          <span className="font-bold text-sm">Home</span>
        </Button>
        {currentProject && (
          <>
            <Button
              variant="ghost"
              className="border-4 border-black hover:bg-gray-100 dark:border-gray-700 dark:hover:bg-gray-800"
              onClick={onTemplateChange}
            >
              <Layout className="h-4 w-4 mr-2" />
              <span className="font-bold text-sm">
                Switch to {template === 'classic' ? 'Enhanced' : 'Classic'}
              </span>
            </Button>
            <div className="relative">
              <Button
                variant="ghost"
                className={cn(
                  "border-4 border-black hover:bg-gray-100 dark:border-gray-700 dark:hover:bg-gray-800",
                  showSaved && "bg-green-100 dark:bg-green-900"
                )}
                onClick={handleSave}
              >
                {showSaved ? (
                  <Check className="h-4 w-4 mr-2 text-green-600 dark:text-green-400" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                <span className="font-bold text-sm">
                  {showSaved ? 'Saved!' : 'Save Project'}
                </span>
              </Button>
              {lastSavedText && !showSaved && (
                <div className="absolute -bottom-4 left-0 text-xs text-gray-500 adjustposition">
                  Last saved: {lastSavedText}
                </div>
              )}
            </div>
          </>
        )}
      </div>

      <div className="flex gap-2 items-center">
        {navigationItems.slice(0, -1).map((item) => {
          let label = item.label;
          if (item.id === 'episodes') {
            label = 'Episodes/Acts';
          }
          return (
            <Button
              key={item.id}
              variant="ghost"
              className={cn(
                "flex items-center gap-2 px-4 py-2 border-4 border-black hover:bg-gray-100 transition-colors dark:border-gray-700 dark:hover:bg-gray-800",
                activeView === item.id ? 'bg-yellow-200 dark:bg-yellow-900' : ''
              )}
              onClick={() => handleViewChange(item.id)}
            >
              <item.icon className="h-4 w-4" />
              <span className="font-bold text-sm">{label}</span>
            </Button>
          );
        })}
        {/* Import/Export Buttons */}
        <Button
          variant="ghost"
          className="border-4 border-black hover:bg-gray-100 dark:border-gray-700 dark:hover:bg-gray-800"
          onClick={openImportModal}
        >
          Import
        </Button>
        <div className="relative" ref={exportDropdownRef}>
          <Button
            variant="ghost"
            className="border-4 border-black hover:bg-gray-100 dark:border-gray-700 dark:hover:bg-gray-800 flex items-center"
            aria-haspopup="menu"
            aria-expanded={exportDropdownOpen}
            aria-controls="export-dropdown-menu"
            onClick={() => setExportDropdownOpen((open) => !open)}
            onKeyDown={e => {
              if (e.key === 'ArrowDown' || e.key === 'Enter' || e.key === ' ') {
                setExportDropdownOpen(true);
                e.preventDefault();
              }
            }}
          >
            Export <ChevronDown className="ml-1 h-4 w-4" />
          </Button>
          {exportDropdownOpen && (
            <div
              id="export-dropdown-menu"
              role="menu"
              tabIndex={-1}
              className="absolute right-0 mt-2 w-48 bg-white border-2 border-black rounded shadow-lg z-50 focus:outline-none"
            >
              <button
                role="menuitem"
                tabIndex={0}
                className="w-full text-left px-4 py-2 hover:bg-gray-100 focus:bg-gray-100 border-b border-gray-200 last:border-b-0"
                onClick={handleExportEpisode}
                onKeyDown={e => { if (e.key === 'Enter') handleExportEpisode(); }}
              >
                Export Episode
              </button>
              <button
                role="menuitem"
                tabIndex={0}
                className="w-full text-left px-4 py-2 hover:bg-gray-100 focus:bg-gray-100 border-b border-gray-200 last:border-b-0"
                onClick={handleExportSeries}
                onKeyDown={e => { if (e.key === 'Enter') handleExportSeries(); }}
              >
                Export Whole Series
              </button>
              <button
                role="menuitem"
                tabIndex={0}
                className="w-full text-left px-4 py-2 hover:bg-gray-100 focus:bg-gray-100"
                onClick={handleExportScript}
                onKeyDown={e => { if (e.key === 'Enter') handleExportScript(); }}
              >
                Export Whole Script
              </button>
            </div>
          )}
        </div>
        {/* Tasks button (last item) */}
        {navigationItems.slice(-1).map((item) => (
          <Button
            key={item.id}
            variant="ghost"
            className={cn(
              "flex items-center gap-2 px-4 py-2 border-4 border-black hover:bg-gray-100 transition-colors dark:border-gray-700 dark:hover:bg-gray-800",
              activeView === item.id ? 'bg-yellow-200 dark:bg-yellow-900' : ''
            )}
            onClick={() => handleViewChange(item.id)}
          >
            <item.icon className="h-4 w-4" />
            <span className="font-bold text-sm">{item.label}</span>
          </Button>
        ))}
        {/* Place ThemeToggle (with zoom controls) at the end of the nav bar */}
        <ThemeToggle className="ml-4" />
      </div>
      
      {/* Import Modal Placeholder */}
      {showImportModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white border-4 border-black p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4">Import Fountain Screenplay</h2>
            <div className="mb-4 flex items-center gap-2">
              <div className="space-y-2 w-full">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="blank-templates"
                    checked={useBlankTemplates}
                    onChange={toggleBlankTemplates}
                    className="form-checkbox h-4 w-4 text-black border-2 border-black"
                  />
                  <label htmlFor="blank-templates" className="text-sm font-medium">
                    Use blank templates (no pre-populated scenes)
                  </label>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="empty-templates"
                    checked={useEmptyTemplates}
                    onChange={toggleEmptyTemplates}
                    className="form-checkbox h-4 w-4 text-black border-2 border-black"
                  />
                  <label htmlFor="empty-templates" className="text-sm font-medium">
                    Use empty scene structures (maintain template framework)
                  </label>
                </div>
              </div>
            </div>
            <div className="mb-4 flex items-center gap-2">
              <input
                type="checkbox"
                id="auto-distribute"
                checked={autoDistribute}
                onChange={e => setAutoDistribute(e.target.checked)}
                className="form-checkbox h-4 w-4 text-black border-2 border-black"
              />
              <label htmlFor="auto-distribute" className="text-sm font-medium">
                Auto-distribute scenes across acts
              </label>
            </div>
            <FountainImport
              onImport={handleImportScenes}
              onCancel={closeImportModal}
              actId={defaultActId}
              episodeId={currentEpisodeId}
            />
          </div>
        </div>
      )}
    </nav>
  );
};