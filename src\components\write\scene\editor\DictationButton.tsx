import React, { useState, useEffect, CSSProperties } from 'react';
import { Mic, MicOff } from 'lucide-react';
import { Button } from '../../../ui/button';
import { cn } from '../../../../lib/utils';

interface DictationButtonProps {
  onTranscript: (text: string) => void;
  disabled?: boolean;
  style?: CSSProperties;
}

export const DictationButton: React.FC<DictationButtonProps> = ({
  onTranscript,
  disabled = false,
  style
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);
  const [isSupported, setIsSupported] = useState(false);

  // Initialize speech recognition
  useEffect(() => {
    if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognitionInstance = new SpeechRecognition();
      
      recognitionInstance.continuous = true;
      recognitionInstance.interimResults = true;
      recognitionInstance.lang = 'en-US';
      
      recognitionInstance.onresult = (event) => {
        const transcript = Array.from(event.results)
          .map(result => result[0].transcript)
          .join('');
        
        if (event.results[0].isFinal) {
          onTranscript(transcript);
        }
      };
      
      recognitionInstance.onerror = (event) => {
        console.error('Speech recognition error', event.error);
        setIsRecording(false);
      };
      
      setRecognition(recognitionInstance);
      setIsSupported(true);
    } else {
      setIsSupported(false);
    }
    
    return () => {
      if (recognition) {
        recognition.stop();
      }
    };
  }, [onTranscript]);
  
  // Toggle speech recognition
  const toggleRecording = () => {
    if (!recognition || disabled) return;
    
    if (isRecording) {
      recognition.stop();
      setIsRecording(false);
    } else {
      recognition.start();
      setIsRecording(true);
    }
  };

  if (!isSupported) {
    return (
      <Button
        variant="ghost"
        size="sm"
        disabled
        title="Speech recognition not supported in this browser"
        className="h-8 w-8 p-1 rounded-full opacity-50"
        style={style}
      >
        <Mic className="h-4 w-4" />
      </Button>
    );
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleRecording}
      disabled={disabled}
      className={cn(
        "h-8 w-8 p-1 rounded-full",
        isRecording && "bg-red-100 text-red-600"
      )}
      title={isRecording ? "Stop dictation" : "Start dictation"}
      style={style}
    >
      {isRecording ? (
        <MicOff className="h-4 w-4" />
      ) : (
        <Mic className="h-4 w-4" />
      )}
    </Button>
  );
};