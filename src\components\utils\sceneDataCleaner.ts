import { logger } from '../../utils/logger';

/**
 * Utility functions for cleaning scene data from templates
 */

/**
 * Strips all scenes from episode data while preserving structure
 * @param projectId The project ID
 * @returns True if successful, false otherwise
 */
export function stripScenesFromEpisodeData(projectId: string): boolean {
  if (!projectId) {
    logger.warn('No project ID provided to stripScenesFromEpisodeData');
    return false;
  }
  
  try {
    // Get episode data
    const episodeDataKey = `project_${projectId}_episode_data`;
    const episodeDataStr = localStorage.getItem(episodeDataKey);
    
    if (!episodeDataStr) {
      logger.warn(`No episode data found for project ${projectId}`);
      return false;
    }
    
    // Parse episode data
    const episodeData = JSON.parse(episodeDataStr);
    
    // Strip scenes from each act
    const strippedData = Object.entries(episodeData).reduce((acc, [episodeId, episodeInfo]: [string, any]) => {
      // Process each act in the episode
      const strippedActs = episodeInfo.acts.map((act: any) => ({
        ...act,
        scenes: [] // Replace scenes with empty array
      }));
      
      // Update the episode with stripped acts
      acc[episodeId] = {
        ...episodeInfo,
        acts: strippedActs
      };
      
      return acc;
    }, {} as Record<string, any>);
    
    // Save stripped data back to localStorage
    localStorage.setItem(episodeDataKey, JSON.stringify(strippedData));
    
    logger.debug(`Successfully stripped scenes from episode data for project ${projectId}`);
    return true;
  } catch (error) {
    logger.error('Failed to strip scenes from episode data:', error);
    return false;
  }
}

/**
 * Resets a project to a completely blank state
 * @param projectId The project ID
 * @returns True if successful, false otherwise
 */
export function resetProjectToBlankState(projectId: string): boolean {
  if (!projectId) {
    logger.warn('No project ID provided to resetProjectToBlankState');
    return false;
  }
  
  try {
    // First, find all scene-related keys
    const sceneKeys = findSceneKeys(projectId);
    
    // Remove all scene-related keys
    sceneKeys.forEach(key => {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        logger.error(`Failed to remove key ${key}:`, error);
      }
    });
    
    // Enable blank templates mode
    localStorage.setItem('use_blank_templates', 'true');
    
    logger.debug(`Reset project ${projectId} to blank state`);
    return true;
  } catch (error) {
    logger.error('Failed to reset project to blank state:', error);
    return false;
  }
}

/**
 * Finds all scene-related keys for a project
 * @param projectId The project ID
 * @returns Array of localStorage keys
 */
function findSceneKeys(projectId: string): string[] {
  if (!projectId) return [];
  
  const keys = [];
  
  // Patterns to match
  const patterns = [
    `project_${projectId}_scene_`,
    `project_${projectId}_episode_data`,
    'write_scene_',
    'episode_data'
  ];
  
  // Find all matching keys
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && patterns.some(pattern => key.includes(pattern))) {
      keys.push(key);
    }
  }
  
  return keys;
}