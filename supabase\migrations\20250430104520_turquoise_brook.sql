/*
  # Writing Module Schema

  1. New Tables
    - `scene_content` - Stores the actual content of scenes with version tracking
    - `scene_metadata` - Stores metadata about scenes (characters, emotional arcs, etc.)
    - `scene_versions` - Tracks historical versions of scene content
    - `scene_notes` - Stores notes and comments on scenes
    - `characters` - Character information for the project
    - `character_scenes` - Junction table for characters in scenes

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
    - Ensure proper cascading deletes
*/

-- Scene content with version tracking
CREATE TABLE IF NOT EXISTS scene_content (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  scene_id UUID NOT NULL REFERENCES scenes(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  fountain_content TEXT NOT NULL,
  version INTEGER NOT NULL DEFAULT 1,
  is_current BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Scene metadata
CREATE TABLE IF NOT EXISTS scene_metadata (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  scene_id UUID NOT NULL REFERENCES scenes(id) ON DELETE CASCADE,
  characters JSONB DEFAULT '[]'::jsonb,
  emotional_start TEXT DEFAULT 'Neutral',
  emotional_end TEXT DEFAULT 'Neutral',
  emotional_progress INTEGER DEFAULT 50,
  is_positive_arc BOOLEAN DEFAULT true,
  beats JSONB DEFAULT '[]'::jsonb,
  notes JSONB DEFAULT '[]'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE(scene_id)
);

-- Scene version history
CREATE TABLE IF NOT EXISTS scene_versions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  scene_id UUID NOT NULL REFERENCES scenes(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  fountain_content TEXT NOT NULL,
  version INTEGER NOT NULL,
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Scene notes and comments
CREATE TABLE IF NOT EXISTS scene_notes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  scene_id UUID NOT NULL REFERENCES scenes(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  content TEXT NOT NULL,
  position JSONB,
  resolved BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Characters
CREATE TABLE IF NOT EXISTS characters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  role TEXT NOT NULL,
  description TEXT,
  goals TEXT[] DEFAULT '{}',
  traits JSONB DEFAULT '{
    "physical": [],
    "personality": [],
    "skills": [],
    "flaws": []
  }'::jsonb,
  relationships JSONB DEFAULT '{}'::jsonb,
  arc JSONB DEFAULT '{
    "starting_point": "",
    "midpoint": "",
    "ending": "",
    "key_moments": []
  }'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Character appearances in scenes
CREATE TABLE IF NOT EXISTS character_scenes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
  scene_id UUID NOT NULL REFERENCES scenes(id) ON DELETE CASCADE,
  is_active BOOLEAN NOT NULL DEFAULT true,
  emotional_state TEXT,
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE(character_id, scene_id)
);

-- Add indexes
CREATE INDEX IF NOT EXISTS scene_content_scene_id_idx ON scene_content(scene_id);
CREATE INDEX IF NOT EXISTS scene_content_is_current_idx ON scene_content(is_current);
CREATE INDEX IF NOT EXISTS scene_metadata_scene_id_idx ON scene_metadata(scene_id);
CREATE INDEX IF NOT EXISTS scene_versions_scene_id_idx ON scene_versions(scene_id);
CREATE INDEX IF NOT EXISTS scene_versions_version_idx ON scene_versions(version);
CREATE INDEX IF NOT EXISTS scene_notes_scene_id_idx ON scene_notes(scene_id);
CREATE INDEX IF NOT EXISTS characters_project_id_idx ON characters(project_id);
CREATE INDEX IF NOT EXISTS character_scenes_character_id_idx ON character_scenes(character_id);
CREATE INDEX IF NOT EXISTS character_scenes_scene_id_idx ON character_scenes(scene_id);

-- Enable RLS
ALTER TABLE scene_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE scene_metadata ENABLE ROW LEVEL SECURITY;
ALTER TABLE scene_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE scene_notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE characters ENABLE ROW LEVEL SECURITY;
ALTER TABLE character_scenes ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can access scene content in their projects"
  ON scene_content
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM scenes
    JOIN acts ON acts.id = scenes.act_id
    JOIN episodes ON episodes.id = acts.episode_id
    JOIN projects ON projects.id = episodes.project_id
    WHERE scenes.id = scene_content.scene_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY "Users can access scene metadata in their projects"
  ON scene_metadata
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM scenes
    JOIN acts ON acts.id = scenes.act_id
    JOIN episodes ON episodes.id = acts.episode_id
    JOIN projects ON projects.id = episodes.project_id
    WHERE scenes.id = scene_metadata.scene_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY "Users can access scene versions in their projects"
  ON scene_versions
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM scenes
    JOIN acts ON acts.id = scenes.act_id
    JOIN episodes ON episodes.id = acts.episode_id
    JOIN projects ON projects.id = episodes.project_id
    WHERE scenes.id = scene_versions.scene_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY "Users can access scene notes in their projects"
  ON scene_notes
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM scenes
    JOIN acts ON acts.id = scenes.act_id
    JOIN episodes ON episodes.id = acts.episode_id
    JOIN projects ON projects.id = episodes.project_id
    WHERE scenes.id = scene_notes.scene_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY "Users can access characters in their projects"
  ON characters
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM projects
    WHERE projects.id = characters.project_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY "Users can access character scenes in their projects"
  ON character_scenes
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM characters
    JOIN projects ON projects.id = characters.project_id
    WHERE characters.id = character_scenes.character_id
    AND projects.user_id = auth.uid()
  ));

-- Add update timestamp triggers
CREATE TRIGGER scene_content_updated_at
  BEFORE UPDATE ON scene_content
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER scene_metadata_updated_at
  BEFORE UPDATE ON scene_metadata
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER scene_notes_updated_at
  BEFORE UPDATE ON scene_notes
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER characters_updated_at
  BEFORE UPDATE ON characters
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

-- Function to get character appearances
CREATE OR REPLACE FUNCTION get_character_appearances(
  character_id_param uuid,
  project_id_param uuid
)
RETURNS TABLE (
  scene_id uuid,
  episode_number integer,
  act_title text,
  scene_title text,
  is_active boolean,
  emotional_state text
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.id as scene_id,
    e.number as episode_number,
    a.title as act_title,
    s.title as scene_title,
    cs.is_active,
    cs.emotional_state
  FROM character_scenes cs
  JOIN scenes s ON s.id = cs.scene_id
  JOIN acts a ON a.id = s.act_id
  JOIN episodes e ON e.id = a.episode_id
  WHERE cs.character_id = character_id_param
  AND e.project_id = project_id_param
  ORDER BY e.number, a.position, s.position;
END;
$$ LANGUAGE plpgsql;