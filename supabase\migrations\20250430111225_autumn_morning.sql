/*
  # Idea Pile Schema

  1. New Tables
    - `idea_items`
      - Core table for storing ideas and notes
      - Supports rich content types (text, images, links)
      - Includes metadata and organization fields
    - `idea_tags`
      - Tag management for ideas
    - `idea_references`
      - Cross-references between ideas and projects
  
  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
*/

-- Check if tables already exist before creating them
DO $$
BEGIN
  -- Create idea_items table if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'idea_items') THEN
    CREATE TABLE idea_items (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID NOT NULL REFERENCES auth.users(id),
      title TEXT NOT NULL,
      content TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('note', 'image', 'voice', 'link', 'reference')),
      status TEXT NOT NULL DEFAULT 'unprocessed' CHECK (status IN ('unprocessed', 'inuse', 'archived')),
      category TEXT NOT NULL CHECK (category IN ('capture', 'inspiration', 'reference')),
      source TEXT,
      metadata JSONB DEFAULT '{}'::jsonb,
      created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
      updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
    );

    -- Add indexes for idea_items
    CREATE INDEX idea_items_user_id_idx ON idea_items(user_id);
    CREATE INDEX idea_items_type_idx ON idea_items(type);
    CREATE INDEX idea_items_status_idx ON idea_items(status);
    CREATE INDEX idea_items_category_idx ON idea_items(category);
    CREATE INDEX idea_items_created_at_idx ON idea_items(created_at DESC);
    
    -- Enable RLS
    ALTER TABLE idea_items ENABLE ROW LEVEL SECURITY;
    
    -- Create RLS policy
    CREATE POLICY "Users can manage their own ideas"
      ON idea_items
      FOR ALL
      USING (auth.uid() = user_id);
  END IF;

  -- Create idea_tags table if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'idea_tags') THEN
    CREATE TABLE idea_tags (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      idea_id UUID NOT NULL REFERENCES idea_items(id) ON DELETE CASCADE,
      tag TEXT NOT NULL,
      created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
      UNIQUE(idea_id, tag)
    );

    -- Add indexes for idea_tags
    CREATE INDEX idea_tags_idea_id_idx ON idea_tags(idea_id);
    CREATE INDEX idea_tags_tag_idx ON idea_tags(tag);
    
    -- Enable RLS
    ALTER TABLE idea_tags ENABLE ROW LEVEL SECURITY;
    
    -- Create RLS policy
    CREATE POLICY "Users can manage tags for their ideas"
      ON idea_tags
      FOR ALL
      USING (EXISTS (
        SELECT 1 FROM idea_items
        WHERE idea_items.id = idea_tags.idea_id
        AND idea_items.user_id = auth.uid()
      ));
  END IF;

  -- Create idea_references table if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'idea_references') THEN
    CREATE TABLE idea_references (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      idea_id UUID NOT NULL REFERENCES idea_items(id) ON DELETE CASCADE,
      project_id UUID NOT NULL,
      project_name TEXT NOT NULL,
      usage TEXT NOT NULL,
      date_used TIMESTAMPTZ NOT NULL DEFAULT now(),
      created_at TIMESTAMPTZ NOT NULL DEFAULT now()
    );

    -- Add indexes for idea_references
    CREATE INDEX idea_references_idea_id_idx ON idea_references(idea_id);
    CREATE INDEX idea_references_project_id_idx ON idea_references(project_id);
    
    -- Enable RLS
    ALTER TABLE idea_references ENABLE ROW LEVEL SECURITY;
    
    -- Create RLS policy
    CREATE POLICY "Users can manage references for their ideas"
      ON idea_references
      FOR ALL
      USING (EXISTS (
        SELECT 1 FROM idea_items
        WHERE idea_items.id = idea_references.idea_id
        AND idea_items.user_id = auth.uid()
      ));
  END IF;
  
  -- Add full text search to idea_items if not already present
  IF NOT EXISTS (
    SELECT 1 FROM pg_attribute 
    WHERE attrelid = 'idea_items'::regclass 
    AND attname = 'fts' 
    AND NOT attisdropped
  ) THEN
    ALTER TABLE idea_items ADD COLUMN fts TSVECTOR 
      GENERATED ALWAYS AS (
        setweight(to_tsvector('english', coalesce(title, '')), 'A') ||
        setweight(to_tsvector('english', coalesce(content, '')), 'B')
      ) STORED;
      
    CREATE INDEX idea_items_fts_idx ON idea_items USING gin(fts);
  END IF;
END$$;