/*
  # Initial Schema for Story Unstuck

  1. Core Tables
    - `users` - User profiles linked to auth.users
    - `projects` - Story projects (TV shows, films)
    - `episodes` - Episodes within projects
    - `acts` - Acts within episodes
    - `scenes` - Scenes within acts
    - `storylines` - Story arcs across episodes
    - `storyline_episodes` - Junction table for storylines and episodes

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
    - Ensure proper cascading deletes
*/

-- Core tables
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  email TEXT NOT NULL UNIQUE,
  display_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  last_login TIMESTAMPTZ
);

CREATE TABLE IF NOT EXISTS projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('tv-30', 'tv-60', 'film')),
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'completed')),
  template TEXT NOT NULL DEFAULT 'classic' CHECK (template IN ('classic', 'enhanced')),
  user_id UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS episodes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  number INTEGER NOT NULL,
  title TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'in_progress', 'complete')),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE(project_id, number)
);

CREATE TABLE IF NOT EXISTS storylines (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  color TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS acts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  episode_id UUID NOT NULL REFERENCES episodes(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  subtitle TEXT,
  color TEXT NOT NULL,
  position INTEGER NOT NULL,
  storyline_id UUID REFERENCES storylines(id),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS scenes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  act_id UUID NOT NULL REFERENCES acts(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  content TEXT,
  position INTEGER NOT NULL,
  storyline_id UUID REFERENCES storylines(id),
  is_complete BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS storyline_episodes (
  storyline_id UUID NOT NULL REFERENCES storylines(id) ON DELETE CASCADE,
  episode_id UUID NOT NULL REFERENCES episodes(id) ON DELETE CASCADE,
  is_active BOOLEAN NOT NULL DEFAULT true,
  PRIMARY KEY (storyline_id, episode_id)
);

-- Workshop module tables
CREATE TABLE IF NOT EXISTS workshop_quadrants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  position INTEGER NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS workshop_sections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  quadrant_id UUID NOT NULL REFERENCES workshop_quadrants(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  subtitle TEXT,
  color TEXT NOT NULL,
  guide TEXT,
  position INTEGER NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS workshop_stories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  quadrant_id UUID NOT NULL REFERENCES workshop_quadrants(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  content TEXT,
  position INTEGER NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS plot_points (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  section_id UUID NOT NULL REFERENCES workshop_sections(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  position INTEGER NOT NULL,
  source TEXT NOT NULL CHECK (source IN ('workshop', 'chat', 'manual')),
  color TEXT,
  storyline_id UUID REFERENCES storylines(id),
  episode_id UUID REFERENCES episodes(id),
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add indexes for common queries
CREATE INDEX IF NOT EXISTS projects_user_id_idx ON projects(user_id);
CREATE INDEX IF NOT EXISTS episodes_project_id_idx ON episodes(project_id);
CREATE INDEX IF NOT EXISTS episodes_number_idx ON episodes(number);
CREATE INDEX IF NOT EXISTS acts_episode_id_idx ON acts(episode_id);
CREATE INDEX IF NOT EXISTS acts_position_idx ON acts(position);
CREATE INDEX IF NOT EXISTS scenes_act_id_idx ON scenes(act_id);
CREATE INDEX IF NOT EXISTS scenes_position_idx ON scenes(position);
CREATE INDEX IF NOT EXISTS storylines_project_id_idx ON storylines(project_id);
CREATE INDEX IF NOT EXISTS workshop_quadrants_project_id_idx ON workshop_quadrants(project_id);
CREATE INDEX IF NOT EXISTS workshop_sections_quadrant_id_idx ON workshop_sections(quadrant_id);
CREATE INDEX IF NOT EXISTS workshop_stories_quadrant_id_idx ON workshop_stories(quadrant_id);
CREATE INDEX IF NOT EXISTS plot_points_section_id_idx ON plot_points(section_id);

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE episodes ENABLE ROW LEVEL SECURITY;
ALTER TABLE acts ENABLE ROW LEVEL SECURITY;
ALTER TABLE scenes ENABLE ROW LEVEL SECURITY;
ALTER TABLE storylines ENABLE ROW LEVEL SECURITY;
ALTER TABLE storyline_episodes ENABLE ROW LEVEL SECURITY;
ALTER TABLE workshop_quadrants ENABLE ROW LEVEL SECURITY;
ALTER TABLE workshop_sections ENABLE ROW LEVEL SECURITY;
ALTER TABLE workshop_stories ENABLE ROW LEVEL SECURITY;
ALTER TABLE plot_points ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can access their own profile"
  ON users
  FOR ALL
  USING (auth.uid() = id);

CREATE POLICY "Users can manage their own projects"
  ON projects
  FOR ALL
  USING (auth.uid() = user_id);

CREATE POLICY "Users can access episodes in their projects"
  ON episodes
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM projects
    WHERE projects.id = episodes.project_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY "Users can access acts in their episodes"
  ON acts
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM episodes
    JOIN projects ON projects.id = episodes.project_id
    WHERE episodes.id = acts.episode_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY "Users can access scenes in their acts"
  ON scenes
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM acts
    JOIN episodes ON episodes.id = acts.episode_id
    JOIN projects ON projects.id = episodes.project_id
    WHERE acts.id = scenes.act_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY "Users can access storylines in their projects"
  ON storylines
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM projects
    WHERE projects.id = storylines.project_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY "Users can access storyline_episodes in their projects"
  ON storyline_episodes
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM storylines
    JOIN projects ON projects.id = storylines.project_id
    WHERE storylines.id = storyline_episodes.storyline_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY "Users can access workshop quadrants in their projects"
  ON workshop_quadrants
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM projects
    WHERE projects.id = workshop_quadrants.project_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY "Users can access workshop sections in their quadrants"
  ON workshop_sections
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM workshop_quadrants
    JOIN projects ON projects.id = workshop_quadrants.project_id
    WHERE workshop_quadrants.id = workshop_sections.quadrant_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY "Users can access workshop stories in their quadrants"
  ON workshop_stories
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM workshop_quadrants
    JOIN projects ON projects.id = workshop_quadrants.project_id
    WHERE workshop_quadrants.id = workshop_stories.quadrant_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY "Users can access plot points in their sections"
  ON plot_points
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM workshop_sections
    JOIN workshop_quadrants ON workshop_quadrants.id = workshop_sections.quadrant_id
    JOIN projects ON projects.id = workshop_quadrants.project_id
    WHERE workshop_sections.id = plot_points.section_id
    AND projects.user_id = auth.uid()
  ));

-- Functions for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add update timestamp triggers
CREATE TRIGGER projects_updated_at
  BEFORE UPDATE ON projects
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER episodes_updated_at
  BEFORE UPDATE ON episodes
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER acts_updated_at
  BEFORE UPDATE ON acts
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER scenes_updated_at
  BEFORE UPDATE ON scenes
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER storylines_updated_at
  BEFORE UPDATE ON storylines
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER workshop_quadrants_updated_at
  BEFORE UPDATE ON workshop_quadrants
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER workshop_sections_updated_at
  BEFORE UPDATE ON workshop_sections
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER workshop_stories_updated_at
  BEFORE UPDATE ON workshop_stories
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();