import React, { useState } from 'react';
import { TaskColumn } from '../tasks/TaskColumn';
import { useTemplate } from '../../hooks/useTemplate';
import { RevisionNotesView } from '../tasks/revision/RevisionNotesView';
import type { Task } from '../../types/task';

const defaultColumns = [
  { id: 'inbox', title: 'INBOX', color: 'bg-gray-500' },
  { id: 'ideation', title: 'IDEATION', color: 'bg-red-500' },
  { id: 'refine', title: 'REFINE', color: 'bg-blue-500' },
  { id: 'systemization', title: 'SYSTEMIZATION', color: 'bg-yellow-500' },
  { id: 'implementation', title: 'IMPLEMENTATION', color: 'bg-green-500' },
  { id: 'archive', title: 'ARCHIVE', color: 'bg-gray-700' }
];

export const TaskView: React.FC = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [activeTab, setActiveTab] = useState<'tasks' | 'revision'>('revision');
  const {template} = useTemplate();

  // Render the RevisionNotesView by default
  if (activeTab === 'revision') {
    return (
      <div className="h-[calc(100vh-4rem)] flex flex-col overflow-hidden">
        <div className="border-b-4 border-black">
          <div className="flex p-2">
            <button 
              className={`px-4 py-2 mr-2 font-bold ${activeTab === 'tasks' 
                ? 'bg-black text-white' 
                : 'bg-white text-black border-2 border-black'}`}
              onClick={() => setActiveTab('tasks')}
            >
              Tasks
            </button>
            <button 
              className={`px-4 py-2 font-bold ${activeTab === 'revision' 
                ? 'bg-black text-white' 
                : 'bg-white text-black border-2 border-black'}`}
              onClick={() => setActiveTab('revision')}
            >
              Revision Notes
            </button>
          </div>
        </div>
        
        <div className="flex-1 p-4 overflow-y-auto">
          <RevisionNotesView />
        </div>
      </div>
    );
  }
  
  const handleAddTask = (columnId: string) => {
    const newTask: Task = {
      id: Date.now().toString(),
      title: 'New Task',
      content: '',
      comments: [],
      labels: [],
      columnId
    };
    setTasks([...tasks, newTask]);
  };

  const handleUpdateTask = (taskId: string, updates: Partial<Task>) => {
    setTasks(prev =>
      prev.map(task =>
        task.id === taskId ? { ...task, ...updates } : task
      )
    );
  };

  const handleDeleteTask = (taskId: string) => {
    setTasks(prev => prev.filter(task => task.id !== taskId));
  };

  const handleMoveTask = (taskId: string, targetColumnId: string) => {
    setTasks(prev =>
      prev.map(task =>
        task.id === taskId ? { ...task, columnId: targetColumnId } : task
      )
    );
  };

  const handleAddComment = (taskId: string, content: string) => {
    setTasks(prev =>
      prev.map(task =>
        task.id === taskId
          ? {
              ...task,
              comments: [
                ...task.comments,
                { 
                  id: Date.now().toString(), 
                  content, 
                  timestamp: Date.now(),
                  author: 'User'
                }
              ]
            }
          : task
      )
    );
  };

  return (
    <div className="h-[calc(100vh-4rem)] flex flex-col overflow-hidden">
      <div className="border-b-4 border-black">
        <div className="flex p-2">
          <button 
            className={`px-4 py-2 mr-2 font-bold ${activeTab === 'tasks' 
              ? 'bg-black text-white' 
              : 'bg-white text-black border-2 border-black'}`}
            onClick={() => setActiveTab('tasks')}
          >
            Tasks
          </button>
          <button 
            className={`px-4 py-2 font-bold ${activeTab === 'revision' 
              ? 'bg-black text-white' 
              : 'bg-white text-black border-2 border-black'}`}
            onClick={() => setActiveTab('revision')}
          >
            Revision Notes
          </button>
        </div>
      </div>
      
      {activeTab === 'tasks' ? (
        <div className="flex-1 overflow-x-auto">
          <div className="flex gap-4 p-4 min-w-max h-full">
            {defaultColumns.map(column => (
              <TaskColumn
                key={column.id}
                id={column.id}
                title={column.title}
                color={column.color}
                tasks={tasks.filter(task => task.columnId === column.id)}
                onAddTask={handleAddTask}
                onUpdateTask={handleUpdateTask}
                onDeleteTask={handleDeleteTask}
                onMoveTask={handleMoveTask}
                onAddComment={handleAddComment}
              />
            ))}
          </div>
        </div>
      ) : (
        <div className="flex-1 p-4 overflow-y-auto">
          <RevisionNotesView />
        </div>
      )}
    </div>
  );
};