# Task: Update AI Assistant to be more ChatGPT-like

## Changes Required:
1. Modify the system prompt in `src/api/aiService.ts` to be more general-purpose
2. Update the ChatContext to use this prompt instead of an empty one
3. Test the changes to ensure the AI responds to a wider range of topics

## Implementation Details:
- Replace the screenwriting-focused prompt with a more versatile one
- Ensure the context is maintained throughout conversations
- Keep the same OpenAI integration but with broader capabilities

## Files to modify:
- src/api/aiService.ts
- src/contexts/ChatContext.tsx