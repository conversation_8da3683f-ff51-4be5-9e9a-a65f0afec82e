import React, { useState, useEffect } from 'react';
import { useProject } from '../../contexts/ProjectContext';
import { supabase } from '../../lib/supabase/client';
import { Button } from '../ui/button';

export function ProjectStorageTest() {
  const { createProject, projects, currentProject, saveProject, updateProject } = useProject();
  const [testResult, setTestResult] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [supabaseProjects, setSupabaseProjects] = useState<any[]>([]);
  const [localProjects, setLocalProjects] = useState<any[]>([]);

  // Test creating a project
  const handleCreateProject = async () => {
    setIsLoading(true);
    setTestResult('Creating test project...');
    try {
      createProject(`Test Project ${Date.now()}`, 'tv-30');
      setTestResult('Project created successfully!');
    } catch (error) {
      setTestResult(`Error creating project: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Test saving a project
  const handleSaveProject = () => {
    setIsLoading(true);
    setTestResult('Saving current project...');
    try {
      if (!currentProject) {
        setTestResult('No current project to save!');
        setIsLoading(false);
        return;
      }
      
      saveProject({ name: `${currentProject.name} (Updated)` });
      setTestResult('Project saved successfully!');
    } catch (error) {
      setTestResult(`Error saving project: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Test updating a project
  const handleUpdateProject = () => {
    setIsLoading(true);
    setTestResult('Updating current project...');
    try {
      if (!currentProject) {
        setTestResult('No current project to update!');
        setIsLoading(false);
        return;
      }
      
      updateProject({ status: 'active' });
      setTestResult('Project updated successfully!');
    } catch (error) {
      setTestResult(`Error updating project: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Check Supabase directly
  const checkSupabase = async () => {
    setIsLoading(true);
    setTestResult('Checking Supabase projects...');
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .order('created_at', { ascending: false });
        
      if (error) throw error;
      
      setSupabaseProjects(data || []);
      setTestResult(`Found ${data?.length || 0} projects in Supabase`);
    } catch (error) {
      setTestResult(`Error checking Supabase: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Check localStorage directly
  const checkLocalStorage = () => {
    setIsLoading(true);
    setTestResult('Checking localStorage projects...');
    try {
      const savedProjects = localStorage.getItem('story_unstuck_projects');
      const parsedProjects = savedProjects ? JSON.parse(savedProjects) : [];
      
      setLocalProjects(parsedProjects);
      setTestResult(`Found ${parsedProjects.length} projects in localStorage`);
    } catch (error) {
      setTestResult(`Error checking localStorage: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsLoading(false);
    }
  };
  // Check auth status
  const checkAuth = async () => {
    setIsLoading(true);
    setTestResult('Checking authentication status...');
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        setTestResult(`Authenticated as: ${session.user.email}`);
      } else {
        setTestResult('Not authenticated');
      }
    } catch (error) {
      setTestResult(`Error checking auth: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 border-4 border-black bg-white">
      <h2 className="text-xl font-bold mb-4">Project Storage Test</h2>
      {import.meta.env.DEV && (
        <button
          style={{
            position: 'relative',
            zIndex: 9999,
            background: 'red',
            color: 'white',
            padding: '6px 12px',
            borderRadius: 4,
            fontWeight: 'bold',
            border: 'none',
            cursor: 'pointer',
            marginBottom: 16
          }}
          onClick={() => {
            localStorage.removeItem('chat_widget_settings');
            localStorage.removeItem('saved-chats');
            window.location.reload();
          }}
        >
          Clear Chat Storage (Dev Only)
        </button>
      )}
      
      <div className="space-y-4 mb-6">
        <div className="flex gap-2">
          <Button onClick={handleCreateProject} disabled={isLoading}>
            Create Test Project
          </Button>
          <Button onClick={handleSaveProject} disabled={isLoading || !currentProject}>
            Save Current Project
          </Button>
          <Button onClick={handleUpdateProject} disabled={isLoading || !currentProject}>
            Update Current Project
          </Button>
          <Button onClick={checkSupabase} disabled={isLoading}>
            Check Supabase
          </Button>
          <Button onClick={checkLocalStorage} disabled={isLoading}>
            Check LocalStorage
          </Button>
          <Button onClick={checkAuth} disabled={isLoading}>
            Check Auth
          </Button>
        </div>
        
        <div className="p-4 bg-gray-100 border-2 border-black">
          <p className="font-mono">{testResult || 'No test run yet'}</p>
        </div>
      </div>
      
      <div className="mb-6">
        <h3 className="font-bold mb-2">Current Project:</h3>
        <pre className="p-4 bg-gray-100 border-2 border-black overflow-auto max-h-40">
          {currentProject ? JSON.stringify(currentProject, null, 2) : 'No current project'}
        </pre>
      </div>
      
      <div className="mb-6">
        <h3 className="font-bold mb-2">Context Projects ({projects.length}):</h3>
        <pre className="p-4 bg-gray-100 border-2 border-black overflow-auto max-h-40">
          {JSON.stringify(projects, null, 2)}
        </pre>
      </div>
      
      <div>
        <h3 className="font-bold mb-2">Supabase Projects ({supabaseProjects.length}):</h3>
        <pre className="p-4 bg-gray-100 border-2 border-black overflow-auto max-h-40">
          {JSON.stringify(supabaseProjects, null, 2)}
        </pre>
      </div>
      
      <div>
        <h3 className="font-bold mb-2">LocalStorage Projects ({localProjects.length}):</h3>
        <pre className="p-4 bg-gray-100 border-2 border-black overflow-auto max-h-40">
          {JSON.stringify(localProjects, null, 2)}
        </pre>
      </div>
    </div>
  );
}