import React, { useState } from 'react';
import { EpisodeSpine } from './EpisodeSpine';
import { ActCard } from './ActCard';
import { Plus, ArrowRightSquare } from 'lucide-react'; // Added ArrowRightSquare
import { SceneCard } from './SceneCard';
import { useEpisodeState } from '../../hooks/useEpisodeState';
import { useTemplate } from '../../hooks/useTemplate';
import { useProject } from '../../contexts/ProjectContext';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';

export const EpisodesView: React.FC = () => {
  const {template} = useTemplate();
  const { currentProject } = useProject();
  const projectType = currentProject?.type || 'tv-30';

  const {
    episodes,
    openEpisodes,
    episodeData,
    toggleEpisode,
    addEpisode,
    updateEpisodeData
  } = useEpisodeState();

  const [draggedScene, setDraggedScene] = useState<{ id: number, actId: string } | null>(null);

  // Debug logging to check episode data
  console.log('Episodes View Render:', {
    episodeCount: episodes.length,
    openEpisodes: Array.from(openEpisodes),
    episodeDataKeys: Object.keys(episodeData)
  });

  // Log a sample of the first episode's data if available
  if (episodes.length > 0 && episodeData[episodes[0]]) {
    console.log('First Episode Data Sample:', {
      episodeId: episodes[0],
      actCount: episodeData[episodes[0]].acts.length,
      firstAct: episodeData[episodes[0]].acts[0]
    });
  }

  // Determine column width based on project type
  const getColumnWidth = () => {
    if (!openEpisodes.size) return '100%';

    // Calculate column width based on project type and number of open episodes
    const columnCount = openEpisodes.size;

    // For film projects, we need to account for the 4 sections
    if (projectType === 'film') {
      return `${100 / columnCount}%`;
    }

    // For TV shows, we need to account for the number of acts
    if (projectType === 'tv-30') {
      return `${100 / columnCount}%`; // 7 columns per episode
    }

    if (projectType === 'tv-60') {
      return `${100 / columnCount}%`; // 8 columns per episode
    }

    // For novels, each chapter is a column
    if (projectType === 'novel') {
      return `${100 / columnCount}%`;
    }

    return `${100 / columnCount}%`;
  };

  const handleSceneUpdate = (episodeId: number, actId: string, sceneId: number, updates: Partial<any>) => {
    updateEpisodeData(episodeId, data => ({
      ...data,
      acts: data.acts.map(act =>
        act.id === actId
          ? {
              ...act,
              scenes: act.scenes.map(scene =>
                scene.id === sceneId ? { ...scene, ...updates } : scene
              )
            }
          : act
      )
    }));
  };

  const handleSceneDelete = (episodeId: number, actId: string, sceneId: number) => {
    console.log('[EpisodesView] Attempting to delete scene:', { episodeId, actId, sceneId });
    updateEpisodeData(episodeId, data => {
      // It's good practice to log the state *before* the change to understand the context.
      // Using JSON.parse(JSON.stringify(data)) for a deep clone if data is complex and might be mutated elsewhere.
      // console.log('[EpisodesView] Episode data before deletion:', JSON.parse(JSON.stringify(data)));

      const updatedActs = data.acts.map(act => {
        if (act.id === actId) {
          const scenesBeforeFilter = act.scenes.length;
          const newScenes = act.scenes.filter(scene => scene.id !== sceneId);
          
          if (scenesBeforeFilter !== newScenes.length) {
            console.log(`[EpisodesView] Scene ${sceneId} successfully identified for filtering from act ${actId} in episode ${episodeId}. Scenes count: ${scenesBeforeFilter} -> ${newScenes.length}`);
          } else {
            console.warn(`[EpisodesView] Scene ${sceneId} NOT filtered from act ${actId} in episode ${episodeId}. Scene ID not found or already removed. Current scenes:`, act.scenes.map(s => s.id));
          }
          return { ...act, scenes: newScenes };
        }
        return act;
      });

      const updatedEpisode = { ...data, acts: updatedActs };
      // console.log('[EpisodesView] Episode data after attempted deletion:', JSON.parse(JSON.stringify(updatedEpisode)));
      return updatedEpisode;
    });
    // Add a log *after* the state update is dispatched, though the actual state update is asynchronous.
    console.log(`[EpisodesView] Dispatched updateEpisodeData for scene deletion: ${sceneId}`);
  };

  const handleSceneDragStart = (e: React.DragEvent, scene: any, actId: string, index: number) => {
    setDraggedScene({ id: scene.id, actId });
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleSceneDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleSceneDrop = (e: React.DragEvent, targetEpisodeId: number, targetActId: string, targetIndex: number) => {
    e.preventDefault();

    if (!draggedScene) return;

    // Get source episode and act
    const sourceEpisodeId = targetEpisodeId; // For now, we're only handling drops within the same episode
    const sourceAct = episodeData[sourceEpisodeId]?.acts.find(a => a.id === draggedScene.actId);
    const targetAct = episodeData[targetEpisodeId]?.acts.find(a => a.id === targetActId);

    if (!sourceAct || !targetAct) return;

    // Find the scene to move
    const sceneToMove = sourceAct.scenes.find(s => s.id === draggedScene.id);

    if (!sceneToMove) return;

    // Update episode data
    updateEpisodeData(sourceEpisodeId, data => {
      const updatedActs = data.acts.map(act => {
        if (act.id === draggedScene.actId) {
          // Remove scene from source act
          return {
            ...act,
            scenes: act.scenes.filter(s => s.id !== draggedScene.id)
          };
        }
        return act;
      });

      return {
        ...data,
        acts: updatedActs
      };
    });

    updateEpisodeData(targetEpisodeId, data => {
      const updatedActs = data.acts.map(act => {
        if (act.id === targetActId) {
          // Add scene to target act at the correct position
          const newScenes = [...act.scenes];
          newScenes.splice(targetIndex, 0, sceneToMove);
          return {
            ...act,
            scenes: newScenes
          };
        }
        return act;
      });

      return {
        ...data,
        acts: updatedActs
      };
    });

    setDraggedScene(null);
  };

  const handleStorylineChange = (episodeId: number, actId: string, sceneId: number, storylineId: string | null) => {
    updateEpisodeData(episodeId, data => ({
      ...data,
      acts: data.acts.map(act =>
        act.id === actId
          ? {
              ...act,
              scenes: act.scenes.map(scene =>
                scene.id === sceneId ? { ...scene, storylineId } : scene
              )
            }
          : act
      )
    }));
  };

  // Calculate column width based on number of open episodes
  const columnWidth = openEpisodes.size > 0
    ? `${100 / openEpisodes.size}%`
    : '100%';

  return (
    <div className={`h-[calc(100vh-4rem)] flex w-full`}>
      <div className="flex-1 overflow-x-auto">
        <div className="flex min-h-full w-full">
          {Array.from(openEpisodes)
            .sort((a, b) => a - b)
            .map(ep => (
              <div
                key={ep}
                className="flex-shrink-0 p-2 border-r-4 border-black"
                style={{ width: columnWidth }}
              >
                <div className="flex items-center justify-between mb-2"> {/* Flex container for title and button */}
                  <h1 className="font-mono text-xl font-bold">
                    {projectType === 'novel' ? `Chapter ${ep}` : `EP_${String(ep).padStart(2, '0')}`}
                  </h1>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-1"
                    onClick={() => toggleEpisode(ep)}
                  >
                    <ArrowRightSquare className="h-3 w-3" />
                  </Button>
                </div>
                <div className="space-y-4">
                  {episodeData[ep]?.acts.map(act => (
                    <div key={act.id} className="border-4 border-black mb-4">
                      <div
                        className={cn(
                          'flex items-center justify-between px-2 py-1',
                          act.color,
                          'border-b-4 border-black'
                        )}
                      >
                        <div className="flex flex-col">
                          <h3 className="font-bold text-xs text-white">{act.title}</h3>
                          {act.subtitle && (
                            <p className="text-[10px] text-white/80">{act.subtitle}</p>
                          )}
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            // Add a new scene to this act
                            updateEpisodeData(ep, data => ({
                              ...data,
                              acts: data.acts.map(a =>
                                a.id === act.id
                                  ? {
                                      ...a,
                                      scenes: [
                                        ...a.scenes,
                                        {
                                          id: Date.now(),
                                          title: `Scene ${a.scenes.length + 1}`,
                                          content: '',
                                          storylineId: null
                                        }
                                      ]
                                    }
                                  : a
                              )
                            }));
                          }}
                          className="h-6 w-6 p-1 hover:bg-white/10 text-white"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-3 w-3"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
                        </Button>
                      </div>

                      <div
                        className="p-2 space-y-2 min-h-[50px]"
                        onDragOver={handleSceneDragOver}
                        onDrop={(e) => handleSceneDrop(e, ep, act.id, 0)}
                      >
                        {/* Ensure act.scenes exists and is an array before mapping */}
                        {Array.isArray(act.scenes) && act.scenes.length > 0 ? (
                          act.scenes.map((scene, index) => (
                          <SceneCard
                            key={scene.id}
                            scene={{
                              ...scene,
                              episodeId: ep,
                              actId: act.id
                            }}
                            index={index}
                            groupId={`${ep}-${act.id}`}
                            color={act.color}
                            storylineId={scene.storylineId ?? null}
                            onUpdate={(updates) => handleSceneUpdate(ep, act.id, scene.id, updates)}
                            onDelete={() => handleSceneDelete(ep, act.id, scene.id)}
                            onDragStart={(e) => handleSceneDragStart(e, scene, act.id, index)}
                            onDragOver={handleSceneDragOver}
                            onDrop={(e) => handleSceneDrop(e, ep, act.id, index)}
                            onStorylineChange={(storylineId) => handleStorylineChange(ep, act.id, scene.id, storylineId)}
                            debug={false}
                            episodeActs={episodeData[ep]?.acts}
                          />
                          ))
                        ) : (
                          <div className="text-center text-gray-400 text-xs py-2">
                            No scenes in this act
                          </div>
                        )}

                        {/* Always show Add Scene button */}
                          <Button
                            variant="ghost"
                            size="sm"
                            title="Add a new scene to this act"
                            onClick={() => {
                              updateEpisodeData(ep, data => ({
                                ...data,
                                acts: data.acts.map(a =>
                                  a.id === act.id
                                    ? {
                                        ...a,
                                        scenes: [
                                          ...a.scenes,
                                          {
                                            id: Date.now(),
                                            title: `Scene 1`,
                                            content: '',
                                            storylineId: null
                                          }
                                        ]
                                      }
                                    : a
                                )
                              }));
                            }}
                            className="w-full border-dashed text-xs text-gray-500 hover:bg-gray-50"
                          >
                            <Plus className="h-3 w-3 mr-1" />
                            Add Scene
                          </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
        </div>
      </div>

      <EpisodeSpine
        episodes={episodes}
        openEpisodes={openEpisodes}
        onEpisodeToggle={toggleEpisode}
        onAddEpisode={addEpisode}
      />
    </div>
  );
};