import { useState, useCallback, useEffect } from 'react';
import { 
  episodeCounts, 
  tv30Template, 
  tv60Template, 
  filmTemplate,
  novelChapterTemplate,
  sceneCounts
} from '../constants/templates';
import type { EpisodeData } from '../types/episodes';
import { useProject } from '../contexts/ProjectContext';
import { useEpisodeTrackStore } from '../store/episodeTrackStore';
import { logger } from '../utils/logger';
import { createBlankTemplate } from '../utils/templateUtils';

export function useEpisodeState() {
  const { currentProject } = useProject();
  const projectId = currentProject?.id;
  const storageKeyPrefix = projectId ? `project_${projectId}_` : '';
  
  // Create project-scoped storage keys
  const STORAGE_KEY = `${storageKeyPrefix}episode_data`;
  const DELETED_ACTS_KEY = `${storageKeyPrefix}deleted_acts`;
  
  const { episodes: trackEpisodes } = useEpisodeTrackStore();
  const [openEpisodes, setOpenEpisodes] = useState<Set<number>>(() => {
    // For TV projects, open all 6 episodes by default
    if (currentProject?.type === 'tv-30' || currentProject?.type === 'tv-60') {
      return new Set([1, 2, 3, 4, 5, 6]);
    }
    // For film projects, open episodes 1, 2, 3, and 4 by default
    if (currentProject?.type === 'film') {
      return new Set([1, 2, 3, 4]);
    }
    // For other project types, just open the first episode
    return new Set([1]);
  });
  const [episodeData, setEpisodeData] = useState<Record<number, EpisodeData>>(() => {
    try {
      // Try project-scoped key first, then fall back to legacy key
      const saved = localStorage.getItem(STORAGE_KEY);
      
      // Debug log to check what's being loaded
      console.log('Loading episode data from storage:', { 
        storageKey: STORAGE_KEY, 
        dataExists: !!saved,
        dataLength: saved?.length || 0
      });
      
      if (!saved && projectId) {
        // Try legacy key as fallback
        const legacySaved = localStorage.getItem('episode_data');
        console.log('Checking legacy storage:', { 
          legacyKey: 'episode_data', 
          dataExists: !!legacySaved 
        });
        
        const saved = legacySaved;
        if (saved) {
          // Migrate to new key format
          localStorage.setItem(STORAGE_KEY, saved);
          logger.debug('Migrated episode data from legacy key to project-scoped key');
        }
      }
      
      const parsed = saved ? JSON.parse(saved) : null;
      if (!parsed || Object.keys(parsed).length === 0) {
        // Get the project type to determine which template to use
        const projectType = currentProject?.type || 'tv-30'; 
        console.log('Initializing with template for project type:', projectType);
        const useBlankTemplates = localStorage.getItem('use_blank_templates') === 'true';
        const useEmptyTemplates = localStorage.getItem('use_empty_templates') === 'true';
        
        // Handle different project types
        let templateStructure;
        let templateSceneCounts;
        
        if (projectType === 'novel') {
          // For novels, use the chapter template
          // For novels, we'll initialize just the first chapter
          templateStructure = [novelChapterTemplate(1)];
          templateSceneCounts = { "chapter1": sceneCounts.novel.chapter };
        } else if (projectType === 'tv-30') {
          templateStructure = tv30Template;
          templateSceneCounts = sceneCounts.tv30;
        } else if (projectType === 'tv-60') {
          templateStructure = tv60Template;
          templateSceneCounts = sceneCounts.tv60;
        } else if (projectType === 'film') {
          templateStructure = filmTemplate;
          templateSceneCounts = sceneCounts.film;
        } else {
          // Default fallback
          templateStructure = tv30Template;
          templateSceneCounts = sceneCounts.tv30;
        }
        
        // Create default data with blank scenes for all episodes
        const defaultData: Record<number, EpisodeData> = {};
        
        // For TV projects, initialize 6 episodes with blank templates
        if (projectType === 'tv-30' || projectType === 'tv-60') {
          for (let i = 1; i <= 6; i++) {
            // Create scenes array for each act
            const actsWithScenes = templateStructure.map(act => ({
              ...act,
              scenes: useBlankTemplates || useEmptyTemplates 
                ? [] 
                : Array.from({ length: templateSceneCounts[act.id] || 2 }, (_, j) => ({
                  id: Date.now() + (i * 100) + j,
                  title: `Scene ${j + 1}`,
                  content: '',
                  storylineId: null
                }))
            }));
            
            defaultData[i] = {
              acts: actsWithScenes
            };
          }
        } else {
          // For other project types, just initialize the first episode
          defaultData[1] = {
            acts: useBlankTemplates || useEmptyTemplates
              ? createBlankTemplate(templateStructure)
              : templateStructure.map(act => ({
                  ...act,
                  scenes: Array.from({ length: templateSceneCounts[act.id] || 2 }, (_, i) => ({
                    id: Date.now() + i,
                    title: `Scene ${i + 1}`,
                    content: '',
                    storylineId: null
                  })),
                  storylineId: null
                }))
          };
        }
        
        localStorage.setItem(STORAGE_KEY, JSON.stringify(defaultData));
        console.log('Created default episode data:', { 
          episodeCount: Object.keys(defaultData).length,
          firstEpisode: defaultData[1]
        });
        return defaultData;
      }
      
      // Log the parsed data for debugging
      console.log('Loaded episode data:', {
        episodeCount: Object.keys(parsed).length,
        firstEpisode: parsed[1] ? {
          actCount: parsed[1].acts.length,
          firstActSceneCount: parsed[1].acts[0]?.scenes?.length || 0
        } : 'No episode 1'
      });
      
      return parsed;
    } catch {
      console.error('Error loading episode data:', error);
      return {};
    }
  });

  const [deletedActs, setDeletedActs] = useState<{
    acts: Array<{ id: string; title: string; color: string; scenes: any[] }>;
    episodeData: Record<number, EpisodeData>;
    timestamp: number;
  } | null>(() => {
    try {
      // Try project-scoped key first, then fall back to legacy key
      let saved = localStorage.getItem(DELETED_ACTS_KEY);
      if (!saved && projectId) {
        // Try legacy key as fallback
        saved = localStorage.getItem('deleted_acts');
        if (saved) {
          // Migrate to new key format
          localStorage.setItem(DELETED_ACTS_KEY, saved);
          logger.debug('Migrated deleted acts from legacy key to project-scoped key');
        }
      }
      return saved ? JSON.parse(saved) : null;
    } catch {
      return null;
    }
  });

  const [hiddenActs, setHiddenActs] = useState<Set<string>>(new Set());

  // Listen for changes to scene content from the write module
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent | CustomEvent) => {
      // Handle both StorageEvent and CustomEvent
      const isStorageEvent = e instanceof StorageEvent;
      const key = isStorageEvent ? e.key : (e as any).detail?.key;
      const newValue = isStorageEvent ? e.newValue : (e as any).detail?.newValue;
      
      if (!key) return;
      
      // Skip events for other projects
      if (projectId && key.includes('project_') && !key.includes(`project_${projectId}`)) {
        return;
      }
      
      // Handle write_scene_* changes
      if (key.startsWith('write_scene_') || (projectId && key.startsWith(`project_${projectId}_scene_`))) {
        try {
          // Extract the scene ID, handling both project-scoped and legacy keys
          let sceneId;
          if (projectId && key.startsWith(`project_${projectId}_scene_`)) {
            sceneId = key.replace(`project_${projectId}_scene_`, '');
          } else if (!key.includes('project_')) {
            sceneId = key.replace('write_scene_', '');
          } else {
            // This is for a different project, ignore
            return;
          }
          
          const parts = sceneId.split('-');
          if (parts.length >= 3) {
            const episodeId = parseInt(parts[0]);
            const actId = parts[1];
            const sceneIdNum = parseInt(parts[2]);
            
            const newData = newValue ? JSON.parse(newValue) : null;
            if (newData && (newData.content !== undefined || newData.title !== undefined)) {
              // Update the scene content in episodeData
              setEpisodeData(prev => {
                const episode = prev[episodeId];
                if (!episode) return prev;
                
                const updatedEpisode = {
                  ...episode,
                  acts: episode.acts.map(act => 
                    act.id === actId 
                      ? {
                          ...act,
                          scenes: act.scenes.map(scene => 
                            scene.id === sceneIdNum 
                              ? { 
                                  ...scene, 
                                  content: newData.content !== undefined ? newData.content : scene.content, 
                                  title: newData.title !== undefined ? newData.title : scene.title 
                                }
                              : scene
                          )
                        }
                      : act
                  )
                };
                
                const result = { ...prev, [episodeId]: updatedEpisode };
                localStorage.setItem(STORAGE_KEY, JSON.stringify(result));
                return result;
              });
            }
          }
        } catch (error) {
          logger.error('Failed to process storage change:', error);
        }
      }
      
      // Also handle direct episode_data changes
      if (key === STORAGE_KEY && newValue) {
        try {
          const newData = JSON.parse(newValue);
          setEpisodeData(newData);
        } catch (error) {
          logger.error('Failed to process episode data change:', error);
        }
      }
    };
    
    // Listen for both storage events and custom events
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('sceneContentChanged', handleStorageChange as EventListener);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('sceneContentChanged', handleStorageChange as EventListener);
    };
  }, [STORAGE_KEY, projectId, storageKeyPrefix]);

  const saveEpisodeData = useCallback((data: Record<number, EpisodeData>) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
      console.log('Saved episode data:', { 
        episodeCount: Object.keys(data).length,
        storageKey: STORAGE_KEY
      });
      
      // Dispatch a custom event to notify other components
      window.dispatchEvent(new CustomEvent('sceneContentChanged', {
        detail: {
          key: STORAGE_KEY,
          newValue: JSON.stringify(data)
        }
      }));
    } catch (error) {
      logger.error('Failed to save episode data:', error);
    }
  }, [STORAGE_KEY]);

  const saveDeletedActs = useCallback((acts: Array<{ id: string; title: string; color: string; scenes: any[] }>, episodeDataSnapshot: Record<number, EpisodeData>) => {
    try {
      const data = { 
        acts, 
        episodeData: episodeDataSnapshot,
        timestamp: Date.now() 
      };
      localStorage.setItem(DELETED_ACTS_KEY, JSON.stringify(data));
      setDeletedActs(data);
    } catch (error) {
      logger.error('Failed to save deleted acts:', error);
    }
  }, [DELETED_ACTS_KEY]);

  const updateEpisodeData = useCallback((
    episodeId: number,
    updater: (data: EpisodeData) => EpisodeData
  ) => {
    setEpisodeData(prev => {
      const next = {
        ...prev,
        [episodeId]: updater(prev[episodeId])
      };
      saveEpisodeData(next);
      return next;
    });
  }, [saveEpisodeData]);

  const toggleEpisode = useCallback((episodeId: number) => {
    setOpenEpisodes(prev => {
      const next = new Set(prev);
      console.log('Toggle episode:', { episodeId, wasOpen: next.has(episodeId) });
      
      // For TV projects, ensure at least one episode remains open
      if (next.has(episodeId) && next.size <= 1 && 
          (currentProject?.type === 'tv-30' || currentProject?.type === 'tv-60')) {
        return next; // Don't close the last open episode
      }
      
      if (next.has(episodeId)) {
        next.delete(episodeId);
      } else {
        console.log('Opening episode:', episodeId);
        next.add(episodeId);
        if (!episodeData[episodeId]) {
          console.log('Episode data not found, creating new episode:', episodeId);
          setEpisodeData(prev => {
            // Get the project type to determine which template to use
            const projectType = currentProject?.type || 'tv-30';
            // Check if we should use empty templates
            const useEmptyTemplates = localStorage.getItem('use_empty_templates') === 'true';
            
            console.log('Creating episode with template for project type:', projectType);
            
            // Special handling for novel type
            if (projectType === 'novel') {
              // For novels, each episode is a chapter with scenes
              // Use blank template option if specified in localStorage
              const useBlankTemplates = localStorage.getItem('use_blank_templates') === 'true';
              const chapterTemplate = useBlankTemplates || useEmptyTemplates
                ? createBlankTemplate([novelChapterTemplate(episodeId)])
                : [novelChapterTemplate(episodeId)];
                
              const next = {
                ...prev,
                [episodeId]: { 
                  acts: chapterTemplate.map(chapter => ({ 
                    ...chapter,
                    storylineId: null
                  }))
                }
              };
              saveEpisodeData(next);
              return next;
            }
            
            // Handle TV and Film templates
            let templateStructure;
            let templateSceneCounts;
            const useBlankTemplates = localStorage.getItem('use_blank_templates') === 'true';
            
            if (projectType === 'tv-30') {
              templateStructure = tv30Template;
              templateSceneCounts = sceneCounts.tv30;
            } else if (projectType === 'tv-60') {
              templateStructure = tv60Template;
              templateSceneCounts = sceneCounts.tv60;
            } else if (projectType === 'film') {
              templateStructure = filmTemplate;
              templateSceneCounts = sceneCounts.film;
            } else {
              // Default fallback
              templateStructure = tv30Template;
              templateSceneCounts = sceneCounts.tv30;
            }
            
            // If using blank templates, don't create any scenes
            const processedTemplate = useBlankTemplates || useEmptyTemplates
              ? templateStructure.map(act => ({ 
                  ...act, 
                  scenes: [] 
                }))
              : templateStructure.map(act => ({ 
                  ...act, 
                  scenes: Array.from({ length: templateSceneCounts[act.id] || 2 }, (_, i) => ({
                    id: Date.now() + i,
                    title: `Scene ${i + 1}`,
                    content: '',
                    storylineId: null
                  }))
                }));
            
            const next = {
              ...prev,
              [episodeId]: { acts: processedTemplate }
            };
            saveEpisodeData(next);
            console.log('Created new episode data:', { 
              episodeId, 
              actCount: processedTemplate.length,
              firstActSceneCount: processedTemplate[0]?.scenes?.length || 0
            });
            return next;
          });
        }
      }
      return next;
    });
  }, [episodeData, saveEpisodeData, currentProject]);

  const deleteAct = useCallback((actId: string) => {
    const episodeDataSnapshot = JSON.parse(JSON.stringify(episodeData));
    
    const actsToDelete = Object.values(episodeData)
      .flatMap(ep => ep.acts)
      .filter(act => act.id === actId)
      .map(act => ({ 
        id: act.id, 
        title: act.title, 
        color: act.color,
        scenes: act.scenes
      }));

    if (actsToDelete.length > 0) {
      saveDeletedActs(actsToDelete, episodeDataSnapshot);

      setEpisodeData(prev => {
        const next = Object.entries(prev).reduce((acc, [epId, epData]) => ({
          ...acc,
          [epId]: {
            ...epData,
            acts: epData.acts.filter(act => act.id !== actId)
          }
        }), {});
        saveEpisodeData(next);
        return next;
      });
    }
  }, [episodeData, saveDeletedActs, saveEpisodeData]);

  const undoDeleteAct = useCallback(() => {
    if (!deletedActs) return;
    setEpisodeData(deletedActs.episodeData);
    saveEpisodeData(deletedActs.episodeData);
    localStorage.removeItem(DELETED_ACTS_KEY);
    setDeletedActs(null);
  }, [deletedActs, saveEpisodeData]);

  const toggleActVisibility = useCallback((actId: string) => {
    setHiddenActs(prev => {
      const next = new Set(prev);
      if (next.has(actId)) {
        next.delete(actId);
      } else {
        next.add(actId);
      }
      return next;
    });
  }, []);

  const updateSceneStoryline = useCallback((
    episodeId: number,
    actId: string,
    sceneId: number,
    storylineId: string | null
  ) => {
    logger.debug('Updating scene storyline:', { episodeId, actId, sceneId, storylineId });
    
    updateEpisodeData(episodeId, data => ({
      ...data,
      acts: data.acts.map(act => 
        act.id === actId ? {
          ...act,
          scenes: act.scenes.map(scene =>
            scene.id === sceneId ? {
              ...scene,
              storylineId
            } : scene
          )
        } : act
      )
    }));
  }, [updateEpisodeData]);

  const getSceneStoryline = useCallback((
    episodeId: number,
    actId: string,
    sceneId: number
  ): string | null => {
    const episode = episodeData[episodeId];
    if (!episode) return null;

    const act = episode.acts.find(a => a.id === actId);
    if (!act) return null;

    const scene = act.scenes.find(s => s.id === sceneId);
    return scene?.storylineId || null;
  }, [episodeData]);

  const getActColor = useCallback((actId: string): string | null => {
    for (const episode of Object.values(episodeData)) {
      const act = episode.acts.find(a => a.id === actId);
      if (act) {
        return act.color;
      }
    }
    return null;
  }, [episodeData]);

  // Convert episode track episode IDs to numbers
  const episodes = trackEpisodes.map(ep => parseInt(ep.replace('EP_', '')));

  // Initialize episodes for a specific project type
  const initializeEpisodesForProjectType = useCallback(() => {
    const projectType = currentProject?.type || 'tv-30';
    
    // Always create 6 episodes for TV projects
    const episodeCount = projectType === 'tv-30' || projectType === 'tv-60' 
      ? 6 // Fixed at 6 episodes for TV projects
      : projectType === 'film' 
        ? 1 // 1 episode for film
        : projectType === 'novel'
          ? 12 // 12 chapters for novel
          : 6; // Default to 6
    
    // Check if episodes already exist
    const existingCount = episodes.length;
    if (existingCount >= episodeCount) return;
    
    // Add missing episodes
    for (let i = existingCount + 1; i <= episodeCount; i++) {
      const episodeId = `EP_${String(i).padStart(2, '0')}`;
      useEpisodeTrackStore.getState().addEpisode(episodeId);
      
      // For TV projects, open all episodes by default
      if ((projectType === 'tv-30' || projectType === 'tv-60')) {
        toggleEpisode(i);
      } else if (projectType === 'film' && i === 1) {
        // For film type, also toggle the first episode to show it
        toggleEpisode(i);
      }
    }
  }, [currentProject?.type, episodes.length, toggleEpisode]);
  
  // Call initialization on mount if we have a project and episodes are empty
  useEffect(() => {
    if (currentProject?.id && episodes.length === 0) {
      logger.debug('Initializing episodes for project', currentProject.id, currentProject.type);
      initializeEpisodesForProjectType();
    }
  }, [currentProject?.id, episodes.length, initializeEpisodesForProjectType]);

  const addEpisode = useCallback(() => {
    const newEpisodeNumber = episodes.length + 1;
    const maxEpisodes = currentProject?.type ? episodeCounts[currentProject.type] : 10;
    
    // Don't add more episodes than the template specifies
    if (newEpisodeNumber <= maxEpisodes) {
      const newEpisodeId = `EP_${String(newEpisodeNumber).padStart(2, '0')}`;
      useEpisodeTrackStore.getState().addEpisode(newEpisodeId);
      toggleEpisode(newEpisodeNumber);
    } else {
      logger.warn(`Cannot add more than ${maxEpisodes} episodes for this project type`);
    }
  }, [episodes.length, toggleEpisode, currentProject?.type]);

  return {
    episodes,
    openEpisodes,
    episodeData,
    deletedActs,
    hiddenActs,
    toggleEpisode,
    addEpisode,
    updateEpisodeData,
    deleteAct,
    undoDeleteAct,
    toggleActVisibility,
    updateSceneStoryline,
    getSceneStoryline,
    getActColor
  };
}