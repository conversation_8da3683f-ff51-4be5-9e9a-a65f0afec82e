// Theme configuration
export const ACT_COLORS = {
  coldopen: 'bg-blue-500',
  act1: 'bg-purple-500',
  act2a: 'bg-green-500',
  act2b: 'bg-emerald-500',
  act3: 'bg-yellow-500',
  act4: 'bg-orange-500',
  cliffhanger: 'bg-red-500'
} as const;

// UI color variants
export const UI_COLORS = {
  primary: {
    default: 'bg-black text-white',
    hover: 'hover:bg-gray-800',
    active: 'active:bg-gray-700',
  },
  ghost: {
    default: 'bg-transparent',
    hover: 'hover:bg-gray-100',
  }
} as const;

// Theme types
export type ThemeMode = 'light' | 'dark';
export type FontSize = 'small' | 'normal' | 'large';
export type ZoomScale = '100';

export interface ThemeSettings {
  mode: ThemeMode;
  fontSize: FontSize;
  zoomScale: ZoomScale;
  highContrast: boolean;
}

// Default theme settings
export const DEFAULT_THEME_SETTINGS: ThemeSettings = {
  mode: 'light',
  fontSize: 'small',
  zoomScale: '100',
  highContrast: false
};