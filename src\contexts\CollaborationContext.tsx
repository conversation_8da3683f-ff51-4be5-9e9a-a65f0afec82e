import React, { createContext, useContext, useState } from 'react';

interface CollaborationContextType {
  isCollaborating: boolean;
  collaborators: string[];
  startCollaboration: () => void;
  endCollaboration: () => void;
  addCollaborator: (collaborator: string) => void;
  removeCollaborator: (collaborator: string) => void;
}

const CollaborationContext = createContext<CollaborationContextType | undefined>(undefined);

export function CollaborationProvider({ children }: { children: React.ReactNode }) {
  const [isCollaborating, setIsCollaborating] = useState(false);
  const [collaborators, setCollaborators] = useState<string[]>([]);

  const startCollaboration = () => setIsCollaborating(true);
  const endCollaboration = () => setIsCollaborating(false);
  
  const addCollaborator = (collaborator: string) => {
    setCollaborators(prev => [...prev, collaborator]);
  };
  
  const removeCollaborator = (collaborator: string) => {
    setCollaborators(prev => prev.filter(c => c !== collaborator));
  };

  return (
    <CollaborationContext.Provider value={{
      isCollaborating,
      collaborators,
      startCollaboration,
      endCollaboration,
      addCollaborator,
      removeCollaborator
    }}>
      {children}
    </CollaborationContext.Provider>
  );
}

export const useCollaboration = () => {
  const context = useContext(CollaborationContext);
  if (context === undefined) {
    throw new Error('useCollaboration must be used within a CollaborationProvider');
  }
  return context;
};
