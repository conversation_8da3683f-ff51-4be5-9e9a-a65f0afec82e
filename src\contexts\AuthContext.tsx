import React, { createContext, useContext, useState } from 'react';
import { supabase } from '../lib/supabase/client';
import type { AuthContextType, User } from '../types/auth';
import { logger } from '../utils/logger';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Mock user for testing
const MOCK_USER: User = {
  id: '1',
  email: '<EMAIL>',
  createdAt: new Date(),
  lastLogin: new Date()
};

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Check for existing session on mount
  React.useEffect(() => {
    const checkSession = async () => {
      setIsLoading(true);
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user) {
          setUser({
            id: session.user.id,
            email: session.user.email || 'unknown',
            createdAt: new Date(session.user.created_at),
            lastLogin: new Date()
          });
          logger.debug('User authenticated from session:', session.user);
        } else {
          // Check for mock auth in localStorage
          const mockAuth = localStorage.getItem('supabase.auth.token');
          if (mockAuth) {
            try {
              const parsed = JSON.parse(mockAuth);
              if (parsed.currentSession?.user) {
                const mockUser = parsed.currentSession.user;
                setUser({
                  id: mockUser.id,
                  email: mockUser.email || '<EMAIL>',
                  createdAt: new Date(mockUser.created_at || Date.now()),
                  lastLogin: new Date()
                });
                logger.debug('Using mock authentication:', mockUser);
              }
            } catch (parseError) {
              logger.error('Failed to parse mock auth:', parseError);
            }
          }
        }
      } catch (err) {
        logger.error('Session check error:', err);
      } finally {
        setIsLoading(false);
      }
    };
    
    checkSession();
    
    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          setUser({
            id: session.user.id,
            email: session.user.email || 'unknown',
            createdAt: new Date(session.user.created_at),
            lastLogin: new Date()
          });
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
        }
      }
    );
    
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signUp = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      setError(null);
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });
      
      if (error) throw error;
      
      if (data.user) {
        logger.debug('User signed up:', { email });
      }
    } catch (err) {
      logger.error('Sign up error:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      setError(null);
      try {
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });
        
        if (error) throw error;
        
        if (data.user) {
          // Store user data
          setUser({
            id: data.user.id,
            email: data.user.email || 'unknown',
            createdAt: new Date(data.user.created_at),
            lastLogin: new Date()
          });
          logger.debug('User signed in:', { email });
        }
      } catch (authError) {
        logger.warn('Auth failed, using mock user instead:', authError);
        
        // For testing/development purposes, create a mock user
        setUser(MOCK_USER);
        logger.debug('Mock user signed in');
        
        // Store mock auth in localStorage
        localStorage.setItem('supabase.auth.token', JSON.stringify({
          currentSession: {
            access_token: 'mock-token',
            user: {
              id: MOCK_USER.id,
              email: MOCK_USER.email,
              created_at: MOCK_USER.createdAt.toISOString()
            }
          }
        }));
      }
    } catch (err) {
      logger.error('Sign in error:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setIsLoading(true);
      // Check if we're using mock auth
      const mockAuth = localStorage.getItem('supabase.auth.token');
      if (mockAuth && JSON.parse(mockAuth).currentSession?.user?.id === MOCK_USER.id) {
        localStorage.removeItem('supabase.auth.token');
        setUser(null);
        logger.debug('Mock user signed out');
      } else {
        // Real auth signout
        const { error } = await supabase.auth.signOut();
        
        if (error) throw error;
        
        setUser(null);
        logger.debug('User signed out');
      }
    } catch (err) {
      logger.error('Sign out error:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    try {
      setIsLoading(true);
      setError(null);
      const { error } = await supabase.auth.resetPasswordForEmail(email);
      
      if (error) throw error;
      
      logger.debug('Password reset email sent:', { email });
    } catch (err) {
      logger.error('Password reset error:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<User>) => {
    try {
      setIsLoading(true);
      if (!user) throw new Error('No user logged in');
      
      // Update auth metadata if needed
      if (updates.email) {
        const { error } = await supabase.auth.updateUser({
          email: updates.email
        });
        
        if (error) throw error;
      }
      
      // Update profile in users table
      const { data, error } = await supabase
        .from('users')
        .update({
          display_name: updates.displayName,
          avatar_url: updates.avatarUrl
        })
        .eq('id', user.id)
        .select()
        .single();
        
      if (error) throw error;
      
      if (data) {
        setUser({
          ...user,
          ...updates
        });
      }
      
      logger.debug('Profile updated:', updates);
    } catch (err) {
      logger.error('Profile update error:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      isLoading,
      error,
      signUp,
      signIn,
      signOut,
      resetPassword,
      updateProfile
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}