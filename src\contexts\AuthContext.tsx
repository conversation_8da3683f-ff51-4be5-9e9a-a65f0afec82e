import React, { createContext, useContext, useState, useEffect } from 'react';
import type { AuthContextType, User } from '../types/auth';
import { logger } from '../utils/logger';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Local storage key for user data
const LOCAL_USER_KEY = 'story_unstuck_user';

// Default user for local development
const DEFAULT_USER: User = {
  id: 'local-user-1',
  email: '<EMAIL>',
  displayName: 'Local Developer',
  createdAt: new Date(),
  lastLogin: new Date()
};

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Load user from localStorage on mount
  useEffect(() => {
    const loadUser = () => {
      setIsLoading(true);
      try {
        const savedUser = localStorage.getItem(LOCAL_USER_KEY);
        if (savedUser) {
          const parsedUser = JSON.parse(savedUser);
          setUser({
            ...parsedUser,
            createdAt: new Date(parsedUser.createdAt),
            lastLogin: new Date(parsedUser.lastLogin)
          });
          logger.debug('User loaded from localStorage:', parsedUser.email);
        } else {
          // Auto-login with default user for local development
          const newUser = { ...DEFAULT_USER, lastLogin: new Date() };
          setUser(newUser);
          localStorage.setItem(LOCAL_USER_KEY, JSON.stringify(newUser));
          logger.debug('Auto-logged in with default user for local development');
        }
      } catch (err) {
        logger.error('Failed to load user from localStorage:', err);
        // Fallback to default user
        const newUser = { ...DEFAULT_USER, lastLogin: new Date() };
        setUser(newUser);
        localStorage.setItem(LOCAL_USER_KEY, JSON.stringify(newUser));
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, []);

  const signUp = async (email: string, _password: string) => {
    try {
      setIsLoading(true);
      setError(null);

      // Create new user for local development
      const newUser: User = {
        id: `user-${Date.now()}`,
        email,
        displayName: email.split('@')[0],
        createdAt: new Date(),
        lastLogin: new Date()
      };

      setUser(newUser);
      localStorage.setItem(LOCAL_USER_KEY, JSON.stringify(newUser));
      logger.debug('User signed up locally:', { email });
    } catch (err) {
      logger.error('Sign up error:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const signIn = async (email: string, _password: string) => {
    try {
      setIsLoading(true);
      setError(null);

      // For local development, accept any email/password combination
      const loginUser: User = {
        id: `user-${email.replace(/[^a-zA-Z0-9]/g, '-')}`,
        email,
        displayName: email.split('@')[0],
        createdAt: new Date(),
        lastLogin: new Date()
      };

      setUser(loginUser);
      localStorage.setItem(LOCAL_USER_KEY, JSON.stringify(loginUser));
      logger.debug('User signed in locally:', { email });
    } catch (err) {
      logger.error('Sign in error:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setIsLoading(true);

      // Clear user data from localStorage
      localStorage.removeItem(LOCAL_USER_KEY);
      setUser(null);
      logger.debug('User signed out locally');
    } catch (err) {
      logger.error('Sign out error:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    try {
      setIsLoading(true);
      setError(null);

      // For local development, just log the reset request
      logger.debug('Password reset requested for local development:', { email });
      // In a real app, this would send an email
    } catch (err) {
      logger.error('Password reset error:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<User>) => {
    try {
      setIsLoading(true);
      if (!user) throw new Error('No user logged in');

      // Update user data locally
      const updatedUser = {
        ...user,
        ...updates,
        lastLogin: new Date()
      };

      setUser(updatedUser);
      localStorage.setItem(LOCAL_USER_KEY, JSON.stringify(updatedUser));
      logger.debug('Profile updated locally:', updates);
    } catch (err) {
      logger.error('Profile update error:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      isLoading,
      error,
      signUp,
      signIn,
      signOut,
      resetPassword,
      updateProfile
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}