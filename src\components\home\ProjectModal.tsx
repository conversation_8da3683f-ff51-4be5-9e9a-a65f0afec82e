import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from '../ui/dialog';
import { Button } from '../ui/button';
import { cn } from '../../lib/utils';
import type { Project } from '../../types/project';

interface ProjectModalProps {
  type: Project['type'] | null;
  isOpen: boolean;
  onClose: () => void;
  onCreateProject: (name: string, type: Project['type']) => void;
}

export const ProjectModal: React.FC<ProjectModalProps> = ({
  type,
  isOpen,
  onClose,
  onCreateProject,
}) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name.trim()) {
      setError('Please enter a project name');
      return;
    }

    if (!type) {
      setError('Invalid project type');
      return;
    }

    // Pass the description to the createProject function
    const projectData = {
      name: name.trim(),
      type,
      description: description.trim() || undefined
    };
    
    onCreateProject(projectData.name, projectData.type);
    setName('');
    setDescription('');
    setError('');
  };

  const typeLabels = {
    'tv-30': '30-Minute Show',
    'tv-60': '60-Minute Show',
    'film': 'Feature Film',
    'novel': 'Novel'
  };

  if (!type) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New {typeLabels[type]}</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="mt-4">
          <div className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Project Name
              </label>
              <input
                id="name"
                type="text"
                value={name}
                onChange={(e) => {
                  setName(e.target.value);
                  setError('');
                }}
                className={cn(
                  "mt-1 block w-full rounded-md px-3 py-2",
                  "border-2 border-black focus:outline-none focus:ring-2 focus:ring-black",
                  error && "border-red-500"
                )}
                placeholder="Enter project name..."
                autoFocus
              />
              {error && (
                <p className="mt-1 text-sm text-red-500">{error}</p>
              )}
            </div>
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Project Description (optional)
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="mt-1 block w-full rounded-md px-3 py-2 border-2 border-black focus:outline-none focus:ring-2 focus:ring-black"
                placeholder="Brief description of your project..."
                rows={3}
              />
            </div>
          </div>

          <DialogFooter className="mt-6">
            <Button
              type="button"
              variant="ghost"
              onClick={onClose}
              className="border-2 border-black"
            >
              Cancel
            </Button>
            <Button type="submit" className="border-2 border-black">
              Create Project
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};