import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>d2, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, FileText, Eye, EyeOff } from 'lucide-react';
import { <PERSON><PERSON> } from '../../../ui/button';
import { cn } from '../../../../lib/utils';
import { FormattingToolbar } from './FormattingToolbar';
import { formatFountainText, highlightFountainSyntax, fountainToHtml, validateFountainScenes } from '../../../../utils/fountainUtils';

interface ScriptEditorProps {
  content: string;
  onUpdate: (content: string) => void;
}

export const ScriptEditor: React.FC<ScriptEditorProps> = ({
  content,
  onUpdate
}) => {
  const [localContent, setLocalContent] = useState(content);
  const [isFocused, setIsFocused] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isFormatting, setIsFormatting] = useState(false);
  const [showPreview, setShowPreview] = useState(true);
  const [editMode, setEditMode] = useState<'syntax' | 'plain'>('syntax');
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const previewRef = useRef<HTMLDivElement>(null);
  
  // Update local content when prop changes
  useEffect(() => {
    setLocalContent(content);
  }, [content]);
  
  // Debounced update
  useEffect(() => {
    const timer = setTimeout(() => {
      if (localContent !== content) {
        onUpdate(localContent);
      }
    }, 500);
    
    return () => clearTimeout(timer);
  }, [localContent, content, onUpdate]);
  
  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;
    
    textarea.style.height = 'auto';
    textarea.style.height = `${textarea.scrollHeight}px`;
  }, [localContent]);
  
  // Ensure scrolling is synced between textarea and highlighted preview
  useEffect(() => {
    if (!textareaRef.current || !previewRef.current || isFocused) return;
    
    const handleScroll = () => {
      if (previewRef.current && textareaRef.current) {
        previewRef.current.scrollTop = textareaRef.current.scrollTop;
      }
    };
    
    textareaRef.current.addEventListener('scroll', handleScroll);
    return () => {
      textareaRef.current?.removeEventListener('scroll', handleScroll);
    };
  }, [isFocused]);
  
  // Apply formatting to the script
  const formatScript = () => {
    // Save current selection and scroll position
    const textarea = textareaRef.current;
    if (!textarea) return;
    
    const selectionStart = textarea.selectionStart;
    const selectionEnd = textarea.selectionEnd;
    const scrollTop = textarea.scrollTop;
    
    setIsFormatting(true);
    
    // Check if the content has valid scene headings
    let formattedContent;
    if (!validateFountainScenes(localContent)) {
      // Add a scene heading if none exists
      formattedContent = formatFountainText(`INT. SCENE - DAY\n\n${localContent}`);
    } else {
      // Format existing content
      formattedContent = formatFountainText(localContent);
    }
    
    setLocalContent(formattedContent);
    
    // After formatting, restore cursor position and scroll in a smooth way
    setTimeout(() => {
      if (textarea) {
        // Try to maintain cursor position proportionally
        const positionRatio = selectionStart / (localContent.length || 1);
        const newPosition = Math.round(positionRatio * formattedContent.length);
        
        textarea.selectionStart = newPosition;
        textarea.selectionEnd = newPosition;
        textarea.scrollTop = scrollTop;
      }
      setIsFormatting(false);
    }, 100);
  };
  
  // Insert template text at cursor position
  const insertTemplate = (template: string) => {
    const cursorPos = textareaRef.current?.selectionStart || 0;
    const currentContent = textareaRef.current?.value || '';
    
    const newContent = 
      currentContent.substring(0, cursorPos) + 
      template + 
      currentContent.substring(cursorPos);
    
    setLocalContent(newContent);
    
    // Focus and set cursor position
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
        const newCursorPos = cursorPos + template.length;
        textareaRef.current.selectionStart = newCursorPos;
        textareaRef.current.selectionEnd = newCursorPos;
      }
    }, 0);
  };

  // Common text style classes for both textarea and highlighted overlay
  const textStyles = "font-mono text-sm leading-relaxed antialiased";

  // Custom renderer for highlighted text to ensure it's crisp
  const renderHighlightedText = (text: string) => {
    // Replace newlines with <br> tags for proper rendering
    const htmlContent = highlightFountainSyntax(text).replace(/\n/g, '<br>');
    
    // Add specific styling to ensure text clarity
    return `
      <div style="font-family: monospace; font-size: 0.875rem; line-height: 1.5; letter-spacing: normal;">
        ${htmlContent}
      </div>
    `;
  };

  return (
    <div className="flex-1 flex flex-col">
      {/* Toolbar */}
      <div className="flex flex-col w-full">
        <div className="sticky top-0 z-10 p-2 bg-gray-50 flex justify-between items-center" 
             style={{ position: 'relative', zIndex: 35 }}>
          <span className="font-mono text-sm font-bold">Fountain Format</span>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowPreview(!showPreview)}
              className={cn(
                "h-8 p-1 rounded-full flex items-center",
                showPreview ? "bg-blue-100 text-blue-600" : "bg-gray-100 text-gray-600"
              )}
              title={showPreview ? "Switch to Edit Mode" : "Switch to Preview Mode"}
            >
              {showPreview ? (
                <>
                  <EyeOff className="h-4 w-4 mr-1" />
                  <span className="text-xs">Edit</span>
                </>
              ) : (
                <>
                  <Eye className="h-4 w-4 mr-1" />
                  <span className="text-xs">Preview</span>
                </>
              )}
            </Button>
            
            {/* Auto-save indicator - separate from buttons */}
            <div className="text-xs text-gray-500 ml-2 border border-gray-200 px-2 py-1 rounded-full">
              Auto-saving
            </div>
          </div>
        </div>
        
        <FormattingToolbar 
          content={localContent}
          onFormat={setLocalContent}
          onInsertTemplate={insertTemplate}
        />
      </div>
      
      {/* Editor/Preview */}
      <div className="flex-1 flex w-full">
        {showPreview ? (
          <div className="flex flex-col w-full h-full">
            <div className="sticky top-0 z-10 bg-slate-700 text-white px-4 py-2 flex justify-between items-center">
              <span className="font-mono text-sm">preview view</span>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={formatScript}
                  className="text-white hover:bg-slate-600 text-xs px-2 py-1 flex items-center"
                  disabled={isFormatting}
                >
                  <Wand2 className="h-3 w-3 mr-1" />
                  Format
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowPreview(false)}
                  className="text-white hover:bg-slate-600 text-xs px-2 py-1 flex items-center"
                >
                  <EyeOff className="h-3 w-3 mr-1" />
                  Edit Mode
                </Button>
              </div>
            </div>
            <div className="flex-1 overflow-y-auto p-4 font-mono text-sm leading-relaxed w-full">
              <div 
                className="screenplay-preview"
                dangerouslySetInnerHTML={{ __html: fountainToHtml(localContent) }}
              />
            </div>
          </div>
        ) : (
          <div className="flex-1 flex relative w-full h-full">
            <div className="absolute top-0 left-0 right-0 z-10 bg-amber-100 text-amber-800 px-4 py-1 text-xs flex items-center justify-between">
              <div className="flex items-center">
                <span>Edit Mode - Click or type to edit</span>
                <div className="ml-4 flex items-center">
                  <button
                    onClick={() => setEditMode(editMode === 'syntax' ? 'plain' : 'syntax')}
                    className={cn(
                      "ml-2 px-2 py-0.5 rounded text-xs",
                      editMode === 'syntax' 
                        ? "bg-amber-200 text-amber-800" 
                        : "bg-gray-200 text-gray-700"
                    )}
                  >
                    {editMode === 'syntax' ? 'Syntax Highlight' : 'Plain Text'}
                  </button>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowPreview(true)}
                className="text-amber-800 hover:bg-amber-200 text-xs px-2 py-0.5 h-6 flex items-center"
              >
                <Eye className="h-3 w-3 mr-1" />
                Preview
              </Button>
            </div>
            <textarea
              ref={textareaRef}
              className={cn(
                "w-full h-full p-4 resize-none",
                "border-none focus:outline-none focus:ring-0",
                textStyles,
                "backdrop-blur-none font-mono w-full"
              )}
              style={{
                WebkitFontSmoothing: "subpixel-antialiased",
                MozOsxFontSmoothing: "auto",
                textRendering: "geometricPrecision",
                lineHeight: "1.5",
                letterSpacing: "normal",
                fontFeatureSettings: "normal",
                fontSize: "0.875rem",
              }}
              value={localContent}
              onChange={(e) => setLocalContent(e.target.value)}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              placeholder="INT. LOCATION - TIME\n\nDescription of the scene...\n\nCHARACTER\n(action)\nDialogue..."
            />
            
            {editMode === 'syntax' && !isFocused && localContent && (
              <div 
                ref={previewRef}
                className={cn(
                  "absolute inset-0 pointer-events-none p-4 overflow-hidden",
                  "whitespace-pre-wrap text-black"
                )}
                style={{
                  WebkitFontSmoothing: "subpixel-antialiased",
                  MozOsxFontSmoothing: "auto",
                  textRendering: "geometricPrecision",
                  opacity: 1,
                }}
                dangerouslySetInnerHTML={{ 
                  __html: renderHighlightedText(localContent)
                }}
              />
            )}
            
            {isRecording && (
              <div className="absolute bottom-4 right-4 bg-red-100 text-red-600 px-3 py-1 rounded-full text-xs font-mono flex items-center gap-1 animate-pulse">
                <span className="h-2 w-2 bg-red-600 rounded-full"></span>
                Recording...
              </div>
            )}
            
            {isFormatting && (
              <div className="absolute inset-0 bg-white/30 flex items-center justify-center z-10 pointer-events-none">
                <div className="px-3 py-2 bg-purple-100 text-purple-700 rounded-md text-sm font-mono">
                  Formatting...
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}