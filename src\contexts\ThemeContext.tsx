import React, { createContext, useContext, useState, useEffect } from 'react';
import { ThemeMode, ThemeSettings, DEFAULT_THEME_SETTINGS, ZoomScale } from '../lib/theme';

interface ThemeContextType {
  theme: ThemeSettings;
  setTheme: (theme: Partial<ThemeSettings>) => void;
  toggleMode: () => void;
  toggleFontSize: () => void;
  toggleHighContrast: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const STORAGE_KEY = 'story_unstuck_theme';

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setThemeState] = useState<ThemeSettings>(() => {
    try {
      const savedTheme = localStorage.getItem(STORAGE_KEY);
      return savedTheme ? JSON.parse(savedTheme) : DEFAULT_THEME_SETTINGS;
    } catch {
      return DEFAULT_THEME_SETTINGS;
    }
  });

  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(theme));
    } catch (error) {
      console.error('Failed to save theme settings:', error);
    }

    // Apply theme classes to document
    const { mode, fontSize, zoomScale, highContrast } = theme;

    // Update dark mode class
    if (mode === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    // Update font size class
    document.documentElement.classList.remove('text-small', 'text-large');
    if (fontSize === 'large') {
      document.documentElement.classList.add('text-large');
    } else if (fontSize === 'small') {
      document.documentElement.classList.add('text-small');
    }
    // normal fontSize has no class

    // Update zoom scale class - first remove all scale classes
    document.documentElement.classList.remove('scale-75', 'scale-90', 'scale-100', 'scale-110');
    // Then add the selected scale
    document.documentElement.classList.add(`scale-${zoomScale}`);

    // Update contrast class
    if (highContrast) {
      document.documentElement.classList.add('high-contrast');
    } else {
      document.documentElement.classList.remove('high-contrast');
    }
  }, [theme]);

  useEffect(() => {
    // DEV ONLY: Always set zoom to 100% on load unless user changed it
    if (import.meta.env.DEV && theme.zoomScale !== '100') {
      setThemeState(prev => ({ ...prev, zoomScale: '100' }));
    }
  }, []);

  useEffect(() => {
    // Always re-apply padding-top to .scale-container after zoom changes
    const containers = document.querySelectorAll('.scale-container');
    containers.forEach(container => {
      (container as HTMLElement).style.paddingTop = '4rem';
    });
  }, [theme.zoomScale]);

  const setTheme = (updates: Partial<ThemeSettings>) => {
    setThemeState(prev => ({ ...prev, ...updates }));
  };

  const toggleMode = () => {
    setThemeState(prev => ({
      ...prev,
      mode: prev.mode === 'light' ? 'dark' : 'light'
    }));
  };

  const toggleFontSize = () => {
    setThemeState(prev => ({
      ...prev,
      fontSize: prev.fontSize === 'small'
        ? 'normal'
        : prev.fontSize === 'normal'
          ? 'large'
          : 'small'
    }));
  };

  const toggleHighContrast = () => {
    setThemeState(prev => ({
      ...prev,
      highContrast: !prev.highContrast
    }));
  };

  return (
    <ThemeContext.Provider value={{
      theme,
      setTheme,
      toggleMode,
      toggleFontSize,
      toggleHighContrast,
    }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}