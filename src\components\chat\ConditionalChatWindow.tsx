import React from 'react';
import { useProject } from '../../contexts/ProjectContext';
import { ChatWindow } from './ChatWindow';

/**
 * ConditionalChatWindow only renders the ChatWindow when a project is selected
 * This prevents the chat from showing on the home page
 */
export const ConditionalChatWindow: React.FC = () => {
  const { currentProject } = useProject();

  // Only render the ChatWindow if a project is selected
  if (!currentProject) {
    return null;
  }

  return <ChatWindow />;
};
