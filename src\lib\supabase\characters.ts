import { supabase } from './client';
import type { Character, CharacterScene, CharacterAppearance } from '../../types/character';

export const charactersApi = {
  async getCharacters(projectId: string): Promise<Character[]> {
    const { data, error } = await supabase
      .from('characters')
      .select('*')
      .eq('project_id', projectId)
      .order('name');
      
    if (error) throw error;
    return data;
  },

  async getCharacter(id: string): Promise<Character> {
    const { data, error } = await supabase
      .from('characters')
      .select('*')
      .eq('id', id)
      .single();
      
    if (error) throw error;
    return data;
  },

  async create<PERSON>haracter(character: Omit<Character, 'id' | 'createdAt' | 'updatedAt'>): Promise<Character> {
    const { data, error } = await supabase
      .from('characters')
      .insert([character])
      .select()
      .single();
      
    if (error) throw error;
    return data;
  },

  async updateCharacter(id: string, updates: Partial<Character>): Promise<Character> {
    const { data, error } = await supabase
      .from('characters')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
      
    if (error) throw error;
    return data;
  },

  async deleteCharacter(id: string): Promise<void> {
    const { error } = await supabase
      .from('characters')
      .delete()
      .eq('id', id);
      
    if (error) throw error;
  },

  async getCharacterScenes(characterId: string): Promise<CharacterScene[]> {
    const { data, error } = await supabase
      .from('character_scenes')
      .select('*')
      .eq('character_id', characterId)
      .order('created_at');
      
    if (error) throw error;
    return data;
  },

  async updateCharacterScene(
    characterId: string, 
    sceneId: string, 
    updates: Partial<CharacterScene>
  ): Promise<CharacterScene> {
    const { data, error } = await supabase
      .from('character_scenes')
      .update(updates)
      .eq('character_id', characterId)
      .eq('scene_id', sceneId)
      .select()
      .single();
      
    if (error) throw error;
    return data;
  },

  async getCharacterAppearances(
    characterId: string, 
    projectId: string
  ): Promise<CharacterAppearance[]> {
    const { data, error } = await supabase
      .rpc('get_character_appearances', {
        character_id_param: characterId,
        project_id_param: projectId
      });
      
    if (error) throw error;
    return data;
  },

  // Subscribe to character changes
  subscribeToCharacter(characterId: string, callback: (character: Character) => void) {
    return supabase
      .channel(`character-${characterId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'characters',
          filter: `id=eq.${characterId}`
        },
        (payload) => callback(payload.new as Character)
      )
      .subscribe();
  },

  // Subscribe to character scene changes
  subscribeToCharacterScenes(characterId: string, callback: (scene: CharacterScene) => void) {
    return supabase
      .channel(`character-scenes-${characterId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'character_scenes',
          filter: `character_id=eq.${characterId}`
        },
        (payload) => callback(payload.new as CharacterScene)
      )
      .subscribe();
  }
};