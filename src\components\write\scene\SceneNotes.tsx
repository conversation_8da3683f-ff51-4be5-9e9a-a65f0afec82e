import React, { useState, useEffect } from 'react';
import { MessageCircle, ChevronLeft, ChevronRight, MessageSquare, Trash2, Check, UserPlus, X, Video } from 'lucide-react';
import { Button } from '../../ui/button';
import { cn } from '../../../lib/utils';
import type { SceneNote, NoteType } from '../../../types/write';
import { CollaboratorComponent } from './CollaboratorComponent';
import { useProject } from '../../../contexts/ProjectContext';

interface SceneNotesProps {
  notes: SceneNote[];
  onAddNote: (content: string, type: NoteType) => void;
  onDeleteNote: (id: string) => void;
  onResolveNote: (id: string, isResolved: boolean) => void;
}

// Note type configurations
const noteTypes: Record<NoteType, { label: string, color: string, icon: string }> = {
  'rewrite': { label: 'Rewrite', color: 'bg-red-100 border-red-400 text-red-800', icon: '🔴' },
  'review': { label: 'Review', color: 'bg-orange-100 border-orange-400 text-orange-800', icon: '🟠' },
  'discuss': { label: 'Discuss', color: 'bg-yellow-100 border-yellow-400 text-yellow-800', icon: '🟡' },
  'research': { label: 'Research', color: 'bg-blue-100 border-blue-400 text-blue-800', icon: '🔵' },
  'general': { label: 'Note', color: 'bg-gray-100 border-gray-400 text-gray-800', icon: '📝' }
};

export const SceneNotes: React.FC<SceneNotesProps> = ({
  notes,
  onAddNote,
  onDeleteNote,
  onResolveNote
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [selectedNoteType, setSelectedNoteType] = useState<NoteType>('general');
  const [isCollaboratorOpen, setIsCollaboratorOpen] = useState(false);
  const [email, setEmail] = useState('');
  const { currentProject, addCollaborator, removeCollaborator } = useProject();
  const [meetLink, setMeetLink] = useState('');
  
  const collaborators = currentProject?.collaborators || [];

  // Function to generate a random Google Meet link
  const generateMeetLink = () => {
    const chars = 'abcdefghijklmnopqrstuvwxyz';
    let result = '';
    for (let i = 0; i < 10; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    const link = `https://meet.google.com/${result}`;
    setMeetLink(link);
    // Open the link in a new tab
    window.open(link, '_blank');
  };

  const handleAddComment = () => {
    if (newComment.trim()) {
      onAddNote(newComment, selectedNoteType);
      setNewComment('');
    }
  };

  const handleNoteTypeChange = (type: NoteType) => {
    setSelectedNoteType(type);
  };

  const handleAddCollaborator = () => {
    if (email.trim()) {
      addCollaborator(email.trim());
      setEmail('');
    }
  };

  const handleRemoveCollaborator = (emailToRemove: string) => {
    removeCollaborator(emailToRemove);
  };

  return (
    <div className={cn(
      'border-l-4 border-black flex flex-col SceneNotes',
      'transition-all duration-300',
      isCollapsed ? 'w-12' : 'w-96'
    )}>
      {/* Header */}
      <div className="border-b-4 border-black bg-gray-50">
        <div 
          className="pt-2 pb-2 px-4 flex items-center justify-between cursor-pointer"
          onClick={() => setIsCollapsed(!isCollapsed)}
        >
          {!isCollapsed && (
            <span className="font-mono font-bold flex items-center gap-2">
              <MessageCircle className="h-4 w-4" />
              NOTES & REVISIONS
            </span>
          )}
          {isCollapsed ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
        </div>

        {/* Collaborators section in header */}
        {!isCollapsed && (
          <div className="px-4 pb-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Collaborators</span>
              <div className="flex">
                {!isCollaboratorOpen ? (
                  <>
                    <Button 
                      size="sm" 
                      variant="ghost" 
                      className="text-xs h-7 border border-gray-300"
                      onClick={() => setIsCollaboratorOpen(true)}
                    >
                      <UserPlus className="h-3 w-3 mr-1" />
                      Add
                    </Button>
                    <Button 
                      size="sm" 
                      variant="ghost" 
                      className="text-xs h-7 border border-gray-300 ml-2"
                      onClick={generateMeetLink}
                      title="Start Google Meet"
                    >
                      <Video className="h-3 w-3 mr-1" />
                      Meet
                    </Button>
                  </>
                ) : (
                  <Button 
                    size="sm" 
                    variant="ghost" 
                    className="text-xs h-7"
                    onClick={() => setIsCollaboratorOpen(false)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
              </div>
            </div>
            
            {collaborators.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {collaborators.map(collab => (
                  <div 
                    key={collab} 
                    className="inline-flex items-center px-2 py-1 bg-gray-100 rounded text-xs"
                  >
                    {collab}
                    <button 
                      onClick={() => handleRemoveCollaborator(collab)}
                      className="ml-1 text-gray-500 hover:text-red-500"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}
            
            {isCollaboratorOpen && (
              <div className="flex items-center mt-2">
                <input
                  type="email"
                  placeholder="Email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded-l"
                />
                <Button
                  size="sm"
                  onClick={handleAddCollaborator}
                  disabled={!email.trim()}
                  className="text-xs h-7 min-h-0 py-0 rounded-l-none text-white bg-black hover:bg-gray-800"
                >
                  Add
                </Button>
              </div>
            )}
            
            {collaborators.length === 0 && !isCollaboratorOpen && (
              <p className="text-xs text-gray-400 mt-1">No collaborators added</p>
            )}
          </div>
        )}
      </div>

      {/* Notes Content */}
      {!isCollapsed && (
        <div className="flex-1 flex flex-col p-4">
          {/* Note Type Selector */}
          <div className="flex mb-4 gap-1 justify-between">
            {Object.entries(noteTypes).map(([type, config]) => (
              <button
                key={type}
                onClick={() => handleNoteTypeChange(type as NoteType)}
                className={cn(
                  "px-2 py-1 text-xs font-medium border-2 rounded flex-1 whitespace-nowrap",
                  type === selectedNoteType 
                    ? `${config.color} border-current` 
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                )}
              >
                <span>{config.icon} {config.label}</span>
              </button>
            ))}
          </div>

          {/* Add Comment Input - move up here */}
          <div className="flex flex-col gap-2 mb-4">
            <textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              className={cn(
                "w-full p-2 border-2 font-mono text-sm min-h-[80px] rounded",
                noteTypes[selectedNoteType].color
              )}
              placeholder={`Add a ${selectedNoteType} note...`}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && e.ctrlKey) {
                  e.preventDefault();
                  handleAddComment();
                }
              }}
            />
            <Button 
              className={cn(
                "border-2 w-full flex items-center justify-center gap-2",
                noteTypes[selectedNoteType].color
              )}
              onClick={handleAddComment}
              disabled={!newComment.trim()}
            >
              <MessageSquare className="h-4 w-4" />
              <span>Add {noteTypes[selectedNoteType].label}</span>
            </Button>
          </div>

          {/* Notes List */}
          <div className="flex-1 space-y-4 overflow-y-auto notes-list">
            {notes.length === 0 ? (
              <div className="text-center text-gray-500 italic py-8">
                No notes yet. Add your first note below.
              </div>
            ) : (
              notes.map(note => {
                const noteConfig = noteTypes[note.type || 'general'];
                return (
                  <div key={note.id} className={cn(
                    "flex gap-2 group",
                    note.isResolved && "opacity-60"
                  )}>
                    <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center font-mono flex-shrink-0">
                      {note.user[0]}
                    </div>
                    <div className="flex-1">
                      <div className={cn(
                        "p-3 rounded-lg relative border-2",
                        noteConfig.color
                      )}>
                        <div className="font-mono text-sm font-bold mb-1 flex items-center justify-between">
                          <span>{note.user}</span>
                          <span className="text-xs">{noteConfig.icon} {noteConfig.label}</span>
                        </div>
                        <p className={cn(
                          "font-mono text-sm",
                          note.isResolved && "line-through"
                        )}>{note.content}</p>
                        <div className="absolute top-2 right-2 flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onResolveNote(note.id, !note.isResolved)}
                            className={cn(
                              "h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity",
                              note.isResolved ? "text-green-600" : "text-gray-500 hover:text-green-600"
                            )}
                            title={note.isResolved ? "Mark as unresolved" : "Mark as resolved"}
                          >
                            <Check className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onDeleteNote(note.id)}
                            className="h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">{note.time}</div>
                      {/* Add collaborator component */}
                      <CollaboratorComponent
                        noteId={note.id}
                        collaborators={note.collaborators || []}
                        sceneId={note.sceneId || ''}
                      />
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </div>
      )}
    </div>
  );
};