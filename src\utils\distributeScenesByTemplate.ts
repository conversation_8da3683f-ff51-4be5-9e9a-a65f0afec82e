// Utility to distribute scenes across acts/sections based on template
// Usage: distributeScenesByTemplate(scenes, projectType)
import { tv30Template, tv60Template, filmTemplate, novelTemplate } from '../constants/templates';

export function distributeScenesByTemplate(scenes, projectType) {
  let actIds = [];
  if (projectType === 'tv-30') {
    actIds = tv30Template.map(act => act.id);
  } else if (projectType === 'tv-60') {
    actIds = tv60Template.map(act => act.id);
  } else if (projectType === 'film') {
    actIds = filmTemplate.map(section => section.id);
  } else if (projectType === 'novel') {
    // For novel, we'll use a single chapter structure at a time
    actIds = novelTemplate(1).map(chapter => chapter.id);
  }
  
  const result = {};
  const scenesPerAct = Math.floor(scenes.length / actIds.length);
  let remainder = scenes.length % actIds.length;
  let idx = 0;
  for (const actId of actIds) {
    const count = scenesPerAct + (remainder > 0 ? 1 : 0);
    result[actId] = scenes.slice(idx, idx + count);
    idx += count;
    if (remainder > 0) remainder--;
  }
  return result;
} 