import React from 'react';
import { <PERSON>, Settings, Maximize2, Minimize2 } from 'lucide-react';
import { Button } from '../ui/button';
import { useNavigation } from '../../contexts/NavigationContext';

interface EpisodeHeaderProps {
  episodeNumber: number;
  isExpanded: boolean;
  onToggleExpand: () => void;
  onOpenSettings?: () => void;
}

export const EpisodeHeader: React.FC<EpisodeHeaderProps> = ({
  episodeNumber,
  isExpanded,
  onToggleExpand,
  onOpenSettings
}) => {
  const { setCurrentView } = useNavigation();

  const handleWriteClick = () => {
    // Navigate to write view and pass the episode ID
    setCurrentView('write');
    // Store the active episode ID in session storage for the write view to use
    sessionStorage.setItem('activeEpisodeId', episodeNumber.toString());
  };

  return (
    <div className="flex items-stretch border-b-4 border-black bordertop">
      <div className="w-12 bg-gray-100 flex items-center justify-center font-mono text-sm font-bold border-r-4 border-black">
        EP_{String(episodeNumber).padStart(2, '0')}
      </div>
      <div className="flex-1 h-6 flex items-center justify-end pr-2 gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleWriteClick}
          className="h-6 w-6 p-1 hover:bg-gray-100"
          title="Write Mode"
        >
          <Pen className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleExpand}
          className="h-6 w-6 p-1 hover:bg-gray-100"
          title={isExpanded ? "Collapse" : "Expand"}
        >
          {isExpanded ? (
            <Minimize2 className="h-4 w-4" />
          ) : (
            <Maximize2 className="h-4 w-4" />
          )}
        </Button>
        {onOpenSettings && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onOpenSettings}
            className="h-6 w-6 p-1 hover:bg-gray-100"
            title="Episode Settings"
          >
            <Settings className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
};