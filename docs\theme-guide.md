# Theme Guide

This document provides guidance on how to use and update the global theme system in the Story Unstuck application.

## Overview

The theme system is designed to provide consistent styling across the application with support for:

- Light and dark modes
- High contrast mode
- Adjustable font sizes
- Consistent color palette
- Typography system
- Border styles
- Spacing constants

## Theme Structure

### 1. Tailwind Configuration

The base theme is defined in `tailwind.config.js`, which extends Tailwind's default theme with custom colors, typography, and other design tokens.

```js
// tailwind.config.js
export default {
  theme: {
    extend: {
      colors: {
        primary: '#000000',
        accent: '#00FFF0',
        secondary: {
          pink: '#FF3AF2',
          yellow: '#FFE600',
          // ...more colors
        },
        // ...more color definitions
      },
      // ...typography, spacing, etc.
    },
  },
  darkMode: 'class', // Enable dark mode with class strategy
};
```

### 2. CSS Variables

CSS variables are defined in `src/index.css` to support runtime theme switching:

```css
:root {
  --color-primary: #000000;
  --color-accent: #00FFF0;
  /* ...more variables */
}

.dark {
  --color-background: #171717;
  --color-foreground: #ffffff;
  /* ...dark mode overrides */
}

.high-contrast {
  --color-primary: #000000;
  --color-accent: #00CCFF;
  /* ...high contrast overrides */
}
```

### 3. Theme Constants

Centralized theme constants are available in `src/lib/theme.ts`:

```typescript
// Color palettes
export const STORYLINE_COLORS = [...];
export const ACT_COLORS = {...};

// UI variants
export const UI_COLORS = {...};

// Typography styles
export const TYPOGRAPHY = {...};

// Border styles
export const BORDERS = {...};
```

### 4. Theme Context

A React context provides theme state management in `src/contexts/ThemeContext.tsx`:

```typescript
interface ThemeContextType {
  theme: ThemeSettings;
  setTheme: (theme: Partial<ThemeSettings>) => void;
  toggleMode: () => void;
  toggleFontSize: () => void;
  toggleHighContrast: () => void;
}
```

## Using the Theme System

### 1. Accessing Theme Context

```tsx
import { useTheme } from '../contexts/ThemeContext';

function MyComponent() {
  const { theme, toggleMode } = useTheme();
  
  return (
    <div>
      <p>Current theme: {theme.mode}</p>
      <button onClick={toggleMode}>Toggle Theme</button>
    </div>
  );
}
```

### 2. Using Theme Constants

```tsx
import { TYPOGRAPHY, BORDERS, UI_COLORS } from '../lib/theme';
import { cn } from '../lib/utils';

function StyledComponent() {
  return (
    <div className={cn(
      TYPOGRAPHY.heading.h2,
      BORDERS.default,
      UI_COLORS.primary.default,
      "m-4"
    )}>
      Styled Content
    </div>
  );
}
```

### 3. Using CSS Variables

```tsx
function CustomStyledComponent() {
  return (
    <div style={{ 
      backgroundColor: 'var(--color-background)',
      color: 'var(--color-foreground)',
      borderColor: 'var(--color-accent)'
    }}>
      Custom Styled Content
    </div>
  );
}
```

### 4. Using Tailwind Classes

```tsx
function TailwindComponent() {
  return (
    <div className="bg-primary text-white dark:bg-gray-800 dark:text-gray-100">
      Tailwind Styled Content
    </div>
  );
}
```

## Updating the Theme

### 1. Adding New Colors

To add new colors to the theme:

1. Add the color to `tailwind.config.js`:
```js
colors: {
  // ...existing colors
  newColor: '#FF00FF',
}
```

2. Add CSS variables in `src/index.css`:
```css
:root {
  /* ...existing variables */
  --color-new-color: #FF00FF;
}

.dark {
  /* ...existing variables */
  --color-new-color: #CC00CC;
}
```

3. Update theme constants in `src/lib/theme.ts` if needed:
```typescript
export const UI_COLORS = {
  // ...existing colors
  newColor: {
    default: 'bg-newColor text-white',
    hover: 'hover:bg-newColor/80',
  },
};
```

### 2. Adding New Typography Styles

To add new typography styles:

1. Update `tailwind.config.js` if needed:
```js
fontSize: {
  // ...existing sizes
  '5xl': '3rem',
}
```

2. Update typography constants in `src/lib/theme.ts`:
```typescript
export const TYPOGRAPHY = {
  // ...existing styles
  display: {
    large: 'text-5xl font-bold leading-tight',
  },
};
```

### 3. Adding New Theme Features

To add new theme features (e.g., font spacing):

1. Update the `ThemeSettings` interface in `src/lib/theme.ts`:
```typescript
export interface ThemeSettings {
  mode: ThemeMode;
  fontSize: 'normal' | 'large';
  highContrast: boolean;
  fontSpacing: 'normal' | 'wide'; // New feature
}
```

2. Update the default settings:
```typescript
export const DEFAULT_THEME_SETTINGS: ThemeSettings = {
  // ...existing settings
  fontSpacing: 'normal',
};
```

3. Add CSS classes in `src/index.css`:
```css
.wide-spacing {
  letter-spacing: 0.05em;
}
```

4. Update the `ThemeContext` to handle the new feature:
```typescript
const toggleFontSpacing = () => {
  setThemeState(prev => ({
    ...prev,
    fontSpacing: prev.fontSpacing === 'normal' ? 'wide' : 'normal'
  }));
};
```

## Best Practices

1. **Use Theme Constants**: Always use theme constants from `src/lib/theme.ts` instead of hardcoding values.

2. **Support Dark Mode**: Add dark mode variants to all components using the `dark:` prefix in Tailwind classes.

3. **Use the `cn()` Utility**: Combine classes with the `cn()` utility from `src/lib/utils.ts` to handle conditional classes cleanly.

4. **Responsive Design**: Ensure all components work well across different screen sizes and with different font size settings.

5. **Accessibility**: Maintain sufficient color contrast, especially in dark mode and high contrast mode.

6. **Testing**: Test components in all theme modes to ensure they look and function correctly.

## Theme Components

The application includes several theme-related components:

- `ThemeToggle`: A component for switching between light and dark modes, adjusting font size, and toggling high contrast mode.
- `Button`: A styled button component that respects the current theme.
- `Card`: A styled card component with theme-aware styling.

## Conclusion

This theme system provides a flexible and consistent approach to styling the application. By following these guidelines, you can ensure that all components maintain a cohesive look and feel while supporting various user preferences.