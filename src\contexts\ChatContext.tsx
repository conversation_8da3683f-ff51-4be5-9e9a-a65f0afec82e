import React, { createContext, useContext, useState, useEffect } from 'react';
import { useLocalStorage } from '../hooks/shared/useLocalStorage';
import { generateCompletion, generateStreamingCompletion } from '../api/aiService';
import { getPersonaPrompt } from '../utils/personaUtils';
import { logger } from '../utils/logger';

interface Message {
  id: string;
  content: string;
  type: 'user' | 'assistant';
  timestamp: number;
  isStreaming?: boolean;
}

interface SavedChat {
  id: string;
  title: string;
  messages: Message[];
  createdAt: number;
  updatedAt: number;
}

interface Position {
  x: number;
  y: number;
}

interface ChatSize {
  width: number;
  height: number;
}

interface ChatContextType {
  messages: Message[];
  addMessage: (content: string, type: 'user' | 'assistant') => void;
  isCollapsed: boolean;
  setIsCollapsed: (value: boolean) => void;
  selectedModel: string;
  setSelectedModel: (value: string) => void;
  selectedPersona: string;
  setSelectedPersona: (value: string) => void;
  position: Position;
  setPosition: (position: Position) => void;
  size: ChatSize;
  setSize: (size: ChatSize) => void;
  saveChat: (title?: string) => string;
  savedChats: SavedChat[];
  loadChat: (chatId: string) => void;
  currentChatId: string | null;
  clearChat: (resetWidgetSettings?: boolean) => void;
  createPromptFromChat: (messageIds: string[]) => { title: string, content: string };
  uploadContent: (content: string) => void;
  isLoading: boolean;
  useStreaming: boolean;
  setUseStreaming: (value: boolean) => void;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export const ChatProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: 'Hello! How can I help you today?',
      type: 'assistant',
      timestamp: Date.now(),
    },
  ]);
  // Always start with the chat widget visible
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [selectedModel, setSelectedModel] = useState('gpt-4');
  const [selectedPersona, setSelectedPersona] = useState('writer');
  const [position, setPosition] = useState<Position>({ x: window.innerWidth - 380, y: 80 });
  const [size, setSize] = useState<ChatSize>({ width: 360, height: 600 });
  const [currentChatId, setCurrentChatId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [useStreaming, setUseStreaming] = useState(true);

  // Use local storage to persist saved chats
  const [savedChats, setSavedChats] = useLocalStorage<SavedChat[]>('saved-chats', []);

  // Load saved position and size from localStorage on mount
  useEffect(() => {
    try {
      const savedSettings = localStorage.getItem('chat_widget_settings');
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        if (settings.position) setPosition(settings.position);
        if (settings.size) setSize(settings.size);
      }
    } catch (error) {
      logger.error('Failed to load chat widget settings:', error);
      // If there's an error, clear the localStorage to prevent persistent issues
      localStorage.removeItem('chat_widget_settings');
    }
  }, []);

  // Force refresh chat settings when the component mounts
  useEffect(() => {
    // Clear any existing chat widget settings to ensure it's visible
    try {
      localStorage.removeItem('chat_widget_settings');
      console.log('Chat widget settings cleared to ensure visibility');
    } catch (e) {
      logger.error('Error clearing chat widget settings:', e);
    }

    // This ensures that any code changes to default messages are reflected
    const initialMessage = messages[0];
    if (initialMessage && initialMessage.type === 'assistant') {
      // Update the message in localStorage to match the current code
      const savedChatsData = localStorage.getItem('saved-chats');
      if (savedChatsData) {
        try {
          const chats = JSON.parse(savedChatsData);
          // Only update if there are saved chats
          if (Array.isArray(chats) && chats.length > 0) {
            // Update the initial message in each chat if it exists
            const updatedChats = chats.map(chat => {
              if (chat.messages && chat.messages.length > 0 && chat.messages[0].type === 'assistant') {
                chat.messages[0].content = initialMessage.content;
              }
              return chat;
            });
            localStorage.setItem('saved-chats', JSON.stringify(updatedChats));
          }
        } catch (e) {
          logger.error('Error updating saved chats:', e);
        }
      }
    }
  }, []);

  const addMessage = async (content: string, type: 'user' | 'assistant') => {
    console.log(`Adding message: ${type}`, content.substring(0, 50) + '...');
    const newMessage = {
      id: Date.now().toString(),
      content,
      type,
      timestamp: Date.now(),
    };
    setMessages(prev => [...prev, newMessage]);

    // If it's a user message, generate an AI response
    if (type === 'user') {
      setIsLoading(true);
      console.log('Generating AI response...');

      if (useStreaming) {
        console.log('Using streaming API');
        // Create a placeholder message for streaming
        const streamingMessageId = (Date.now() + 1).toString();
        const streamingMessage: Message = {
          id: streamingMessageId,
          content: '',
          type: 'assistant',
          timestamp: Date.now() + 1000,
          isStreaming: true
        };

        // Add the empty streaming message that will be updated
        setMessages(prev => [...prev, streamingMessage]);

        // Get previous messages for context (last 20 messages to provide more conversation history)
        const previousMessages = messages
          .slice(-20)
          .map(msg => ({
            role: msg.type as 'user' | 'assistant',
            content: msg.content
          }));

        // Use the NATURAL_CONVERSATION_PROMPT from aiService
        const systemPrompt = '';

        try {
          // Use the streaming API
          generateStreamingCompletion(
            content,
            selectedModel,
            systemPrompt,
            previousMessages,
            {
              onChunk: (chunk) => {
                console.log('Received chunk:', chunk.substring(0, 20) + '...');
                // Update the streaming message with each chunk
                setMessages(prev => {
                  const updatedMessages = [...prev];
                  const streamingIndex = updatedMessages.findIndex(m => m.id === streamingMessageId);

                  if (streamingIndex !== -1) {
                    updatedMessages[streamingIndex] = {
                      ...updatedMessages[streamingIndex],
                      content: updatedMessages[streamingIndex].content + chunk
                    };
                  }

                  return updatedMessages;
                });
              },
              onComplete: (finalResponse) => {
                console.log('Streaming completed:', finalResponse.id);
                // Update the streaming flag when streaming is complete
                setMessages(prev => {
                  const updatedMessages = [...prev];
                  const streamingIndex = updatedMessages.findIndex(m => m.id === streamingMessageId);

                  if (streamingIndex !== -1) {
                    updatedMessages[streamingIndex] = {
                      ...updatedMessages[streamingIndex],
                      isStreaming: false
                    };
                  }

                  return updatedMessages;
                });

                // Streaming completed
                setIsLoading(false);
              },
              onError: (error) => {
                console.error('Streaming AI response error:', error);

                // Update the streaming message with an error message
                setMessages(prev => {
                  const updatedMessages = [...prev];
                  const streamingIndex = updatedMessages.findIndex(m => m.id === streamingMessageId);

                  if (streamingIndex !== -1) {
                    updatedMessages[streamingIndex] = {
                      ...updatedMessages[streamingIndex],
                      content: 'Sorry, I had trouble generating a response. Please try again.',
                      isStreaming: false
                    };
                  }

                  return updatedMessages;
                });

                setIsLoading(false);
              }
            }
          );
        } catch (error) {
          console.error('Failed to initiate streaming AI response:', error);

          // Update the streaming message with an error message
          setMessages(prev => {
            const updatedMessages = [...prev];
            const streamingIndex = updatedMessages.findIndex(m => m.id === streamingMessageId);

            if (streamingIndex !== -1) {
              updatedMessages[streamingIndex] = {
                ...updatedMessages[streamingIndex],
                content: 'Sorry, I had trouble generating a response. Please try again.',
                isStreaming: false
              };
            }

            return updatedMessages;
          });

          setIsLoading(false);
        }
      } else {
        console.log('Using non-streaming API');
        // Traditional non-streaming response
        try {
          // Get previous messages for context (last 20 messages to provide more conversation history)
          const previousMessages = messages
            .slice(-20)
            .map(msg => ({
              role: msg.type as 'user' | 'assistant',
              content: msg.content
            }));

          // Use the NATURAL_CONVERSATION_PROMPT from aiService
          const systemPrompt = '';

          // Generate completion from the AI service
          const completion = await generateCompletion(
            content,
            selectedModel,
            systemPrompt,
            previousMessages
          );

          console.log('Received response:', completion.id);

          const aiResponse = {
            id: (Date.now() + 1).toString(),
            content: completion.content,
            type: 'assistant' as const,
            timestamp: Date.now() + 1000,
          };

          setMessages(prev => [...prev, aiResponse]);
        } catch (error) {
          console.error('Failed to generate AI response:', error);

          // Add error message
          const errorResponse = {
            id: (Date.now() + 1).toString(),
            content: 'Sorry, I had trouble generating a response. Please try again.',
            type: 'assistant' as const,
            timestamp: Date.now() + 1000,
          };

          setMessages(prev => [...prev, errorResponse]);
        } finally {
          setIsLoading(false);
        }
      }
    }
  };

  // Save current chat conversation
  const saveChat = (title?: string) => {
    const chatId = currentChatId || Date.now().toString();
    const now = Date.now();

    const chatTitle = title ||
      `Chat ${new Date(now).toLocaleDateString()} ${new Date(now).toLocaleTimeString()}`;

    const newChat: SavedChat = {
      id: chatId,
      title: chatTitle,
      messages: [...messages],
      createdAt: currentChatId ? (savedChats.find(c => c.id === currentChatId)?.createdAt || now) : now,
      updatedAt: now,
    };

    setSavedChats(prev => {
      const filteredChats = prev.filter(chat => chat.id !== chatId);
      return [...filteredChats, newChat];
    });

    setCurrentChatId(chatId);
    return chatId;
  };

  // Load a saved chat
  const loadChat = (chatId: string) => {
    const chat = savedChats.find(c => c.id === chatId);
    if (chat) {
      setMessages(chat.messages);
      setCurrentChatId(chatId);
    }
  };

  // Clear the current chat and optionally reset widget settings
  const clearChat = (resetWidgetSettings = false) => {
    setMessages([
      {
        id: Date.now().toString(),
        content: 'Hello! How can I help you today?',
        type: 'assistant',
        timestamp: Date.now(),
      },
    ]);
    setCurrentChatId(null);

    // Optionally reset the widget settings to defaults
    if (resetWidgetSettings) {
      try {
        // Remove chat widget settings from localStorage
        localStorage.removeItem('chat_widget_settings');

        // Reset position to default
        setPosition({ x: window.innerWidth - 380, y: 80 });

        // Reset size to default
        setSize({ width: 360, height: 600 });

        // Reset collapsed state
        setIsCollapsed(false);

        console.log('Chat widget settings reset to defaults');
      } catch (error) {
        logger.error('Failed to reset chat widget settings:', error);
      }
    }
  };

  // Create a prompt from selected chat messages
  const createPromptFromChat = (messageIds: string[]) => {
    const selectedMessages = messages.filter(m => messageIds.includes(m.id));

    if (selectedMessages.length === 0) {
      return { title: '', content: '' };
    }

    const title = selectedMessages[0].content.substring(0, 30) +
      (selectedMessages[0].content.length > 30 ? '...' : '');

    const content = selectedMessages
      .map(m => `${m.type === 'user' ? 'User' : 'AI'}: ${m.content}`)
      .join('\n\n');

    return { title, content };
  };

  // Upload content to chat
  const uploadContent = (content: string) => {
    addMessage(`Uploaded content:\n\n${content}`, 'user');
  };

  return (
    <ChatContext.Provider
      value={{
        messages,
        addMessage,
        isCollapsed,
        setIsCollapsed,
        selectedModel,
        setSelectedModel,
        selectedPersona,
        setSelectedPersona,
        position,
        setPosition,
        size,
        setSize,
        saveChat,
        savedChats,
        loadChat,
        currentChatId,
        clearChat,
        createPromptFromChat,
        uploadContent,
        isLoading,
        useStreaming,
        setUseStreaming,
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};

export const useChat = () => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};