/**
 * Utility functions for working with localStorage in a project-scoped way
 */
import { logger } from './logger';

/**
 * Creates a project-scoped storage key
 * @param projectId The current project ID
 * @param key The base key
 * @returns A project-scoped key
 */
export function getProjectScopedKey(projectId: string | undefined, key: string): string {
  if (!projectId) {
    // Generate a fallback project ID to prevent data leakage between projects
    const fallbackId = 'default-project';
    return `project_${fallbackId}_${key}`;
  }
  return `project_${projectId}_${key}`;
}

/**
 * Gets an item from localStorage with project scope
 * @param projectId The current project ID
 * @param key The base key
 * @param migrateLegacy Whether to migrate legacy data
 * @returns The stored value or null
 */
export function getProjectItem<T>(
  projectId: string | undefined, 
  key: string,
  migrateLegacy = true
): T | null {
  try {
    // Try project-scoped key first
    const scopedKey = getProjectScopedKey(projectId, key);
    let value = localStorage.getItem(scopedKey);
    
    // If not found and migration is enabled, try legacy key
    if (!value && migrateLegacy && projectId && projectId !== 'default-project') {
      const legacyValue = localStorage.getItem(key);
      if (legacyValue) {
        // Migrate legacy data to project-scoped key
        localStorage.setItem(scopedKey, legacyValue);
        value = legacyValue;
        logger.debug(`Migrated data from "${key}" to "${scopedKey}"`);
      }
    }
    
    return value ? JSON.parse(value) : null;
  } catch (error) {
    logger.error(`Failed to get item "${key}" for project "${projectId}":`, error);
    return null;
  }
}

/**
 * Sets an item in localStorage with project scope
 * @param projectId The current project ID
 * @param key The base key
 * @param value The value to store
 * @returns True if successful, false otherwise
 */
export function setProjectItem<T>(
  projectId: string | undefined,
  key: string,
  value: T
): boolean {
  try {
    const scopedKey = getProjectScopedKey(projectId, key);
    localStorage.setItem(scopedKey, JSON.stringify(value));
    return true;
  } catch (error) {
    logger.error(`Failed to set item "${key}" for project "${projectId}":`, error);
    return false;
  }
}

/**
 * Removes an item from localStorage with project scope
 * @param projectId The current project ID
 * @param key The base key
 * @returns True if successful, false otherwise
 */
export function removeProjectItem(
  projectId: string | undefined,
  key: string
): boolean {
  try {
    const scopedKey = getProjectScopedKey(projectId, key);
    localStorage.removeItem(scopedKey);
    return true;
  } catch (error) {
    logger.error(`Failed to remove item "${key}" for project "${projectId}":`, error);
    return false;
  }
}

/**
 * Clears all project-specific data from localStorage
 * @param projectId The project ID to clear data for
 */
export function clearProjectData(projectId: string): void {
  try {
    const prefix = `project_${projectId}_`;
    
    // Find all keys that start with the project prefix
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(prefix)) {
        keysToRemove.push(key);
      }
    }
    
    // Remove all project-specific keys
    keysToRemove.forEach(key => localStorage.removeItem(key));
    
    logger.debug(`Cleared ${keysToRemove.length} items for project "${projectId}"`);
  } catch (error) {
    logger.error(`Failed to clear data for project "${projectId}":`, error);
  }
}

/**
 * Clears all project-specific data from localStorage
 * @param projectId The project ID to clear data for
 */

/**
 * Generates a unique identifier for projects, episodes, etc.
 * @param prefix Prefix for the ID (e.g. 'proj', 'ep', etc.)
 * @returns A unique string ID
 */
export function generateUniqueId(prefix: string = 'id'): string {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
}