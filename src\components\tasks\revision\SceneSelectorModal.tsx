import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '../../ui/dialog';
import { Button } from '../../ui/button';
import { Select } from '../../ui/select';
import { useEpisodeState } from '../../../hooks/useEpisodeState';
import { useProject } from '../../../contexts/ProjectContext';

interface SceneSelectorModalProps {
  onSelect: (sceneId: string, noteType: string, assigneeId?: string) => void;
  onCancel: () => void;
  selectedText?: string;
}

export const SceneSelectorModal: React.FC<SceneSelectorModalProps> = ({
  onSelect,
  onCancel,
  selectedText = ''
}) => {
  const { episodes, episodeData } = useEpisodeState();
  const { currentProject } = useProject();
  const collaborators = currentProject?.collaborators || [];
  const [selectedEpisode, setSelectedEpisode] = useState<number | ''>('');
  const [selectedScene, setSelectedScene] = useState<string>('');
  const [noteType, setNoteType] = useState('review');
  const [assigneeId, setAssigneeId] = useState('');

  // Reset scene selection when episode changes
  useEffect(() => {
    setSelectedScene('');
  }, [selectedEpisode]);
  
  // Get all scenes for the selected episode, grouped by acts
  const scenes = selectedEpisode !== '' && episodeData[selectedEpisode]
    ? episodeData[selectedEpisode].acts.flatMap(act => 
        act.scenes.map(scene => ({
          id: scene.id,
          title: scene.title,
          actId: act.id,
          actTitle: act.title,
          fullId: `${selectedEpisode}-${act.id}-${scene.id}`
        }))
      )
    : [];
  
  const noteTypes = [
    { value: 'rewrite', label: 'Rewrite' },
    { value: 'review', label: 'Review' },
    { value: 'discuss', label: 'Discuss' },
    { value: 'research', label: 'Research' },
    { value: 'general', label: 'General' }
  ];
  
  return (
    <Dialog open={true} onOpenChange={onCancel}>
      <DialogContent className="bg-white border-4 border-black text-black">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-black">Send to Scene</DialogTitle>
        </DialogHeader>
        
        {selectedText && (
          <div className="mt-2 p-3 bg-yellow-50 border-2 border-yellow-400 rounded">
            <div className="text-sm font-bold mb-1">Selected Text:</div>
            <div className="text-sm max-h-32 overflow-y-auto whitespace-pre-wrap">
              {selectedText.length > 300 
                ? `${selectedText.substring(0, 300)}...` 
                : selectedText}
            </div>
          </div>
        )}
        
        <div className="mt-4">
          <label className="block mb-2 font-bold">Episode</label>
          <Select
            value={selectedEpisode.toString()}
            onValueChange={(value) => setSelectedEpisode(Number(value))}
            className="w-full mb-4 p-2 border-2 border-black"
          >
            <option value="">Select an episode...</option>
            {episodes.map((episodeId) => (
              <option key={episodeId} value={episodeId}>
                Episode {episodeId}
              </option>
            ))}
          </Select>
        </div>
        
        <div className="mt-4">
          <label className="block mb-2 font-bold">Scene</label>
          <Select
            value={selectedScene}
            onValueChange={setSelectedScene}
            className="w-full mb-4 p-2 border-2 border-black"
            disabled={selectedEpisode === ''}
          >
            <option value="">Select a scene...</option>
            {scenes.map((scene) => (
              <option key={scene.fullId} value={scene.fullId}>
                {scene.actTitle} - {scene.title}
              </option>
            ))}
          </Select>
        </div>
        
        <div>
          <label className="block mb-2 font-bold">Note Type</label>
          <Select
            value={noteType}
            onValueChange={setNoteType}
            className="w-full mb-4 p-2 border-2 border-black"
          >
            {noteTypes.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </Select>
        </div>
        
        <div>
          <label className="block mb-2 font-bold">Assign To (Optional)</label>
          <Select
            value={assigneeId}
            onValueChange={setAssigneeId}
            className="w-full mb-4 p-2 border-2 border-black"
          >
            <option value="">Unassigned</option>
            {collaborators.map(collaborator => (
              <option key={collaborator} value={collaborator}>
                {collaborator}
              </option>
            ))}
          </Select>
        </div>
        
        <div className="flex justify-end gap-2 mt-6">
          <Button variant="outline" onClick={onCancel} className="border-2 border-black text-black">
            Cancel
          </Button>
          <Button
            onClick={() => onSelect(selectedScene, noteType, assigneeId)}
            disabled={!selectedScene}
            className="bg-black text-white hover:bg-gray-800"
          >
            Send {selectedText ? 'Selection' : 'Note'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}; 