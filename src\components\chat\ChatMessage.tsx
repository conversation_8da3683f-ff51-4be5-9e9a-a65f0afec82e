import React from 'react';
import { Grip, Copy, Check } from 'lucide-react';
import { But<PERSON> } from '../ui/button';
import { useDragState } from '../../hooks/shared/useDragState';
import { cn } from '../../lib/utils';
import { useEpisodeTrackStore } from '../../store/episodeTrackStore';
import type { CardState } from '../../types/card';
import type { DragMetadata } from '../../types/dragTypes';

interface ChatMessageProps {
  id: string;
  content: string;
  type: 'user' | 'assistant';
  isSelectable?: boolean;
  isSelected?: boolean;
  onSelect?: (id: string) => void;
  isStreaming?: boolean;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({
  id,
  content,
  type,
  isSelectable = false,
  isSelected = false,
  onSelect,
  isStreaming = false
}) => {
  const { handleDragStart, handleDragEnd } = useDragState();
  const addCard = useEpisodeTrackStore(state => state.addCard);

  const startDrag = (e: React.DragEvent) => {
    if (isSelectable || isStreaming) {
      e.preventDefault();
      return;
    }

    const card: CardState = {
      id,
      content,
      source: 'chat',
      currentLocation: 'chat'
    };

    const metadata: DragMetadata = {
      sourceView: 'chat',
      sourceContainer: {
        type: 'quadrant',
        id: 'chat-container'
      },
      position: {
        index: parseInt(e.currentTarget.getAttribute('data-index') || '0'),
        parentId: 'chat-container'
      }
    };

    // Also add the card to the color legend directly
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: 'storyline-card',
      cardId: id,
      content: content
    }));

    handleDragStart(e, card, metadata);
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(content);
  };

  const handleAddToColorLegend = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Add directly to color legend
    addCard({
      content,
      source: 'chat',
      color: 'white',
      sectionId: 'chat'
    });
  };

  const handleClick = () => {
    if (isSelectable && onSelect) {
      onSelect(id);
    }
  };

  const renderTypingIndicator = () => {
    if (!isStreaming) return null;

    return (
      <div className="inline-flex items-center ml-1">
        <span className="w-1 h-1 bg-gray-600 rounded-full animate-pulse mx-0.5"
              style={{ animationDelay: '0ms' }}></span>
        <span className="w-1 h-1 bg-gray-600 rounded-full animate-pulse mx-0.5"
              style={{ animationDelay: '200ms' }}></span>
        <span className="w-1 h-1 bg-gray-600 rounded-full animate-pulse mx-0.5"
              style={{ animationDelay: '400ms' }}></span>
      </div>
    );
  };

  return (
    <div
      draggable={!isSelectable && !isStreaming}
      onDragStart={startDrag}
      onDragEnd={handleDragEnd}
      data-index={id}
      onClick={handleClick}
      className={cn(
        'p-3 rounded-lg flex items-start gap-2 group relative',
        'border-2 border-black transition-all duration-200',
        type === 'assistant' ? 'bg-gray-100' : 'bg-black text-white ml-4',
        isSelectable || isStreaming ? 'cursor-default' : 'cursor-move',
        isSelectable && 'hover:border-blue-500',
        isSelected && isSelectable && 'border-blue-500 bg-blue-50',
        isSelectable && !isSelected && 'hover:shadow-md',
        !isSelectable && !isStreaming && 'hover:shadow-md hover:scale-[1.01]',
        isStreaming && 'border-blue-300 bg-blue-50'
      )}
    >
      {isSelectable ? (
        <div className={cn(
          'h-5 w-5 rounded-full border-2 flex items-center justify-center flex-shrink-0',
          isSelected ? 'bg-blue-500 border-blue-500' : 'border-gray-400'
        )}>
          {isSelected && <Check className="h-3 w-3 text-white" />}
        </div>
      ) : (
        <Grip className={cn(
          'h-4 w-4 mt-1 flex-shrink-0',
          type === 'assistant' ? 'text-gray-400' : 'text-gray-500',
          isStreaming && 'text-blue-400'
        )} />
      )}

      <div className="flex-1 break-words">
        {content}
        {renderTypingIndicator()}
      </div>

      {!isStreaming && (
        <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity duration-150 flex gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleAddToColorLegend}
            className={cn(
              'h-8 w-8 p-1 rounded-md',
              type === 'assistant'
                ? 'bg-gray-100 hover:bg-gray-200 hover:shadow-sm'
                : 'bg-gray-700 hover:bg-gray-800 hover:shadow-sm'
            )}
            title="Add to Color Legend"
          >
            <span className="text-sm font-bold">+</span>
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopy}
            className={cn(
              'h-8 w-8 p-1 rounded-md',
              type === 'assistant'
                ? 'bg-gray-100 hover:bg-gray-200 hover:shadow-sm'
                : 'bg-gray-700 hover:bg-gray-800 hover:shadow-sm'
            )}
            title="Copy to Clipboard"
          >
            <Copy className="h-5 w-5" />
          </Button>
        </div>
      )}
    </div>
  );
};