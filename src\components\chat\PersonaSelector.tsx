import React from 'react';
import { Select } from '../ui/select';

interface PersonaSelectorProps {
  value: string;
  onChange: (value: string) => void;
}

export const PersonaSelector: React.FC<PersonaSelectorProps> = ({ value, onChange }) => {
  return (
    <Select
      value={value}
      onValueChange={onChange}
      className="w-40 border-2 border-black"
    >
      <option value="writer">Story Writer</option>
      <option value="editor">Story Editor</option>
      <option value="critic">Story Critic</option>
      <option value="character">Character Expert</option>
      <option value="structure">Structure Expert</option>
    </Select>
  );
};