export interface Scene {
  id: number;
  title: string;
  content: string;
  actId: string;
  episodeId?: number;
  storylineId?: string | null;
  episodeId?: number;
  storylineId?: string | null;
}

export interface Act {
  id: string;
  title: string;
  subtitle?: string;
  color: string;
  scenes: Scene[];
  storylineId?: string | null;
  storylineId?: string | null;
}

export interface Episode {
  id: number;
  acts: Act[];
}

export interface DragData {
  type: 'scene-card';
  sceneId: number;
  sourceActId: string;
  sourceEpisodeId: number;
  storylineId?: string | null;
  title: string;
  content: string;
  storylineId?: string | null;
}