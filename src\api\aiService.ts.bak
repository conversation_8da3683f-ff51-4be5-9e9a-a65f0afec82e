import { logger } from '../utils/logger';

// Define the types of responses we expect from AI services
export interface AICompletion {
  id: string;
  content: string;
  model: string;
}

// Interface for streaming response handlers
export interface StreamCallbacks {
  onChunk: (chunk: string) => void;
  onComplete: (finalResponse: AICompletion) => void;
  onError: (error: any) => void;
}

// Configuration using Vite environment variables
// Make sure to use the key from the .env file
const config = {
  openai: {
    // Use the API key directly
    apiKey: '********************************************************************************************************************************************************************',
    endpoint: 'https://api.openai.com/v1/chat/completions'
  }
};

// Log to check if the API key is loaded
console.log('API key configured:', !!config.openai.apiKey);

// Update to a neutral system prompt
const NATURAL_CONVERSATION_PROMPT = `
You are a helpful assistant.
Be conversational, direct, and helpful.
Maintain context through the conversation.
`;

export async function generateCompletion(
  prompt: string, 
  model: string = 'gpt-3.5',
  systemPrompt: string = '',
  previousMessages: Array<{ role: 'user' | 'assistant', content: string }> = []
): Promise<AICompletion> {
  try {
    // Always use OpenAI
    return await callOpenAI(prompt, model, NATURAL_CONVERSATION_PROMPT, previousMessages);
  } catch (error) {
    logger.error('AI completion failed:', error);
    return {
      id: 'error-' + Date.now().toString(),
      content: `I'm sorry, there was an error generating a response. Please try again later.`,
      model: model
    };
  }
}

// For streaming responses
export function generateStreamingCompletion(
  prompt: string,
  model: string = 'gpt-3.5',
  systemPrompt: string = '',
  previousMessages: Array<{ role: 'user' | 'assistant', content: string }> = [],
  callbacks: StreamCallbacks
): void {
  try {
    // Always use OpenAI streaming
    streamFromOpenAI(prompt, model, NATURAL_CONVERSATION_PROMPT, previousMessages, callbacks);
  } catch (error) {
    logger.error('AI streaming completion failed:', error);
    callbacks.onError(error);
  }
}

async function callOpenAI(
  prompt: string,
  model: string = 'gpt-3.5-turbo',
  systemPrompt: string = '',
  previousMessages: Array<{ role: 'user' | 'assistant', content: string }> = []
): Promise<AICompletion> {
  if (!config.openai.apiKey) {
    throw new Error('OpenAI API key not configured');
  }

  const messages = [
    ...(systemPrompt ? [{ role: 'system', content: systemPrompt }] : []),
    ...previousMessages,
    { role: 'user', content: prompt }
  ];

  console.log('OpenAI request details:', { 
    model, 
    messageCount: messages.length,
    apiKey: config.openai.apiKey.substring(0, 10) + '...',
    endpoint: config.openai.endpoint
  });

  try {
    const response = await fetch(config.openai.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.openai.apiKey}`
      },
      body: JSON.stringify({
        model,
        messages,
        temperature: 0.7,
        max_tokens: 2000,
      })
    });

    console.log('OpenAI response status:', response.status, response.statusText);

    if (!response.ok) {
      const error = await response.json();
      console.error('OpenAI API error details:', error);
      throw new Error(`OpenAI API error: ${error.error?.message || response.statusText}`);
    }

    const data = await response.json();
    console.log('OpenAI response received:', { id: data.id, choices: data.choices?.length || 0 });
    
    return {
      id: data.id,
      content: data.choices[0].message.content,
      model
    };
  } catch (error) {
    console.error('OpenAI API call exception:', error);
    throw error;
  }
}

async function streamFromOpenAI(
  prompt: string,
  model: string = 'gpt-3.5-turbo',
  systemPrompt: string = '',
  previousMessages: Array<{ role: 'user' | 'assistant', content: string }> = [],
  callbacks: StreamCallbacks
): Promise<void> {
  if (!config.openai.apiKey) {
    throw new Error('OpenAI API key not configured');
  }

  const messages = [
    ...(systemPrompt ? [{ role: 'system', content: systemPrompt }] : []),
    ...previousMessages,
    { role: 'user', content: prompt }
  ];

  console.log('OpenAI streaming request details:', { 
    model, 
    messageCount: messages.length,
    apiKey: config.openai.apiKey.substring(0, 10) + '...',
    endpoint: config.openai.endpoint
  });

  try {
    const response = await fetch(config.openai.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.openai.apiKey}`
      },
      body: JSON.stringify({
        model,
        messages,
        temperature: 0.7,
        max_tokens: 2000,
        stream: true
      })
    });

    console.log('OpenAI streaming response status:', response.status, response.statusText);

    if (!response.ok) {
      const error = await response.text();
      console.error('OpenAI streaming API error details:', error);
      throw new Error(`OpenAI API error: ${error || response.statusText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('ReadableStream not supported in this browser.');
    }

    console.log('Stream reader obtained, starting to process chunks');
    const decoder = new TextDecoder('utf-8');
    let id = 'stream-' + Date.now().toString();
    let fullContent = '';
    let chunkCount = 0;

    try {
      while (true) {
        const { value, done } = await reader.read();
        if (done) {
          console.log('Stream completed, total chunks:', chunkCount);
          break;
        }
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n').filter(line => line.trim() !== '' && line.trim() !== 'data: [DONE]');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const jsonData = JSON.parse(line.slice(6)); // Remove 'data: ' prefix
              
              if (jsonData.choices && jsonData.choices[0].delta && jsonData.choices[0].delta.content) {
                const contentChunk = jsonData.choices[0].delta.content;
                fullContent += contentChunk;
                callbacks.onChunk(contentChunk);
                chunkCount++;
                
                if (chunkCount % 10 === 0) {
                  console.log(`Processed ${chunkCount} chunks so far`);
                }
              }
              
              // Get the ID from the first chunk
              if (jsonData.id && !id.startsWith('stream-')) {
                id = jsonData.id;
                console.log('Received stream ID:', id);
              }
            } catch (e) {
              console.error('Error parsing JSON in stream:', e, 'Line:', line);
            }
          }
        }
      }

      console.log('Stream processing complete, calling onComplete');
      callbacks.onComplete({
        id,
        content: fullContent,
        model
      });
    } catch (error) {
      console.error('Stream processing error:', error);
      callbacks.onError(error);
    } finally {
      reader.releaseLock();
    }
  } catch (error) {
    console.error('OpenAI streaming API call exception:', error);
    callbacks.onError(error);
  }
} 