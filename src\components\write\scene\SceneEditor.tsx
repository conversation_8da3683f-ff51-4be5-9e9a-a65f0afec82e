import React, { useState } from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { Button } from '../../ui/button';
import { CharacterList } from './editor/CharacterList';
import { EmotionalArc } from './editor/EmotionalArc';
import { SceneBeats } from './editor/SceneBeats';
import { ScriptEditor } from './editor/ScriptEditor';
import { cn } from '../../../lib/utils';
import type { Scene } from '../../../types/write';

interface SceneEditorProps {
  scene: Scene;
  onUpdate: (updates: Partial<Scene>) => void;
}

export const SceneEditor: React.FC<SceneEditorProps & { collapsed?: boolean }> = ({
  scene,
  onUpdate,
  collapsed = false
}) => {
  const [isPlanningCollapsed, setIsPlanningCollapsed] = useState(false);

  return (
    <div className="flex-1 flex min-h-0">
      <div className={cn(
        "border-r-4 border-black flex flex-col transition-all duration-300 min-h-0",
        isPlanningCollapsed ? "w-12" : "w-1/2"
      )} style={{ position: 'relative', zIndex: 25, overflow: 'visible' }}>
        {/* Planning Header */}
        <div 
          className="p-2 bg-gray-50 border-b-4 border-black flex items-center justify-between cursor-pointer"
          onClick={() => setIsPlanningCollapsed(!isPlanningCollapsed)}
        >
          {!isPlanningCollapsed && (
            <span className="font-mono text-sm font-bold">Scene Planning</span>
          )}
          {isPlanningCollapsed ? (
            <ChevronRight className="h-4 w-4 mx-auto" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </div>
        
        {/* Planning Content */}
        {!isPlanningCollapsed && (
          <div className="flex-1 flex flex-col gap-2 min-h-0 p-2 pb-0">
            <CharacterList
              characters={scene.characters}
              onUpdate={(characters) => onUpdate({ characters })}
            />
            <EmotionalArc
              start={scene.emotionalStart}
              end={scene.emotionalEnd}
              progress={scene.emotionalProgress}
              isPositive={scene.isPositiveArc}
              onUpdate={(updates) => onUpdate(updates)}
            />
            <SceneBeats
              beats={scene.beats}
              onUpdate={(beats) => onUpdate({ beats })}
            />
          </div>
        )}
      </div>
      
      {/* Main Editor */}
      <div className="flex-1 flex flex-col min-h-0">
        <div className="flex-1 min-h-0">
          <ScriptEditor
            content={scene.content}
            onUpdate={(content) => onUpdate({ content })}
          />
        </div>
      </div>
    </div>
  );
};