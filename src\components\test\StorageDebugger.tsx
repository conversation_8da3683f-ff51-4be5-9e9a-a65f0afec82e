import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '../ui/button';
import { Card, CardContent, CardHeader } from '../ui/card';
import { findSceneKeys, clearAllSceneData, setBlankTemplatesMode, isBlankTemplatesModeEnabled, analyzeSceneData } from '../../utils/clearSceneData';
import { useProject } from '../../contexts/ProjectContext';
import { Trash2, RefreshCw, FileSearch } from 'lucide-react';

export const StorageDebugger: React.FC = () => {
  const { currentProject } = useProject();
  const [sceneKeys, setSceneKeys] = useState<string[]>([]);
  const [analysis, setAnalysis] = useState<any>(null);
  const [isBlankMode, setIsBlankMode] = useState(false);
  const [isEmptyMode, setIsEmptyMode] = useState(false);
  const [clearCount, setClearCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Load initial data
  useEffect(() => {
    refreshData();
  }, [currentProject?.id]);

  const refreshData = () => {
    setIsLoading(true);
    try {
      // Find scene keys
      const keys = findSceneKeys(currentProject?.id);
      setSceneKeys(keys);
      
      // Check blank templates mode
      setIsBlankMode(isBlankTemplatesModeEnabled());
      
      // Check empty templates mode
      setIsEmptyMode(localStorage.getItem('use_empty_templates') === 'true');
      
      // Analyze scene data
      const analysisResult = analyzeSceneData(currentProject?.id);
      setAnalysis(analysisResult);
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearSceneData = () => {
    if (confirm('Are you sure you want to clear all scene data? This cannot be undone.')) {
      setIsLoading(true);
      try {
        const count = clearAllSceneData(currentProject?.id);
        setClearCount(count);
        refreshData();
      } catch (error) {
        console.error('Error clearing scene data:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const toggleBlankTemplatesMode = () => {
    const newValue = !isBlankMode;
    setBlankTemplatesMode(newValue);
    setIsBlankMode(newValue);
  };

  const toggleEmptyTemplatesMode = () => {
    const newValue = !isEmptyMode;
    localStorage.setItem('use_empty_templates', newValue.toString());
    setIsEmptyMode(newValue);
  };

  const formatBytes = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' bytes';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + ' KB';
    else return (bytes / 1048576).toFixed(2) + ' MB';
  };

  return (
    <Card className="border-4 border-black">
      <CardHeader className="border-b-4 border-black bg-gray-100">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold">Local Storage Debugger</h2>
          <Button 
            variant="ghost" 
            onClick={refreshData}
            disabled={isLoading}
            className="border-2 border-black"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <div className="space-y-6">
          {/* Project Info */}
          <div className="border-2 border-black p-4 rounded">
            <h3 className="font-bold mb-2">Project Information</h3>
            {currentProject ? (
              <div className="space-y-1">
                <p><span className="font-medium">ID:</span> {currentProject.id}</p>
                <p><span className="font-medium">Name:</span> {currentProject.name}</p>
                <p><span className="font-medium">Type:</span> {currentProject.type}</p>
              </div>
            ) : (
              <p className="text-gray-500 italic">No project currently open</p>
            )}
          </div>

          {/* Storage Analysis */}
          <div className="border-2 border-black p-4 rounded">
            <h3 className="font-bold mb-2">Storage Analysis</h3>
            {analysis ? (
              <div className="space-y-1">
                <p><span className="font-medium">Total Keys:</span> {analysis.totalKeys}</p>
                <p><span className="font-medium">Scene Keys:</span> {analysis.sceneKeys}</p>
                <p><span className="font-medium">Episode Data Keys:</span> {analysis.episodeDataKeys}</p>
                <p><span className="font-medium">Total Size:</span> {formatBytes(analysis.totalSize)}</p>
                {analysis.largestKey && (
                  <p><span className="font-medium">Largest Key:</span> {analysis.largestKey.key} ({formatBytes(analysis.largestKey.size)})</p>
                )}
              </div>
            ) : (
              <p className="text-gray-500 italic">No analysis available</p>
            )}
          </div>

          {/* Template Mode */}
          <div className="border-2 border-black p-4 rounded">
            <h3 className="font-bold mb-2">Template Mode</h3>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="blank-templates-mode"
                  checked={isBlankMode}
                  onChange={toggleBlankTemplatesMode}
                  className="h-4 w-4 border-2 border-black"
                />
                <label htmlFor="blank-templates-mode" className="text-sm">
                  Use blank templates (no pre-populated scenes)
                </label>
              </div>
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="empty-templates-mode"
                  checked={isEmptyMode}
                  onChange={toggleEmptyTemplatesMode}
                  className="h-4 w-4 border-2 border-black"
                />
                <label htmlFor="empty-templates-mode" className="text-sm">
                  Use empty scene structures (maintain template framework)
                </label>
              </div>
              <p className="text-sm text-gray-600 border-t border-gray-200 pt-2 mt-2">
                These settings affect newly created episodes only.
                <br />
                <span className="font-medium">Empty scene structures</span> maintain the template framework while keeping scenes empty.
              </p>
            </div>
          </div>

          {/* Actions */}
          <div className="border-2 border-black p-4 rounded bg-gray-50">
            <h3 className="font-bold mb-2">Actions</h3>
            <div className="space-y-4">
              <div>
                <Button
                  onClick={handleClearSceneData}
                  disabled={isLoading || !currentProject}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear All Scene Data
                </Button>
                <p className="text-xs text-gray-500 mt-1">
                  This will remove all scene content from localStorage.
                </p>
              </div>
              
              <div>
                <Button
                  onClick={() => {
                    const keys = sceneKeys.slice(0, 10);
                    const data = keys.map(key => {
                      try {
                        return { key, value: JSON.parse(localStorage.getItem(key) || '{}') };
                      } catch {
                        return { key, value: 'Invalid JSON' };
                      }
                    });
                    console.log('Sample scene data:', data);
                    alert('Sample scene data logged to console');
                  }}
                  disabled={isLoading || sceneKeys.length === 0}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <FileSearch className="h-4 w-4 mr-2" />
                  Log Sample Data to Console
                </Button>
                <p className="text-xs text-gray-500 mt-1">
                  Logs a sample of scene data to the browser console for inspection.
                </p>
              </div>
            </div>
          </div>

          {/* Results */}
          {clearCount > 0 && (
            <div className="border-2 border-green-500 p-4 rounded bg-green-50">
              <p className="text-green-800">
                Successfully cleared {clearCount} items from localStorage.
              </p>
              <p className="text-sm text-gray-600 mt-2">
                Refresh the page to see the changes take effect.
              </p>
            </div>
          )}

          {/* Key List */}
          <div className="border-2 border-black p-4 rounded">
            <h3 className="font-bold mb-2">Scene-Related Keys ({sceneKeys.length})</h3>
            {sceneKeys.length > 0 ? (
              <div className="max-h-40 overflow-y-auto">
                <ul className="text-xs font-mono space-y-1">
                  {sceneKeys.map(key => (
                    <li key={key} className="truncate hover:text-blue-600">
                      {key}
                    </li>
                  ))}
                </ul>
              </div>
            ) : (
              <p className="text-gray-500 italic">No scene-related keys found</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};