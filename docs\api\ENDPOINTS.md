# API Documentation

## Types

```typescript
// Common Types
interface ApiResponse<T> {
  data: T;
  error: null | {
    code: string;
    message: string;
  };
}

// Pagination Types
interface PaginationParams {
  page?: number;
  limit?: number;
}

interface PaginatedResponse<T> {
  data: T[];
  metadata: {
    total: number;
    page: number;
    limit: number;
    hasMore: boolean;
  };
}

// Project Types
interface Project {
  id: string;
  name: string;
  type: 'tv-30' | 'tv-60' | 'film';
  status: 'active' | 'draft' | 'completed';
  template: 'classic' | 'enhanced';
  lastModified: Date;
}

// Workshop Types
interface Story {
  id: string;
  title: string;
  content: string;
  quadrantId: string;
}

interface PlotPoint {
  id: string;
  content: string;
  sectionId: string;
  position: number;
}

// Episode Types
interface Scene {
  id: number;
  title: string;
  content: string;
  actId: string;
  episodeId: number;
  storylineId?: string | null;
}

interface Act {
  id: string;
  title: string;
  subtitle?: string;
  color: string;
  scenes: Scene[];
  storylineId?: string | null;
}

// Timeline Types
interface Storyline {
  id: string;
  title: string;
  color: string;
  episodes: Record<string, boolean>;
}

// Prompt Types
interface Prompt {
  id: string;
  title: string;
  details: string;
  action: string;
  files: PromptFile[];
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

interface PromptFile {
  id: string;
  name: string;
  type: 'pdf' | 'text';
  content: string;
  createdAt: Date;
}

// Idea Types
interface IdeaItem {
  id: string;
  title: string;
  content: string;
  type: 'note' | 'image' | 'voice' | 'link' | 'reference';
  status: 'unprocessed' | 'inuse' | 'archived';
  category: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}
```

## Projects API

### Get Projects
```typescript
GET /api/projects
Response: ApiResponse<Project[]>
```

### Create Project
```typescript
POST /api/projects
Body: {
  name: string;
  type: 'tv-30' | 'tv-60' | 'film';
  template?: 'classic' | 'enhanced';
}
Response: ApiResponse<Project>
```

### Get Project
```typescript
GET /api/projects/:id
Params: { id: string }
Response: ApiResponse<Project>
```

### Update Project
```typescript
PUT /api/projects/:id
Params: { id: string }
Body: Partial<Project>
Response: ApiResponse<Project>
```

### Delete Project
```typescript
DELETE /api/projects/:id
Params: { id: string }
Response: ApiResponse<void>
```

## Workshop API

### Get Stories
```typescript
GET /api/projects/:projectId/workshop/stories
Params: { projectId: string }
Query: PaginationParams & {
  quadrantId?: string;
  search?: string;
}
Response: ApiResponse<PaginatedResponse<Story>>
```

### Create Story
```typescript
POST /api/projects/:projectId/workshop/stories
Params: { projectId: string }
Body: {
  title: string;
  content: string;
  quadrantId: string;
}
Response: ApiResponse<Story>
```

### Update Story
```typescript
PUT /api/projects/:projectId/workshop/stories/:id
Params: { 
  projectId: string;
  id: string;
}
Body: Partial<Story>
Response: ApiResponse<Story>
```

### Delete Story
```typescript
DELETE /api/projects/:projectId/workshop/stories/:id
Params: {
  projectId: string;
  id: string;
}
Response: ApiResponse<void>
```

### Get Plot Points
```typescript
GET /api/projects/:projectId/workshop/plot-points
Params: { projectId: string }
Query: {
  sectionId?: string;
}
Response: ApiResponse<PlotPoint[]>
```

### Create Plot Point
```typescript
POST /api/projects/:projectId/workshop/plot-points
Params: { projectId: string }
Body: {
  content: string;
  sectionId: string;
  position: number;
}
Response: ApiResponse<PlotPoint>
```

## Episodes API

### Get Episodes
```typescript
GET /api/projects/:projectId/episodes
Params: { projectId: string }
Response: ApiResponse<{
  episodes: number[];
  data: Record<number, {
    acts: Act[];
  }>;
}>
```

### Create Episode
```typescript
POST /api/projects/:projectId/episodes
Params: { projectId: string }
Response: ApiResponse<{
  episodeNumber: number;
  data: {
    acts: Act[];
  };
}>
```

### Get Episode Acts
```typescript
GET /api/projects/:projectId/episodes/:episodeId/acts
Params: {
  projectId: string;
  episodeId: string;
}
Response: ApiResponse<Act[]>
```

### Update Act
```typescript
PUT /api/projects/:projectId/episodes/:episodeId/acts/:actId
Params: {
  projectId: string;
  episodeId: string;
  actId: string;
}
Body: Partial<Act>
Response: ApiResponse<Act>
```

### Create Scene
```typescript
POST /api/projects/:projectId/episodes/:episodeId/acts/:actId/scenes
Params: {
  projectId: string;
  episodeId: string;
  actId: string;
}
Body: {
  title: string;
  content: string;
}
Response: ApiResponse<Scene>
```

### Update Scene
```typescript
PUT /api/projects/:projectId/episodes/:episodeId/acts/:actId/scenes/:sceneId
Params: {
  projectId: string;
  episodeId: string;
  actId: string;
  sceneId: string;
}
Body: Partial<Scene>
Response: ApiResponse<Scene>
```

## Timeline API

### Get Storylines
```typescript
GET /api/projects/:projectId/storylines
Params: { projectId: string }
Response: ApiResponse<Storyline[]>
```

### Create Storyline
```typescript
POST /api/projects/:projectId/storylines
Params: { projectId: string }
Body: {
  title: string;
  color: string;
}
Response: ApiResponse<Storyline>
```

### Update Storyline
```typescript
PUT /api/projects/:projectId/storylines/:id
Params: {
  projectId: string;
  id: string;
}
Body: Partial<Storyline>
Response: ApiResponse<Storyline>
```

### Toggle Episode in Storyline
```typescript
POST /api/projects/:projectId/storylines/:id/episodes/:episodeId/toggle
Params: {
  projectId: string;
  id: string;
  episodeId: string;
}
Response: ApiResponse<{
  storylineId: string;
  episodeId: string;
  isActive: boolean;
}>
```

## Prompts API

### Get Prompt Groups
```typescript
GET /api/projects/:projectId/prompts/groups
Params: { projectId: string }
Response: ApiResponse<{
  id: string;
  title: string;
  color: string;
  prompts: Prompt[];
}[]>
```

### Create Prompt
```typescript
POST /api/projects/:projectId/prompts
Params: { projectId: string }
Body: {
  groupId: string;
  title: string;
  details: string;
  action: string;
  tags?: string[];
}
Response: ApiResponse<Prompt>
```

### Update Prompt
```typescript
PUT /api/projects/:projectId/prompts/:id
Params: {
  projectId: string;
  id: string;
}
Body: Partial<Prompt>
Response: ApiResponse<Prompt>
```

### Add File to Prompt
```typescript
POST /api/projects/:projectId/prompts/:id/files
Params: {
  projectId: string;
  id: string;
}
Body: {
  name: string;
  type: 'pdf' | 'text';
  content: string;
}
Response: ApiResponse<PromptFile>
```

## Idea Pile API

### Get Ideas
```typescript
G