import React, { useState, useEffect } from 'react';
import { X, Save, Trash2, CheckSquare, Square } from 'lucide-react';
import { Button } from '../../ui/button';
import { SceneHeader } from './SceneHeader';
import { SceneEditor } from './SceneEditor';
import { SceneNotes } from './SceneNotes';
import { ExportButton } from "./editor/ExportButton";
import { ErrorBoundary } from '../../ErrorBoundary';
import { useSceneState } from '../../../hooks/write/useSceneState';
import { useProject } from '../../../contexts/ProjectContext';
import { useWriteNavigation } from '../../../hooks/write/useWriteNavigation';
import { logger } from '../../../utils/logger';
import { getProjectScopedKey } from '../../../utils/storageUtils';
import { cn } from '../../../lib/utils';

// Fix duplicate import
import { useCallback } from 'react';

interface SceneContentProps {
  sceneId: string;
  onClose: () => void;
}

export const SceneContent: React.FC<SceneContentProps> = ({
  sceneId,
  onClose
}) => {
  const { currentProject } = useProject();
  const projectId = currentProject?.id;
  const { getSceneTitle, updateSceneTitle, importScenes } = useWriteNavigation();
  const {
    scene,
    tags,
    notes,
    isLoading,
    addTag,
    addNote,
    deleteNote,
    updateScene,
    initializeScene,
    deleteScene,
    toggleSceneComplete,
    resolveNote
  } = useSceneState(sceneId);
  
  const [isSaving, setIsSaving] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Create a project-scoped storage key function
  const getStorageKey = useCallback((sceneId: string) => 
    getProjectScopedKey(projectId, `scene_${sceneId}`),
  [projectId]);

  // Always initialize and use a full default scene object
  let effectiveScene = scene;
  useEffect(() => {
    if (!scene && !isLoading) {
      // Parse the scene ID to get the components
      const parts = sceneId.split('-');
      if (parts.length >= 3) {
        const sceneNumber = parts[2];
        const title = getSceneTitle(sceneId);
        initializeScene({
          id: sceneId,
          title: title || 'Untitled Scene',
          content: '',
          notes: [],
          characters: [],
          emotionalStart: '',
          emotionalEnd: '',
          emotionalProgress: 0,
          isPositiveArc: false,
          beats: [],
          isComplete: false,
          revisionState: 'not-started',
          lastEdited: '',
        });
      }
    }
  }, [scene, sceneId, initializeScene, getSceneTitle, isLoading]);
  if (!effectiveScene) {
    effectiveScene = {
      id: sceneId,
      title: getSceneTitle(sceneId) || 'Untitled Scene',
      content: '',
      notes: [],
      characters: [],
      emotionalStart: '',
      emotionalEnd: '',
      emotionalProgress: 0,
      isPositiveArc: false,
      beats: [],
      isComplete: false,
      revisionState: 'not-started',
      lastEdited: '',
    };
  }

  // Find the scene's global order within the episode
  let sceneOrder = 1;
  try {
    const parts = sceneId.split('-');
    if (parts.length >= 3 && currentProject) {
      const episodeId = parseInt(parts[0]);
      const actId = parts[1];
      const sceneNum = parts[2];
      const { episodeData } = require('../../../hooks/useEpisodeState');
      const allScenes = episodeData?.[episodeId]?.acts?.flatMap((act: any) => act.scenes.map((scene: any) => ({ ...scene, actId: act.id })));
      if (allScenes) {
        const idx = allScenes.findIndex((s: any) => `${episodeId}-${s.actId}-${s.id}` === sceneId);
        if (idx !== -1) sceneOrder = idx + 1;
      }
    }
  } catch {}

  const handleSave = () => {
    setIsSaving(true);
    // Simulate save operation
    setTimeout(() => {
      setIsSaving(false);
    }, 500);
  };

  const handleDelete = () => {
    if (showDeleteConfirm) {
      deleteScene();
      onClose();
    } else {
      setShowDeleteConfirm(true);
      // Auto-hide confirmation after 3 seconds
      setTimeout(() => setShowDeleteConfirm(false), 3000);
    }
  };

  try {
    return (
      <div className="flex-1 flex flex-col SceneContent">
        <div className="border-b-4 border-black p-4 bg-gray-50 flex justify-between items-center">
          <SceneHeader
            sceneId={sceneId}
            title={effectiveScene.title}
            tags={tags}
            onAddTag={addTag}
            onClose={onClose}
            onTitleChange={(title) => {
              updateScene({ title });
              updateSceneTitle(sceneId, title);
            }}
            sceneOrder={sceneOrder}
          />
          <div className="flex gap-2 items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleSceneComplete}
              className={cn(
                "border-2 border-black h-8 w-8 p-1",
                effectiveScene.isComplete && "bg-green-100"
              )}
              title={effectiveScene.isComplete ? "Mark as Incomplete" : "Mark as Complete"}
            >
              {effectiveScene.isComplete ? (
                <CheckSquare className="h-4 w-4 text-green-600" />
              ) : (
                <Square className="h-4 w-4" />
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSave}
              className={cn("border-2 border-black h-8 w-8 p-1", isSaving && "bg-green-100")}
              title={isSaving ? "Saving..." : "Save"}
            >
              <Save className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDelete}
              className={cn(
                "border-2 border-black h-8 w-8 p-1",
                showDeleteConfirm && "bg-red-100 text-red-600"
              )}
              title={showDeleteConfirm ? "Confirm Delete" : "Delete"}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex-1 flex">
          <div className="flex-1 flex flex-col">
            <ErrorBoundary
              fallback={
                <div className="flex-1 flex items-center justify-center bg-red-50 p-8">
                  <div className="text-center">
                    <h3 className="text-lg font-bold text-red-700 mb-2">Scene Editor Error</h3>
                    <p className="text-gray-700 mb-4">There was a problem with the scene editor.</p>
                    <button 
                      onClick={onClose}
                      className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                    >
                      Close Scene
                    </button>
                  </div>
                </div>
              }
            >
              <SceneEditor
                scene={effectiveScene}
                onUpdate={updateScene}
              />
            </ErrorBoundary>
          </div>
          <SceneNotes
            notes={notes}
            onAddNote={addNote}
            onDeleteNote={deleteNote}
            onResolveNote={resolveNote}
          />
        </div>
      </div>
    );
  } catch (error) {
    logger.error('Error rendering SceneContent:', error);
    return (
      <div className="flex-1 flex items-center justify-center bg-red-50 p-8">
        <div className="text-center">
          <h3 className="text-lg font-bold text-red-700 mb-2">Rendering Error</h3>
          <p className="text-gray-700 mb-4">There was a problem displaying this scene.</p>
          <button 
            onClick={onClose}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Close Scene
          </button>
        </div>
      </div>
    );
  }
};