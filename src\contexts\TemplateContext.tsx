import React, { createContext, useState } from 'react';

export type TemplateType = 'classic' | 'enhanced';

export interface TemplateContextType {
  template: TemplateType;
  setTemplate: (value: TemplateType) => void;
  toggleTemplate: () => void;
}

// ✅ Use createContext here!
export const TemplateContext = createContext<TemplateContextType | undefined>(undefined);

export const TemplateProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [template, setTemplate] = useState<TemplateType>('enhanced');

  const toggleTemplate = () => {
    setTemplate((prev) => (prev === 'classic' ? 'enhanced' : 'classic'));
  };

  return (
    <TemplateContext.Provider value={{ template, setTemplate, toggleTemplate }}>
      {children}
    </TemplateContext.Provider>
  );
};
