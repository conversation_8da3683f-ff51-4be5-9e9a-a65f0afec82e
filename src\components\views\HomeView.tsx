import React, { useState } from 'react';
import { Plus, FolderOpen } from 'lucide-react';
import { Button } from '../ui/button';
import { useProject } from '../../contexts/ProjectContext';
import { useNavigation } from '../../contexts/NavigationContext';
import type { ViewType } from '../../constants/navigation';
import { ProjectCard } from '../home/<USER>';
import type { Project } from '../../types/project';

interface HomeViewProps {
  onNavigate: (view: ViewType) => void;
}

export const HomeView: React.FC<HomeViewProps> = ({ onNavigate }) => {
  const { setCurrentView } = useNavigation();
  const { projects, createProject, openProject } = useProject();
  const [showNewProjectOptions, setShowNewProjectOptions] = useState(false);

  // Create a new project of a specific type
  const handleCreateProject = (type: 'tv-30' | 'tv-60' | 'film' | 'novel') => {
    const name = prompt('Enter project name:');
    if (name) {
      const project = createProject(name, type);
      openProject(project);
      setCurrentView('episodes');
      onNavigate('episodes');
    }
    setShowNewProjectOptions(false);
  };

  // Handle clicking the new project button - show template options
  const handleNewProjectClick = () => {
    setShowNewProjectOptions(true);
  };

  // Handle opening an existing project
  const handleOpenProject = (project: Project) => {
    openProject(project);
    setCurrentView('episodes');
    onNavigate('episodes');
  };

  return (
    <div className="scale-container">
      <div className="flex flex-col h-full w-full">
        <div className="p-4 overflow-auto w-full" style={{ maxHeight: 'calc(100vh - 4rem)' }}>
          <h1 className="text-2xl font-bold mb-6">Projects</h1>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pb-4 w-full">
            {/* New Project Card */}
            <div className="border rounded-md shadow-sm p-4 flex flex-col gap-4">
              {!showNewProjectOptions ? (
                <Button
                  className="w-full h-full flex flex-col items-center justify-center"
                  variant="outline"
                  onClick={handleNewProjectClick}
                >
                  <Plus className="h-10 w-10 mb-2" />
                  <span>Create New Project</span>
                </Button>
              ) : (
                <div className="flex flex-col gap-3">
                  <h3 className="font-semibold text-lg">Select Project Type</h3>
                  <Button
                    className="w-full"
                    variant="secondary"
                    onClick={() => handleCreateProject('tv-30')}
                  >
                    30-Minute TV Series
                  </Button>
                  <Button
                    className="w-full"
                    variant="secondary"
                    onClick={() => handleCreateProject('tv-60')}
                  >
                    60-Minute TV Series
                  </Button>
                  <Button
                    className="w-full"
                    variant="secondary"
                    onClick={() => handleCreateProject('film')}
                  >
                    Feature Film
                  </Button>
                  <Button
                    className="w-full"
                    variant="secondary"
                    onClick={() => handleCreateProject('novel')}
                  >
                    Novel
                  </Button>
                  <Button
                    className="w-full"
                    variant="outline"
                    onClick={() => setShowNewProjectOptions(false)}
                  >
                    Cancel
                  </Button>
                </div>
              )}
            </div>

            {/* Existing Project Cards */}
            {projects.map(project => (
              <ProjectCard
                key={project.id}
                project={project}
                onClick={() => handleOpenProject(project)}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};