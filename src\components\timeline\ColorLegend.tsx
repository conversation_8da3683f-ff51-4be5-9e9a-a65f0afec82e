import React, { useRef, useEffect } from 'react';
import { Grip, ChevronDown, ChevronUp, Square, X, Minimize2 } from 'lucide-react';
import { Button } from '../ui/button';
import { useEpisodeTrackStore } from '../../store/episodeTrackStore';
import { cn } from '../../lib/utils';

export const ColorLegend: React.FC = () => {
  const { 
    addCard, 
    unassignedCards, 
    colorLegend,
    toggleColorLegendExpanded,
    setColorLegendVisible,
    minimizeColorLegend,
    isColorLegendMinimized
  } = useEpisodeTrackStore();

  const cardListRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when unassignedCards change
  useEffect(() => {
    if (cardListRef.current) {
      cardListRef.current.scrollTop = cardListRef.current.scrollHeight;
    }
  }, [unassignedCards.length]);

  const handleAddWhiteCard = () => {
    const content = prompt('Enter card content:');
    if (content) {
      addCard({
        content,
        source: 'manual',
        color: 'white',
        sectionId: 'manual'
      });
    }
  };

  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation();
    setColorLegendVisible(false);
  };

  const handleMinimize = (e: React.MouseEvent) => {
    e.stopPropagation();
    minimizeColorLegend(!isColorLegendMinimized);
  };

  if (!colorLegend.isVisible) return null;

  // Calculate height based on number of cards
  const cardCount = unassignedCards.length;
  const rowCount = Math.ceil(cardCount / 4); // 4 cards per row
  const baseHeight = 40; // Header height
  const rowHeight = 80; // Height per row of cards
  const maxRows = 2; // Maximum number of visible rows when collapsed
  
  // Calculate collapsed height (header + up to maxRows)
  const collapsedHeight = baseHeight + (Math.min(rowCount, maxRows) * rowHeight);
  
  // Calculate expanded height (all rows)
  const expandedHeight = baseHeight + (rowCount * rowHeight);

  return (
    <div 
      className={cn(
        "fixed bottom-0 left-0 right-0 bg-white border-t-4 border-black",
        "transition-all duration-300 ease-in-out z-50",
        "shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1)]",
        isColorLegendMinimized ? "translate-y-[calc(100%-40px)]" : 
          colorLegend.isExpanded ? "translate-y-0" : `translate-y-[calc(100%-${collapsedHeight}px)]`,
        "hover:shadow-lg"
      )}
      style={{
        height: colorLegend.isExpanded ? 300 : 'auto', // Fixed max height when expanded
        maxHeight: '40vh',
        width: '100%',
        pointerEvents: 'auto'
      }}
    >
      {/* Header - Always visible */}
      <div className="h-10 px-4 flex items-center justify-between bg-white sticky top-0 z-10 cursor-pointer"
           onClick={() => !isColorLegendMinimized && toggleColorLegendExpanded()}>
        <div className="flex items-center gap-2">
          <h3 className="text-xs font-bold">Color Legend</h3>
          {!isColorLegendMinimized && (
            <Button
              variant="ghost"
              size="sm"
              className="h-5 w-5 p-1 hover:bg-gray-100"
            >
              {colorLegend.isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronUp className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>
        <div className="flex items-center gap-2">
          {!isColorLegendMinimized && (
            <Button
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation();
                handleAddWhiteCard();
              }}
              className="border-2 border-black hover:bg-gray-50 h-7 text-xs"
            >
              <Square className="h-3 w-3 mr-1" />
              Add Card
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleMinimize}
            className="h-7 w-7 p-1 hover:bg-gray-100"
            title={isColorLegendMinimized ? "Expand Color Legend" : "Minimize Color Legend"}
          >
            {isColorLegendMinimized ? (
              <ChevronUp className="h-3 w-3" />
            ) : (
              <Minimize2 className="h-3 w-3" />
            )}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            className="h-7 w-7 p-1 hover:bg-gray-100"
            title="Close Color Legend"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Scrollable Content */}
      {!isColorLegendMinimized && (
        <div 
          ref={cardListRef}
          className="overflow-y-auto bg-white" 
          style={{ maxHeight: 'calc(100% - 40px)' }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Unassigned Cards */}
          {unassignedCards.length > 0 && (
            <div className="p-3">
              <h4 className="text-xs font-medium mb-2">Unassigned Cards ({unassignedCards.length})</h4>
              <div className="grid grid-cols-4 gap-3 pb-2">
                {unassignedCards.map((card, index) => (
                  <div 
                    key={`${card.id}-${index}`} // Ensure unique keys by combining id and index
                    draggable
                    onDragStart={(e) => {
                      console.log('Card drag started:', card);
                      e.dataTransfer.setData('application/json', JSON.stringify({
                        type: 'storyline-card',
                        id: card.id,
                        cardId: card.id,
                        content: card.content,
                        sectionId: card.sectionId,
                        source: card.source || 'manual',
                        color: card.color || 'white'
                      }));
                    }}
                    className="flex-shrink-0 p-2 bg-white border-2 border-black rounded cursor-move hover:shadow-md transition-all duration-200 font-bold"
                  >
                    <div className="flex items-center gap-1 mb-1">
                      <Grip className="h-3 w-3 text-gray-400" />
                      <span className="text-xs flex-1 truncate">{card.content}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};