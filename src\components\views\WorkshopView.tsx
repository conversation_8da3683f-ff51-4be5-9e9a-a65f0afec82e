import React, { useState } from 'react';
import { QuadrantColumn } from '../workshop/QuadrantColumn';
import { defaultPlotSections } from '../../constants/plotSections';
import type { Story, PlotPoint, Section } from '../../types/workshop';
import { useTimelineStore } from '../../store/timelineStore'; 

const defaultQuadrants = [
  { id: 'q1', title: 'Quadrant 1' },
  { id: 'q2', title: 'Quadrant 2' },
  { id: 'q3', title: 'Quadrant 3' },
  { id: 'q4', title: 'Quadrant 4' },
];

export const WorkshopView: React.FC = () => {
  const [stories, setStories] = useState<Story[]>([]);
  const [openQuadrants, setOpenQuadrants] = useState(new Set(['q1', 'q2', 'q3', 'q4']));
  const [expandedQuadrant, setExpandedQuadrant] = useState<string | null>(null);
  const [plotPoints, setPlotPoints] = useState<Record<string, Array<PlotPoint>>>({});
  const [plotSections, setPlotSections] = useState<Record<string, Section[]>>({
    q1: [...defaultPlotSections],
    q2: [...defaultPlotSections],
    q3: [...defaultPlotSections],
    q4: [...defaultPlotSections],
  });

  const createPlotPoint = useTimelineStore(state => state.createPlotPoint); // ✅ new centralized creator
  const setUnassignedCards = useTimelineStore.setState;
  const removeZustandCard = useTimelineStore(state => state.removeCard);

  const handleAddStory = (quadrantId: string) => {
    const newStory: Story = {
      id: Date.now().toString(),
      title: 'New Story Idea',
      content: 'Start writing your story idea here...',
      quadrantId,
    };
    setStories([...stories, newStory]);
  };

  const handleDeleteStory = (id: string) => {
    setStories(stories.filter((story) => story.id !== id));
  };

  const handleUpdateStory = (id: string, title: string, content: string) => {
    setStories(
      stories.map((story) =>
        story.id === id ? { ...story, title, content } : story
      )
    );
  };

  const toggleQuadrant = (quadrantId: string) => {
    const newOpen = new Set(openQuadrants);
    if (newOpen.has(quadrantId)) {
      newOpen.delete(quadrantId);
    } else {
      newOpen.add(quadrantId);
    }
    setOpenQuadrants(newOpen);
  };

  const handleAddPlotPoint = (quadrantId: string, sectionId: string) => {
    const id = createPlotPoint({
      storylineId: sectionId,
      position: 0,
      content: 'New plot point...',
      color: 'white'
    });

    const newPlotPoint = {
      id,
      content: 'New plot point...',
      sectionId,
      position: 0,
    };

    setPlotPoints(prev => ({
      ...prev,
      [quadrantId]: [...(prev[quadrantId] || []), newPlotPoint]
    }));
  };

  const handleUpdatePlotPoint = (quadrantId: string, pointId: string, content: string) => {
    setPlotPoints(prev => ({
      ...prev,
      [quadrantId]: prev[quadrantId].map(point => 
        point.id === pointId ? { ...point, content } : point
      )
    }));

    setUnassignedCards(state => ({
      unassignedCards: state.unassignedCards.map(card =>
        card.id === pointId ? { ...card, content } : card
      )
    }));
  };

  const handleDeletePlotPoint = (quadrantId: string, pointId: string) => {
    setPlotPoints(prev => ({
      ...prev,
      [quadrantId]: prev[quadrantId].filter(point => point.id !== pointId)
    }));

    removeZustandCard(pointId);
  };

  const handleUpdateSection = (quadrantId: string, sectionId: string, updates: Partial<Section>) => {
    setPlotSections(prev => ({
      ...prev,
      [quadrantId]: prev[quadrantId].map(section =>
        section.id === sectionId ? { ...section, ...updates } : section
      )
    }));
  };

  return (
    <div className="h-screen flex">
      <div className="flex-1 flex">
        {defaultQuadrants.map((quadrant) => (
          <QuadrantColumn
            style={{ paddingTop: "20px" }}
            key={quadrant.id}
            id={quadrant.id}
            title={quadrant.title}
            stories={stories.filter((s) => s.quadrantId === quadrant.id)}
            plotSections={plotSections[quadrant.id]}
            plotPoints={plotPoints[quadrant.id] || []}
            isOpen={openQuadrants.has(quadrant.id)}
            isExpanded={expandedQuadrant === quadrant.id}
            onToggle={() => toggleQuadrant(quadrant.id)}
            onExpand={() => setExpandedQuadrant(quadrant.id)}
            onCollapse={() => setExpandedQuadrant(null)}
            onAddStory={handleAddStory}
            onDeleteStory={handleDeleteStory}
            onUpdateStory={handleUpdateStory}
            onAddPlotPoint={(sectionId) => handleAddPlotPoint(quadrant.id, sectionId)}
            onUpdatePlotPoint={(id, content) => handleUpdatePlotPoint(quadrant.id, id, content)}
            onDeletePlotPoint={(id) => handleDeletePlotPoint(quadrant.id, id)}
            onUpdateSection={(sectionId, updates) =>
              handleUpdateSection(quadrant.id, sectionId, updates)
            }
          />
        ))}
      </div>
    </div>
  );
};
