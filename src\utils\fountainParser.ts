// TODO: Replace with actual implementation when @nyousefi/fountain is working
// import { parse } from '@nyousefi/fountain';
import { logger } from './logger';
import type { Scene, Character } from '../types/write';

/**
 * Represents a scene parsed from Fountain format
 */
export interface ParsedScene {
  heading: string;
  content: string;
  scene_heading?: string;
  scene_content?: string;
}

/**
 * Parse Fountain formatted text into scenes
 * @param text The Fountain formatted text
 * @returns Array of parsed scenes
 */
export function parseScenes(text: string): ParsedScene[] {
  logger.debug('Parsing Fountain text...');
  
  // First, normalize line endings
  const normalizedText = text.replace(/\r\n/g, '\n');
  
  // Identify scene headings
  const sceneHeadingRegex = /^(INT|EXT|INT\/EXT|EXT\/INT|INT\.\/EXT\.|EXT\.\/INT\.|INT \/ EXT|EXT \/ INT|INT\.|EXT\.)[.\s].+$/gmi;
  
  // Find all scene heading positions
  const headingMatches: { index: number, heading: string }[] = [];
  let match;
  while ((match = sceneHeadingRegex.exec(normalizedText)) !== null) {
    headingMatches.push({
      index: match.index,
      heading: match[0].trim()
    });
  }
  
  // Split text into scene blocks based on heading positions
  const scenes: ParsedScene[] = [];
  for (let i = 0; i < headingMatches.length; i++) {
    const currentHeading = headingMatches[i];
    const nextHeading = headingMatches[i + 1];
    
    const sceneStart = currentHeading.index;
    const sceneEnd = nextHeading ? nextHeading.index : normalizedText.length;
    
    // Extract the full scene text
    const sceneText = normalizedText.substring(sceneStart, sceneEnd).trim();
    
    // Split into heading and content
    const lines = sceneText.split('\n');
    const heading = lines[0].trim();
    
    // Content is everything after the heading
    const content = lines.slice(1).join('\n').trim();
    
    // Add scene to results
    scenes.push({
      heading,
      content,
      scene_heading: heading,
      scene_content: content
    });
    
    // Log the first few characters of content for debugging
    if (i < 3) {
      logger.debug(`Scene ${i+1} content preview:`, content.substring(0, 50) + '...');
    }
  }
  
  logger.debug(`Found ${scenes.length} scene headings in Fountain text`);
  
  // Show warning if no scenes found
  if (scenes.length === 0) {
    logger.warn('No scenes found in Fountain text - check format');
  } else {
    // Log first few scenes for debugging
    scenes.slice(0, 3).forEach((scene, i) => {
      logger.debug(`Parsed scene ${i+1}:`, { 
        heading: scene.heading,
        contentStart: scene.content.substring(0, 50) + '...',
        contentLength: scene.content.length
      });
    });
  }
  
  return scenes;
}

/**
 * Extracts character names from screenplay content
 * @param content The screenplay content text
 * @returns Array of unique character names
 */
export function extractCharactersFromContent(content: string): string[] {
  if (!content) return [];
  
  try {
    // Characters in Fountain format are in ALL CAPS and on their own line
    // Look for lines that are just a name in uppercase (might end with a continuation like "(CONT'D)")
    const characterLineRegex = /^([A-Z][A-Z\s.'-]+)(\s*\(.*\))?$/gm;
    
    // Also find character cues in dialogue (names in parentheses)
    const characterCueRegex = /\(([A-Z][A-Za-z\s.'-]+)\)/g;
    
    const lines = content.split('\n');
    const characters = new Set<string>();
    
    // Process each line for character names
    lines.forEach(line => {
      const trimmedLine = line.trim();
      
      // Skip empty lines and common non-character lines
      if (!trimmedLine || /^(to|with|and|or|at|from|by|through)$/i.test(trimmedLine)) {
        return;
      }
      
      // Check for character speaking lines
      const characterMatch = trimmedLine.match(/^([A-Z][A-Z\s.'-]+)(\s*\(.*\))?$/);
      if (characterMatch) {
        // Clean up the name (remove any continuation markers)
        const name = characterMatch[1].trim().replace(/\(CONT'D\)|\(cont'd\)|\(CONTINUING\)/i, '');
        
        // Ignore common false positives
        if (
          // Scene headings
          !/^(INT|EXT|INT\/EXT|I\/E|CUT TO|FADE IN|FADE OUT|DISSOLVE TO|BACK TO|END|THE END|FLASHBACK|INTERCUT|TITLE|SUPER)$/i.test(name) &&
          
          // Exclude scene headings with locations
          !/^(INT|EXT|INT\/EXT|I\/E)\./.test(name) &&
          
          // Exclude camera directions
          !/^(ANGLE ON|CLOSE ON|CLOSE UP|CONTINUOUS|DAY|NIGHT|DAWN|DUSK|MORNING|EVENING|LATER|MOMENTS LATER|SAME TIME|O\.S\.|V\.O\.|OFF SCREEN|VOICE OVER)$/.test(name) &&
          
          // Exclude transitions
          !/TO:$/.test(name) &&
          
          // Must be longer than 1 character
          name.length > 1
        ) {
          characters.add(name);
        }
      }
      
      // Check for character cues
      const cueMatches = [...trimmedLine.matchAll(characterCueRegex)];
      if (cueMatches.length > 0) {
        cueMatches.forEach(match => {
          const name = match[1].trim();
          // Exclude camera/acting directions
          if (
            name && 
            name.length > 1 && 
            !/^(O\.S\.|V\.O\.|off screen|voice over|cont'd|whispers|laughs|calling|shouting|quietly|angrily)$/i.test(name)
          ) {
            characters.add(name);
          }
        });
      }
    });
    
    // Convert Set to Array and sort alphabetically
    return Array.from(characters).sort();
  } catch (error) {
    logger.error('Failed to extract characters from content:', error);
    return [];
  }
}

/**
 * Converts parsed scenes to app scene format
 * @param parsedScenes Array of parsed scenes
 * @param actId The ID of the act to assign scenes to
 * @param episodeId The episode ID
 * @returns Array of app scenes
 */
export function convertToAppScenes(
  parsedScenes: ParsedScene[], 
  actId: string,
  episodeId: number
): Scene[] {
  return parsedScenes.map((scene, index) => {
    // Use deterministic integer ID
    const id = index + 1;
    
    // Since we now include the heading in content from parseScenes,
    // we no longer need to ensure the heading is at the top
    const content = scene.content;
    
    // Extract a title from the heading
    const title = extractTitleFromHeading(scene.heading) || `Scene ${index + 1}`;
    
    // Convert extracted character names to Character objects
    const characters: Character[] = (scene.characters || []).map((name, charIndex) => ({
      id: `char-${index}-${charIndex}`,
      name,
      isActive: true
    }));
    
    // Debug log the content and characters being added to the scene
    logger.debug(`Converting scene ${id}: title="${title}", content length=${content.length}, characters=${characters.length}`);
    if (characters.length > 0) {
      logger.debug(`Character names in scene ${id}: ${characters.map(c => c.name).join(', ')}`);
    }
    
    const appScene = {
      id,
      title,
      content,
      notes: [],
      characters,
      emotionalStart: 'Neutral',
      emotionalEnd: 'Neutral',
      emotionalProgress: 50,
      isPositiveArc: true,
      beats: [],
      isComplete: false,
      lastEdited: new Date().toISOString()
    };
    return appScene;
  });
}

/**
 * Extracts a title from a scene heading
 * @param heading The scene heading (e.g., "INT. LIVING ROOM - DAY")
 * @returns A simplified title
 */
function extractTitleFromHeading(heading: string): string {
  // Remove INT./EXT. prefix
  let title = heading.replace(/^(INT|EXT|INT\/EXT|I\/E)[.\s]+/i, '');
  
  // Remove time of day suffix
  title = title.replace(/\s+[-–—]\s+.*$/, '');
  
  // Capitalize first letter of each word
  title = title
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
  
  return title.trim();
}

/**
 * Converts app scenes back to Fountain format
 * @param scenes Array of app scenes
 * @returns Fountain formatted text
 */
export function scenesToFountain(scenes: Scene[]): string {
  return scenes
    .map(scene => {
      // Create a basic scene with heading if not already formatted
      const hasHeading = scene.content.match(/^(INT|EXT|INT\/EXT|I\/E)[.\s]+/i);
      return hasHeading ? scene.content : `INT. ${scene.title.toUpperCase()} - DAY\n\n${scene.content}`;
    })
    .join('\n\n');
}