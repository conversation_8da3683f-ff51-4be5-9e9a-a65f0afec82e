import React from 'react';
import { <PERSON>, <PERSON>, Type, ZoomIn, ZoomOut, Search, Maximize, Contrast } from 'lucide-react';
import { Button } from './button';
import { useTheme } from '../../contexts/ThemeContext';
import { cn } from '../../lib/utils';

interface ThemeToggleProps {
  className?: string;
}

export function ThemeToggle({ className }: ThemeToggleProps) {
  const {
    theme,
    toggleMode,
    toggleFontSize,
    toggleHighContrast,
  } = useTheme();

  const getFontSizeIcon = () => {
    switch (theme.fontSize) {
      case 'small':
        return <Search className="h-4 w-4" />;
      case 'normal':
        return <Type className="h-4 w-4" />;
      case 'large':
        return <ZoomIn className="h-4 w-4" />;
      default:
        return <Type className="h-4 w-4" />;
    }
  };

  const getFontSizeTitle = () => {
    switch (theme.fontSize) {
      case 'small':
        return 'Compact View (smallest)';
      case 'normal':
        return 'Normal View';
      case 'large':
        return 'Large View';
      default:
        return 'Toggle font size';
    }
  };

  return (
    <div className={cn("flex items-center gap-2", className)}>
      {/* Theme toggle */}
      <Button
        variant="ghost"
        size="sm"
        onClick={toggleMode}
        title={theme.mode === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}
        className="h-8 w-8 p-1 rounded-full"
      >
        {theme.mode === 'light' ? (
          <Moon className="h-4 w-4" />
        ) : (
          <Sun className="h-4 w-4" />
        )}
      </Button>

      {/* Font size toggle */}
      <Button
        variant="ghost"
        size="sm"
        onClick={toggleFontSize}
        title={getFontSizeTitle()}
        className="h-8 w-8 p-1 rounded-full"
      >
        {getFontSizeIcon()}
      </Button>

      {/* High contrast toggle */}
      <Button
        variant="ghost"
        size="sm"
        onClick={toggleHighContrast}
        title={theme.highContrast ? 'Normal contrast' : 'High contrast'}
        className="h-8 w-8 p-1 rounded-full"
      >
        <Contrast className="h-4 w-4" />
      </Button>
    </div>
  );
}