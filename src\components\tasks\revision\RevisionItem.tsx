import React, { useState, useMemo } from 'react';
import { useRevisionStore } from '../../../store/revisionStore';
import { SceneSelectorModal } from './SceneSelectorModal';
import { Button } from '../../ui/button';
import { Edit, Trash, SendHorizontal, Check, X } from 'lucide-react';
import type { RevisionNote } from '../../../types/task';
import { RevisionNoteEditor } from './RevisionNoteEditor';
import { useEpisodeState } from '../../../hooks/useEpisodeState';
import { CollaboratorComponent } from './CollaboratorComponent';

interface RevisionItemProps {
  note: RevisionNote;
}

export const RevisionItem: React.FC<RevisionItemProps> = ({ note }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [selectedText, setSelectedText] = useState<string>('');
  const { updateNote, deleteNote, markAsProcessed } = useRevisionStore();
  const { episodeData } = useEpisodeState();
  
  const handleUpdateNote = (content: string) => {
    updateNote(note.id, { content });
    setIsEditing(false);
  };
  
  const handleSendToScene = (sceneId: string, noteType: string, assigneeId?: string) => {
    const revisionStore = useRevisionStore.getState();
    // Send only the selected text if there is any, otherwise send the entire note
    const contentToSend = selectedText.trim() ? selectedText.trim() : note.content;
    revisionStore.sendToScene(note.id, sceneId, noteType, contentToSend, assigneeId);
    setIsSending(false);
    setSelectedText('');
  };

  // Handle text selection
  const handleMouseUp = () => {
    const selection = window.getSelection();
    if (selection && selection.toString().trim()) {
      setSelectedText(selection.toString());
    }
  };

  // Parse scene info when a scene ID is available
  const sceneInfo = useMemo(() => {
    if (!note.sceneId) return null;
    
    // Parse the scene ID format: episodeId-actId-sceneId
    const [episodeId, actId, sceneId] = note.sceneId.split('-');
    
    if (!episodeId || !actId || !sceneId || !episodeData[Number(episodeId)]) {
      return { episodeLabel: 'Unknown', actLabel: 'Unknown', sceneLabel: 'Unknown' };
    }
    
    const episode = episodeData[Number(episodeId)];
    const act = episode.acts.find(a => a.id === actId);
    const scene = act?.scenes.find(s => s.id.toString() === sceneId);
    
    return {
      episodeLabel: `Episode ${episodeId}`,
      actLabel: act?.title || 'Unknown Act',
      sceneLabel: scene?.title || 'Unknown Scene'
    };
  }, [note.sceneId, episodeData]);
  
  return (
    <div className={`border-4 border-black p-4 ${note.isProcessed ? 'bg-gray-100' : 'bg-white'}`}>
      {isEditing ? (
        <RevisionNoteEditor
          initialContent={note.content}
          onSave={handleUpdateNote}
          onCancel={() => setIsEditing(false)}
        />
      ) : (
        <>
          <div className="flex justify-between mb-2">
            <div className="flex items-center">
              <button
                onClick={() => markAsProcessed(note.id, !note.isProcessed)}
                className="mr-2"
              >
                {note.isProcessed ? (
                  <Check className="h-5 w-5 text-green-500" />
                ) : (
                  <X className="h-5 w-5 text-gray-300" />
                )}
              </button>
              <span className={note.isProcessed ? 'line-through text-gray-500' : ''}>
                {new Date(note.createdAt).toLocaleDateString()}
              </span>
            </div>
            <div className="flex gap-2">
              <Button size="sm" variant="ghost" onClick={() => setIsEditing(true)}>
                <Edit className="h-4 w-4" />
              </Button>
              <Button 
                size="sm" 
                variant="ghost" 
                onClick={() => {
                  // Clear any previous selection before opening send modal
                  const selection = window.getSelection();
                  if (selection) {
                    const hasSelection = selection.toString().trim() !== '';
                    if (hasSelection) {
                      setSelectedText(selection.toString());
                    } else {
                      setSelectedText('');
                    }
                  }
                  setIsSending(true);
                }}
              >
                <SendHorizontal className="h-4 w-4" />
              </Button>
              <Button size="sm" variant="ghost" onClick={() => deleteNote(note.id)}>
                <Trash className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          <div 
            className={`mt-2 whitespace-pre-wrap ${note.isProcessed ? 'text-gray-500' : ''}`}
            onMouseUp={handleMouseUp}
          >
            {note.content}
          </div>
          
          {/* Add CollaboratorComponent */}
          <CollaboratorComponent 
            noteId={note.id} 
            collaborators={note.collaborators || []} 
          />
          
          {selectedText && (
            <div className="mt-2 p-2 bg-yellow-50 border-l-4 border-yellow-500">
              <div className="text-xs font-bold text-yellow-800">Selected Text:</div>
              <div className="text-sm">{selectedText}</div>
              <div className="flex justify-end mt-1">
                <Button 
                  size="sm" 
                  variant="outline"
                  className="text-xs h-6"
                  onClick={() => setSelectedText('')}
                >
                  Clear Selection
                </Button>
              </div>
            </div>
          )}
          
          {note.sceneId && sceneInfo && (
            <div className="mt-4 p-2 bg-gray-100 border-l-4 border-blue-500">
              <div className="text-sm font-bold text-blue-800">Sent to:</div>
              <div className="text-sm">
                <span className="font-bold">{sceneInfo.episodeLabel}</span> &rsaquo; <span>{sceneInfo.actLabel}</span> &rsaquo; <span>{sceneInfo.sceneLabel}</span>
              </div>
              <div className="text-xs mt-1 text-gray-500">
                Note type: <span className="font-medium">{note.noteType}</span>
              </div>
            </div>
          )}
        </>
      )}
      
      {isSending && (
        <SceneSelectorModal
          onSelect={handleSendToScene}
          onCancel={() => {
            setIsSending(false);
            // Don't clear selection yet - keep it in case they want to try again
          }}
          selectedText={selectedText}
        />
      )}
    </div>
  );
}; 