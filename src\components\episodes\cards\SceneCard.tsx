import React from 'react';
import { <PERSON><PERSON>, <PERSON>ting<PERSON>, <PERSON>, X } from 'lucide-react'; // Added Settings, Link. Edit was a typo, not an icon.
import { Button } from '../../ui/button';
import { cn } from '../../../lib/utils';
import type { Scene } from '../../../types/episode';

interface SceneCardProps {
  scene: Scene;
  onUpdate: (updates: Partial<Scene>) => void;
  onDelete: () => void;
}

export const SceneCard: React.FC<SceneCardProps> = ({
  scene,
  onUpdate,
  onDelete,
}) => {
  const [isEditing, setIsEditing] = React.useState(false);
  const [editContent, setEditContent] = React.useState(scene.content || '');

  const handleSave = () => {
    if (editContent.trim() !== scene.content) {
      onUpdate({ content: editContent.trim() });
    }
    setIsEditing(false);
  };

  return (
    <div
      draggable
      className={cn(
        "group relative bg-white border-4 border-black p-3", // Adjusted padding
        "hover:-translate-y-1 hover:shadow-md",
        "transition-all duration-200 cursor-move"
        // Removed 'group' class as controls are now always visible, not hover-dependent for opacity
      )}
    >
<<<<<<< HEAD
      {/* Scene Controls: Positioned top-right, always visible. */}
      <div className="scene-controls absolute top-2 right-2 flex gap-1">
        <Button variant="ghost" size="icon" title="Edit scene" onClick={() => console.log('Edit scene clicked:', scene.id)} className="h-7 w-7">
          <Settings className="h-5 w-5" />
        </Button>
        <Button variant="ghost" size="icon" title="Open right view" onClick={() => console.log('Open right view for scene:', scene.id)} className="h-7 w-7">
          <Link className="h-5 w-5" />
        </Button>
        <Button variant="ghost" size="icon" title="Delete scene" onClick={onDelete} className="h-7 w-7">
          <X className="h-5 w-5" />
        </Button>
      </div>

      <div className="flex items-start gap-3"> {/* Increased gap */}
        <Grip className="h-6 w-6 text-gray-400 mt-0.5 flex-shrink-0" /> {/* Increased size, adjusted margin top */}
        
        {isEditing ? (
          <textarea
            value={editContent}
            onChange={(e) => setEditContent(e.target.value)}
            onBlur={handleSave}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSave();
              }
              if (e.key === 'Escape') {
                setEditContent(scene.content || '');
                setIsEditing(false);
              }
            }}
            className={cn(
              "w-full p-1 border-2 border-black font-mono text-sm resize-none", // Adjusted padding
              "focus:outline-none focus:ring-2 focus:ring-black"
            )}
            autoFocus
            rows={2} // Suggest 2 rows for editing consistency with display
          />
        ) : (
          <div
            onClick={() => setIsEditing(true)}
            className="flex-1 font-mono text-sm cursor-text min-w-0 pr-2 line-clamp-2" // Added line-clamp and padding-right
            title={scene.content} // Show full content on hover
          >
            {scene.content || <span className="text-gray-400">Click to add content...</span>}
          </div>
        )}
        {/* The old X button that was directly in the flex layout is now removed */}
=======
      <div className="flex items-start gap-2">
        <Grip className="h-5 w-5 text-gray-400 mt-1 flex-shrink-0" />
        <div className="flex-1">
          {isEditing ? (
            <textarea
              value={editContent}
              onChange={(e) => setEditContent(e.target.value)}
              onBlur={handleSave}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSave();
                }
                if (e.key === 'Escape') {
                  setEditContent(scene.content || '');
                  setIsEditing(false);
                }
              }}
              className={cn(
                "w-full p-2 border-2 border-black font-mono text-sm resize-none",
                "focus:outline-none focus:ring-2 focus:ring-black"
              )}
              autoFocus
            />
          ) : (
            <div 
              onClick={() => setIsEditing(true)}
              className="font-mono text-sm cursor-text min-w-0"
            >
              {scene.content || <span className="text-gray-400">Click to add content...</span>}
            </div>
          )}
        </div>
        <div className="flex flex-col gap-1 ml-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsEditing(true)}
            className="h-6 w-6 p-1"
            aria-label="Edit Scene"
          >
            <Edit className="h-5 w-5" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onDelete}
            className="h-6 w-6 p-1"
            aria-label="Delete Scene"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
>>>>>>> main
      </div>
    </div>
  );
};